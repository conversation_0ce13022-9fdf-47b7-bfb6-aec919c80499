package test

import (
	"fmt"
	"loop/pkg/subtitle"
	"testing"
)

func TestSubtitleAutoTime(t *testing.T) {
	// resp, _ := subtitle.SearchSubtitleAuto("We.Bare.Bears")
	resp, _ := subtitle.SearchSubtitleAuto("We.Bare.Bears.S03E01.Grizzly.the.Movie")
	for t := range resp.Subtitles {
		fmt.Printf("resp.Subtitles[t]: %v\n", resp.Subtitles[t])
	}
	fmt.Printf("resp.SubtitleDownloadLink: %v\n", resp.SubtitleDownloadLink)
	// fmt.Printf("resp: %v\n", resp)
}
