package test

import (
	"fmt"
	"testing"
	"time"
)

func TestTime(t *testing.T) {
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		fmt.Println("err", err)
		return
	}

	now := time.Now().In(location)
	year, month, day := now.Date()
	fmt.Printf("now: %v\n", now)
	fmt.Printf("The current date in China is %d-%02d-%02d\n", year, month, day)
}
func TestTime2(t *testing.T) {
	endDate, err := time.Parse("2006-01-02 15:04:05", "2028-12-31 23:59:59")
	if err != nil {
		fmt.Println("err", err)
		return
	}
	fmt.Println("endDate", endDate)
}
