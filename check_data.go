package main

import (
	"fmt"
	"log"
	"os"

	"gopkg.in/yaml.v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"loop/internal/config"
	"loop/internal/model"
)

func main() {
	// 加载配置
	configPaths := []string{
		"../../configs/config.yaml",
		"../configs/config.yaml", 
		"configs/config.yaml",
	}

	var configFile string
	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			configFile = path
			break
		}
	}

	if configFile == "" {
		log.Fatalf("config file not found")
	}

	cfg := &config.Config{}
	yamlFile, err := os.ReadFile(configFile)
	if err != nil {
		log.Fatalf("failed to read config file: %v", err)
	}

	if err = yaml.Unmarshal(yamlFile, cfg); err != nil {
		log.Fatalf("failed to unmarshal config: %v", err)
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(cfg.DbConfig.Url), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 检查各个表的数据
	var count int64

	// 检查角色
	if err := db.Model(&model.SysRole{}).Count(&count).Error; err != nil {
		log.Printf("Error counting roles: %v", err)
	} else {
		fmt.Printf("SysRole count: %d\n", count)
	}

	// 检查用户
	if err := db.Model(&model.SysUser{}).Count(&count).Error; err != nil {
		log.Printf("Error counting users: %v", err)
	} else {
		fmt.Printf("SysUser count: %d\n", count)
	}

	// 检查分类
	if err := db.Model(&model.Category{}).Count(&count).Error; err != nil {
		log.Printf("Error counting categories: %v", err)
	} else {
		fmt.Printf("Category count: %d\n", count)
	}

	// 检查VIP
	if err := db.Model(&model.VIP{}).Count(&count).Error; err != nil {
		log.Printf("Error counting VIPs: %v", err)
	} else {
		fmt.Printf("VIP count: %d\n", count)
	}

	fmt.Println("数据检查完成")
}
