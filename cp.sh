#!/bin/bash

# 定义目标服务器信息
REMOTE_DIR="/root/loop"
ARCHIVE_NAME="loop.zip"


# 检查是否传递了 -f 参数
FORCE_FLAG=""
while getopts "f" opt; do
    case $opt in
        f)
            FORCE_FLAG="-f"
            ;;
        *)
            echo "Usage: $0 [-f]"
            exit 1
            ;;
    esac
done

# 创建 loop 目录并复制文件
mkdir -p loop
zip -r $ARCHIVE_NAME . -x '*.git*'
rm -rf loop

# 复制 loop 到服务器
sshpass -p 'Yongjun0118*' scp  $ARCHIVE_NAME  root@*************:~

# 删除本地压缩包
rm $ARCHIVE_NAME

# 在服务器上执行命令
# ... existing code ...

# 在服务器上执行命令
sshpass -p 'Yongjun0118*' ssh -tt root@************* << EOF
  # 强制删除旧目录
  rm -rf ${REMOTE_DIR} loop
  # 创建并解压新版本
  mkdir -p ${REMOTE_DIR}
  unzip ~/$ARCHIVE_NAME -d ${REMOTE_DIR}
  rm -rf ~/$ARCHIVE_NAME
  cd ${REMOTE_DIR}
EOF

echo "文件传输完成并部署到服务器。"