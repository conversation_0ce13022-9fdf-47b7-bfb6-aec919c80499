# LSENGLISH

## 工具

数据库：Sequel Ace

## mod

```bash
go mod init loop
export GOPROXY=http://mirrors.aliyun.com/goproxy/
go mod tidy
go run main.go // 自动安装

# 使用wire后要这样执行
cd cmd
go run .

docker system prune -a

goctl docker -go cmd/main.go
docker build -t loop:v1 -f Dockerfile .

# 创建mysql
# 进入deploy/mysql
docker-compose -f docker-compose.yaml up -d


mysqlsh --uri mysql://root@127.0.0.1:3306


# 多语言
go run toml.go
```

````
为了让 Loki 通过 Promtail 收集 `/root/aa.log` 这个日志文件，你需要在 `promtail-config.yaml` 文件和 `docker-compose.yml` 文件中做相应的调整。

首先，你需要在 `docker-compose.yml` 文件中的 Promtail 服务部分添加一个新的 volume，将 `/root/aa.log` 文件映射到容器内部（例如映射到 `/var/log/aa.log`）。如下所示：

```yaml
  promtail:
    image: grafana/promtail:2.9.0
    volumes:
      - /root/aa.log:/var/log/aa.log
      - ./promtail-config.yaml:/etc/promtail/config.yml
    command: -config.file=/etc/promtail/config.yml
    networks:
      - loki
````

然后，你需要在 Promtail 的配置文件 `promtail-config.yaml` 中添加一个新的 `scrape_config`，用于收集 `/var/log/aa.log` 这个文件的日志。如下所示：

```yaml
scrape_configs:
  - job_name: myapp
    static_configs:
      - targets:
          - localhost
        labels:
          job: myapplogs
          __path__: /var/log/aa.log
```

这里，我们添加了一个名为 `myapp` 的作业，它从容器内部的 `/var/log/aa.log` 文件收集日志。你可以根据需要修改 `job_name` 和 `labels`。

现在，Promtail 应该能够收集 `/root/aa.log` 文件的日志，并将它们发送到 Loki 了。你可以在 Grafana 中创建一个新的仪表板，然后添加一个日志面板来查看这些日志。在查询日志时，你可以使用 `{job="myapplogs"}` 作为查询表达式，这将显示 `myapp` 作业收集的日志。

## 部署

```sh
./deploy.sh

docker logs loop

mysql -u root -p
```

```
# 执行步骤
# 创建虚拟环境 python3 -m venv myenv
# source myenv/bin/activate
# pip install pandas openpyxl
# python auto_lang.py
# 退出虚拟环境 deactivate
```


https://dict.eudic.net/webting/desktopplay?id=6b6a53a7-ddc4-46dd-b8bc-8e2dbdba1fd6&token=QYN+eyJ0b2tlbiI6IiIsInVzZXJpZCI6IiIsInVybHNpZ24iOiJ3TEdrelNERTB2NEd3emZGbTkrQ3kwL2JPZWs9IiwidCI6IkFCSU1UYzBPVE01T0RJeU1BPT0ifQ%3D%3D

https://api.frdic.com/api/v3/media/mp3/6b6a53a7-ddc4-46dd-b8bc-8e2dbdba1fd6?type=mp4

https://static.esdict.cn/VideoPool/6b6a53a7-ddc4-46dd-b8bc-8e2dbdba1fd6/index.png?stamp=1676866941899