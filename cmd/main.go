package main

import (
	"flag"
	"loop/internal/app"
	"loop/internal/config"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

var mode string = "debug"

func newApp(hs *http.Server, db *gorm.DB, rdb *redis.Client) *app.App {
	return app.New(hs, db, rdb)
}

func main() {
	gin.SetMode(mode)
	flag.Parse()
	c := config.Setup()

	app, cleanup, err := wireApp(c)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}

// package main

// import (
// 	"fmt"
// 	"os/exec"
// 	"strings"
// )

// // 获取视频下载链接和指定语言的字幕下载链接
// func getYtDlpVideoAndSubtitleLinks(videoURL, lang, containerName string) (videoLink, subtitleLink string, err error) {
// 	// 获取视频下载链接
// 	videoCmd := exec.Command("docker", "exec", containerName, "yt-dlp", "--get-url", videoURL)
// 	videoOutput, err := videoCmd.Output()
// 	if err != nil {
// 		return "", "", err
// 	}
// 	videoLink = strings.TrimSpace(string(videoOutput))

// 	// 获取字幕下载链接
// 	subtitleCmd := exec.Command("docker", "exec", containerName, "yt-dlp", "--write-sub", "--sub-lang", lang, "--get-url", "--skip-download", videoURL)
// 	subtitleOutput, err := subtitleCmd.Output()
// 	if err != nil {
// 		return videoLink, "", err
// 	}
// 	subtitleLink = strings.TrimSpace(string(subtitleOutput))

// 	return videoLink, subtitleLink, nil
// }

// func main() {
// 	videoURL := "https://www.ted.com/talks/liv_boeree_the_dark_side_of_competition_in_ai"
// 	// videoURL := "https://www.youtube.com/watch?v=7zC8-06198g"
// 	lang := "en"
// 	containerName := "yt_dlp" // 确保这是您容器的正确名称
// 	videoLink, subtitleLink, err := getYtDlpVideoAndSubtitleLinks(videoURL, lang, containerName)
// 	if err != nil {
// 		fmt.Println("Error:", err)
// 		return
// 	}
// 	fmt.Println("Video Link:", videoLink)
// 	fmt.Println("Subtitle Link:", subtitleLink)
// }
