//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"loop/internal/api"
	"loop/internal/app"
	"loop/internal/client"
	"loop/internal/config"
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/server"

	"github.com/google/wire"
)

func wireApp(config *config.Config) (*app.App, func(), error) {
	panic(wire.Build(
		server.ProviderSet,
		data.ProviderSet,
		api.ProviderSet,
		model.ProviderSet,
		client.ProvideSet,
		newApp,
	))
}
