version: '3.3'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: loop
    restart: always
    ports:
      - "${APP_PORT}:3000"
    environment:
      - TZ=${TZ}
      - OSS_ACCESS_KEY_ID=${OSS_ACCESS_KEY_ID}
      - OSS_ACCESS_KEY_SECRET=${OSS_ACCESS_KEY_SECRET}
    depends_on:
      - redis
    volumes:
      - /var/log/loop/:/var/log/loop/
    networks:
      - loop_network
    # 如果需要健康检查
    # healthcheck:
    #   test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
    #   interval: 30s
    #   timeout: 3s
    #   retries: 3

  redis:
    image: redis:latest
    container_name: redis
    ports:
      - "6379:6379"
    environment:
      TZ: ${TZ}
    command: >
      redis-server 
      --requirepass ${REDIS_PASSWORD}
      --appendonly yes
      --bind 0.0.0.0
      --protected-mode no
    volumes:
      - redis_data:/data
    privileged: true
    restart: always
    networks:
      - loop_network
    # 如果需要自定义 Redis 配置
    # configs:
    #   - source: redis_config
    #     target: /usr/local/etc/redis/redis.conf

networks:
  loop_network:
    driver: bridge

volumes:
  redis_data:
    driver: local

# 如果需要自定义 Redis 配置
# configs:
#   redis_config:
#     file: ./redis.conf 