package apple

import (
	"encoding/json"
	"errors"
	"net/http"
	"time"

	"gopkg.in/square/go-jose.v2"
	"gopkg.in/square/go-jose.v2/jwt"
)

func getApplePublicKeys() (*jose.JSONWebKeySet, error) {
	resp, err := http.Get("https://appleid.apple.com/auth/keys")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var keySet jose.JSONWebKeySet
	if err := json.NewDecoder(resp.Body).Decode(&keySet); err != nil {
		return nil, err
	}

	return &keySet, nil
}

func verifyIdentityToken(tokenString string) (*jwt.Claims, error) {
	keySet, err := getApplePublicKeys()
	if err != nil {
		return nil, err
	}

	token, err := jwt.ParseSigned(tokenString)
	if err != nil {
		return nil, err
	}

	for _, key := range keySet.Keys {
		claims := &jwt.Claims{}
		if err := token.Claims(key, claims); err == nil {
			if claims.Issuer != "https://appleid.apple.com" {
				return nil, errors.New("invalid issuer")
			}

			// Perform additional checks (audience, expiration, etc.) here

			return claims, nil
		}
	}

	return nil, errors.New("verification failed")
}

func VerifyApple(identityToken string) (string, error) {
	claims, err := verifyIdentityToken(identityToken)
	// Check expiration
	if claims.Expiry != nil && claims.Expiry.Time().Before(time.Now()) {
		return "", errors.New("token has expired")
	}
	if claims.IssuedAt != nil && claims.IssuedAt.Time().After(time.Now()) {
		return "", errors.New("invalid issued at time")
	}

	if err != nil {
		return "", err
	}
	return claims.Subject, nil
}
