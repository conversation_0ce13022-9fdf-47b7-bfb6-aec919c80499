package lang

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestGetAvailableLanguage(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name   string
		header string
		expect Language
	}{
		{"精确匹配 zh", "zh-aa", SimplifiedChinese},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request, _ = http.NewRequest("GET", "/", nil)
			if tt.header != "" {
				c.Request.Header.Set("Accept-Language", tt.header)
			}
			lang := GetPreferredLanguage(c)
			t.Logf("header: %s, 返回语言: %v", tt.header, lang)
			if lang != tt.expect {
				t.Errorf("header: %s, got %v, want %v", tt.header, lang, tt.expect)
			}
		})
	}
}
