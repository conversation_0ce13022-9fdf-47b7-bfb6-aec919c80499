package subtitle

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"github.com/sirupsen/logrus"
	"github.com/texttheater/golang-levenshtein/levenshtein"
)

// 子字符串匹配
func isSubstringMatch(fileName1, fileName2 string) bool {
	return strings.Contains(fileName1, fileName2) || strings.Contains(fileName2, fileName1)
}

// Jaccard 相似度
func jaccardSimilarity(s1, s2 string) float64 {
	set1 := make(map[string]struct{})
	set2 := make(map[string]struct{})

	for _, word := range strings.Split(s1, ".") {
		set1[word] = struct{}{}
	}
	for _, word := range strings.Split(s2, ".") {
		set2[word] = struct{}{}
	}

	intersection := 0
	for word := range set1 {
		if _, found := set2[word]; found {
			intersection++
		}
	}

	union := len(set1) + len(set2) - intersection

	if union == 0 {
		return 0
	}

	return float64(intersection) / float64(union)
}

func isJaccardSimilar(fileName1, fileName2 string, threshold float64) bool {
	similarity := jaccardSimilarity(fileName1, fileName2)
	return similarity > threshold
}

// 改进版 Levenshtein 距离
func substringLevenshtein(s1, s2 string) int {
	runes1 := []rune(s1)
	runes2 := []rune(s2)
	minDistance := len(runes1) + len(runes2) // 一个不可能的大值

	for i := 0; i <= len(runes1)-len(runes2); i++ {
		substr := runes1[i : i+len(runes2)]
		distance := levenshtein.DistanceForStrings(substr, runes2, levenshtein.DefaultOptions)
		if distance < minDistance {
			minDistance = distance
		}
	}

	return minDistance
}

func isLevenshteinSimilar(fileName1, fileName2 string, threshold int) bool {
	distance := substringLevenshtein(fileName1, fileName2)
	return distance < threshold
}

func SimilarText(fileName1, fileName2 string) bool {
	// 阈值设定
	jaccardThreshold := 0.1
	levenshteinThreshold := 10
	if isSubstringMatch(fileName1, fileName2) ||
		isJaccardSimilar(fileName1, fileName2, jaccardThreshold) ||
		isLevenshteinSimilar(fileName1, fileName2, levenshteinThreshold) {
		return true
	} else {
		return false
	}
}

/*
1. 如果搜索到多个结果  那就返回列表给用户自己去选择
2. 如果只有一个结果 那就返回所有的下载链接给用户
*/
func SearchSubtitleAuto(movieName string) (*SubtitleResp, error) {
	subtitleResp, err := searchAssrt(movieName)
	if err != nil {
		return nil, err
	}
	return subtitleResp, nil
}

// https://assrt.net/ 从这个网站来查询字幕  后续可能增加其他的字幕网站
func searchAssrt(movieName string) (*SubtitleResp, error) {
	if movieName == "" {
		return nil, errors.New("tedUrl is empty")
	}
	resp, err := http.Get("https://secure.assrt.net/sub/?searchword=" + movieName + "&no_redir=1")
	if err != nil {
		logrus.Debug(err)
		return nil, err
	}
	defer resp.Body.Close()

	// 检查 HTTP 响应状态
	if resp.StatusCode != 200 {
		return nil, err
	}
	// 解析 HTML
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		logrus.Error(err)
		return nil, err
	}
	var subtitles []SubtitleAssrtLinkInfo
	// 查找所有的<a>标签
	doc.Find("a").Each(func(index int, item *goquery.Selection) {
		title, titleExists := item.Attr("title")
		href, hrefExists := item.Attr("href")

		if hrefExists && titleExists && strings.Contains(href, "xml/sub") {
			// 假设链接是相对路径，可以根据需要转换为绝对路径
			fullLink := "https://secure.assrt.net" + href
			subtitles = append(subtitles, SubtitleAssrtLinkInfo{Title: title, Link: fullLink})
		}
	})
	fmt.Printf("len(subtitles): %v\n", len(subtitles))
	if len(subtitles) == 0 {
		logrus.Warn("No links found, please check if the page structure has been changed.")
	}
	if len(subtitles) == 1 {
		return GetDownloadLink(subtitles[0].Link)
	} else {
		return &SubtitleResp{Subtitles: subtitles, Type: 1}, nil
	}
}

func GetDownloadLink(link string) (*SubtitleResp, error) {
	fmt.Printf("GetDownloadLink link: %v\n", link)
	if strings.Contains(link, "xml/sub") {
		linkInfo, err := getAssrtDownload(link)
		if err != nil {
			return nil, err
		}
		return linkInfo, nil
	}
	return nil, nil
}

func getAssrtDownload(link string) (*SubtitleResp, error) {
	fmt.Printf("getAssrtDownload link: %v\n", link)
	if link == "" {
		return nil, errors.New("getAssrtDownload link is empty")
	}
	resp, err := http.Get(link)
	if err != nil {
		logrus.Debug(err)
		return nil, err
	}
	defer resp.Body.Close()

	// 检查 HTTP 响应状态
	if resp.StatusCode != 200 {
		return nil, err
	}
	// 解析 HTML
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		logrus.Error(err)
		return nil, err
	}
	var subtitleDownloadZipLink string
	// 查找所有的<a>标签
	doc.Find("a").Each(func(index int, item *goquery.Selection) {
		href, hrefExists := item.Attr("href")
		// /download/610231/We.Bare.Bears.S03E01.Grizzly.the.Movie.1080p.WEB-DL.AAC2.0.H.264-TVSmash.zip
		if hrefExists && strings.Contains(href, "download") && strings.Contains(href, "zip") {
			subtitleDownloadZipLink = href
		}
	})
	return &SubtitleResp{SubtitleDownloadLink: subtitleDownloadZipLink, Type: 1}, nil
}

type SubtitleAssrtLinkInfo struct {
	Title string
	Link  string
}
type SubtitleResp struct {
	Type                 int // 1 为字幕列表，需要用户自己去选择 2为可下载的字幕链接
	Subtitles            []SubtitleAssrtLinkInfo
	SubtitleDownloadLink string
}
