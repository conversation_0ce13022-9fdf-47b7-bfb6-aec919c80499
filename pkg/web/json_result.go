package web

import (
	"loop/pkg/i18n"
	"loop/pkg/logs"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type JsonResult struct {
	Code  int         `json:"code"`
	Msg   string      `json:"msg"`
	Data  interface{} `json:"data,omitempty"`
	Error string      `json:"error,omitempty"`
}
type PageJsonResult struct {
	Total int64       `json:"total"`
	Data  interface{} `json:"data,omitempty"`
}

func Err(code int, message string, data interface{}, err error) *JsonResult {
	res := JsonResult{
		Code: code,
		Msg:  message,
		Data: data,
	}
	// 生产环境隐藏底层报错
	if err != nil && gin.Mode() != gin.ReleaseMode {
		res.Error = err.Error()
		stack := debug.Stack()
		logrus.Printf("Internal error: %v\nStack trace: %s\n", err, stack)
	}
	return &res
}
func JsonEmptyData() *JsonResult {
	return &JsonResult{
		Code: http.StatusOK,
	}
}
func JsonData(data interface{}) *JsonResult {
	return &JsonResult{
		Code: http.StatusOK,
		Data: data,
	}
}

func JsonItemList(data []interface{}) *JsonResult {
	return &JsonResult{
		Code: http.StatusOK,
		Data: data,
	}
}

func JsonOK() *JsonResult {
	return Err(http.StatusOK, "", nil, nil)
}

func JsonCode(code int) *JsonResult {
	return Err(code, "", nil, nil)
}
func JsonErrorCodeMsg(code int, message string) *JsonResult {
	return Err(code, message, nil, nil)
}

func JsonErrorData(code int, message string, data interface{}) *JsonResult {
	return Err(code, message, data, nil)
}
func JsonEntityNotFound(c *gin.Context) *JsonResult {
	return JsonParamErr(i18n.T(c, i18n.ErrEntityNotFound))
}
func JsonParamErr(message string) *JsonResult {
	logs.ErrorCallers("JsonParamErr", 3, message)
	return Err(http.StatusBadRequest, message, nil, nil)
}
func JsonDBErr(message string, e error) *JsonResult {
	return Err(http.StatusInternalServerError, message, nil, e)
}
func JsonError(msg string) *JsonResult {
	return Err(http.StatusInternalServerError, msg, nil, nil)
}

// 业务错误
func JsonInternalError(e error) *JsonResult {
	logs.ErrorCallers("JsonInternalError", 3, e)
	return Err(http.StatusNotImplemented, "", nil, e)
}
