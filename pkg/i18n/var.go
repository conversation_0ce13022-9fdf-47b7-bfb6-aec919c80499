// Code generated by go script; DO NOT EDIT.
package i18n
var ErrAccountNotFound = `ErrAccountNotFound`
var ErrAlreadyExists = `{#ErrAlreadyExists}`
var ErrBadRequest = `ErrBadRequest`
var ErrEntityNotFound = `ErrEntityNotFound`
var ErrForbidden = `ErrForbidden`
var ErrGenerateTokenFaild = `ErrGenerateTokenFaild`
var ErrInternalServerError = `ErrInternalServerError`
var ErrInvalidPassword = `ErrInvalidPassword`
var ErrNickNameUsed = `ErrNickNameUsed`
var ErrNotFound = `{#ErrNotFound}`
var ErrTokenExpire = `ErrTokenExpire`
var ErrTokenIsEmpty = `ErrTokenIsEmpty`
var ErrTokenMismatch = `ErrTokenMismatch`
var ErrTokenNotFound = `ErrTokenNotFound`
var ErrTokenParse = `ErrTokenParse`
var ErrUnauthorized = `ErrUnauthorized`
var ErrUserNameUsed = `ErrUserNameUsed`
var ErrUserNotFound = `ErrUserNotFound`
var ErrUserWrongPasswordOrName = `ErrUserWrongPasswordOrName`
var ErrVideoLocalResourceNotFound = `ErrVideoLocalResourceNotFound`
var ErrVideoUserCustomSubtitleCreateFailed = `ErrVideoUserCustomSubtitleCreateFailed`
var ErrVideoUserCustomSubtitleNotFound = `ErrVideoUserCustomSubtitleNotFound`
