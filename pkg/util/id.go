package util

import (
	"log"
	"math/rand"
	"time"

	"github.com/bwmarrin/snowflake"
)

var node *snowflake.Node

func init() {
	rand.Seed(time.Now().UnixNano())
	randomInt := rand.Int63n(10)
	var err error
	node, err = snowflake.NewNode(randomInt)
	if err != nil {
		log.Fatalf("Failed to initialize snowflake node: %v", err)
	}
	if node == nil {
		log.Fatalf("snowflake node is not initialized")
	}
}
func GenerateSnowflakeID() string {
	return node.Generate().String()
}
