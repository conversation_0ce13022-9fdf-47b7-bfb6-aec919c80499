package util

import (
	"net"
	"net/http"
	"reflect"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/thoas/go-funk"
)

// IsProd 是否是正式环境
//func IsProd() bool {
//	return config.Instance.Env == "prod"
//}

// GetRequestIP 尽最大努力实现获取客户端 IP 的算法。
// 解析 X-Real-IP 和 X-Forwarded-For 以便于反向代理（nginx 或 haproxy）可以正常工作。
func GetRequestIP(r *http.Request) string {
	xForwardedFor := r.Header.Get("X-Forwarded-For")
	ip := strings.TrimSpace(strings.Split(xForwardedFor, ",")[0])
	if ip != "" {
		return ip
	}

	ip = strings.TrimSpace(r.Header.Get("X-Real-Ip"))
	if ip != "" {
		return ip
	}

	if ip, _, err := net.SplitHostPort(strings.TrimSpace(r.RemoteAddr)); err == nil {
		return ip
	}
	return ""
}

func GetUserAgent(r *http.Request) string {
	return r.Header.Get("User-Agent")
}

func RemoveElement(slice []int, element int) []int {
	// 查找元素的索引
	index := funk.IndexOf(slice, element)
	if index == -1 {
		// 如果元素不存在于切片中，返回原切片
		return slice
	}

	// 删除元素
	return append(slice[:index], slice[index+1:]...)
}

// 自定义验证器
var IntCanBeZero validator.Func = func(fl validator.FieldLevel) bool {
	field := fl.Field()
	kind := field.Kind()

	//处理 int 和 int64 类型的字段
	if kind == reflect.Int || kind == reflect.Int64 {
		return true
	}

	return false
}

// 注册自定义验证器
func RegisterCustomValidators(v *validator.Validate) {
	v.RegisterValidation("intCanBeZero", IntCanBeZero)
}
