package util

import "time"

func AddTimeByNow(t time.Time, years, months, days int) time.Time {
	return AddTime(time.Now(), years, months, days)
}

// Add one month
// oneMonthLater := AddTime(now, 0, 1, 0)
// fmt.Println("One month later:", oneMonthLater)

// // Add one year
// oneYearLater := AddTime(now, 1, 0, 0)
// fmt.Println("One year later:", oneYearLater)

// // Add two years, three months, and 15 days
// customDate := AddTime(now, 2, 3, 15)
func AddTime(t time.Time, years, months, days int) time.Time {
	// Add years and months naturally
	newTime := t.AddDate(years, months, 0)

	// Handle potential date overflow
	if newTime.Day() < t.Day() {
		newTime = time.Date(newTime.Year(), newTime.Month(), 0, t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), t.Location())
	}

	// Add extra days
	newTime = newTime.AddDate(0, 0, days)

	return newTime
}
func GetTodayZeroTime() int64 {
	now := time.Now()

	// 获取当天 0 点时间
	midnight := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 转换为毫秒时间戳
	midnightTimestampMillis := midnight.UnixMilli()
	return midnightTimestampMillis
}
func GetZeroTime(timestamp int64) int64 {
	// 将毫秒时间戳转换为 time.Time
	now := time.Unix(0, timestamp*int64(time.Millisecond))

	// 获取当天 0 点时间
	midnight := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 转换为毫秒时间戳
	midnightTimestampMillis := midnight.UnixMilli()
	return midnightTimestampMillis
}

func GetNextDayZeroTime(timestamp int64) int64 {
	// 将毫秒时间戳转换为 time.Time
	now := time.Unix(0, timestamp*int64(time.Millisecond))

	// 获取次日 0 点时间
	nextMidnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())

	// 转换为毫秒时间戳
	nextMidnightTimestampMillis := nextMidnight.UnixMilli()
	return nextMidnightTimestampMillis
}

func TsFormat2String(ts int64) string {
	return time.UnixMilli(ts).Format("2006-01-02 15:04:05")
}
func CalculateDays(timestamp1, timestamp2 int64) int {
	// 将时间戳转换为 time.Time
	time1 := time.UnixMilli(timestamp1)
	time2 := time.UnixMilli(timestamp2)

	// 确保时间1是较早的时间
	if time1.After(time2) {
		time1, time2 = time2, time1
	}

	// 计算时间差
	duration := time2.Sub(time1)

	// 转换为整天数
	days := int(duration.Hours() / 24)

	// 判断剩余的小时数是否 >= 8
	if int(duration.Hours())%24 >= 8 {
		days++
	}

	return days
}
