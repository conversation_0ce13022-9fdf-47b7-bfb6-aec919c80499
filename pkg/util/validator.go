package util

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/go-playground/validator/v10"
)

var (
	sqlInjectionPattern = regexp.MustCompile(`(?i)(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|ALTER|CREATE|WHERE)\s+`)
	validate            = validator.New()
)

// ValidateStruct 验证结构体
func ValidateStruct(s interface{}) error {
	return validate.Struct(s)
}

// IsSQLInjection 检测是否包含SQL注入特征
func IsSQLInjection(input string) bool {
	// 移除所有引号
	input = strings.Replace(input, "'", "", -1)
	input = strings.Replace(input, "\"", "", -1)

	return sqlInjectionPattern.MatchString(input)
}

// IsValidID 验证ID格式
func IsValidID(id string) bool {
	// ID只能包含字母、数字和下划线
	match, _ := regexp.MatchString("^[a-zA-Z0-9_-]+$", id)
	return match && len(id) <= 64
}

// ValidateIDs 批量验证ID
func ValidateIDs(ids []string, maxCount int) error {
	if len(ids) > maxCount {
		return fmt.Errorf("too many ids, maximum allowed is %d", maxCount)
	}

	for _, id := range ids {
		if !IsValidID(id) {
			return fmt.Errorf("invalid id format: %s", id)
		}
	}
	return nil
}
