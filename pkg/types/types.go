package types

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// JSONB 可以用于任何需要序列化为JSON的类型
type JSONB []byte

// MarshalJSONB 将数据序列化为JSON格式
func MarshalJSONB(v interface{}) (JSONB, error) {
	b, err := json.Marshal(v)
	if err != nil {
		return nil, err
	}
	return JSONB(b), nil
}

// UnmarshalJSONB 将JSON数据反序列化到指定的类型
func UnmarshalJSONB(b JSONB, v interface{}) error {
	return json.Unmarshal([]byte(b), v)
}

// IntArray 自定义整数数组类型
type Int64Array []int64

// IntArray 自定义整数数组类型
type IntArray []int

// StringArray 自定义字符串数组类型
type StringArray []string

// Value 实现 driver.Valuer 接口，将IntArray转换为JSON字符串存储到数据库
func (a IntArray) Value() (driver.Value, error) {
	jb, err := MarshalJSONB(a)
	if err != nil {
		return nil, err
	}
	return []byte(jb), nil // 直接返回 []byte 类型
}

// Value 实现 driver.Valuer 接口，将StringArray转换为JSON字符串存储到数据库
func (a StringArray) Value() (driver.Value, error) {
	jb, err := MarshalJSONB(a)
	if err != nil {
		return nil, err
	}
	return []byte(jb), nil // 直接返回 []byte 类型
}

// Scan 实现 sql.Scanner 接口，从数据库中读取JSON字符串并转换回IntArray
func (a *IntArray) Scan(value interface{}) error {
	if value == nil {
		*a = IntArray{}
		return nil
	}
	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return UnmarshalJSONB(JSONB(b), a)
}

// Scan 实现 sql.Scanner 接口，从数据库中读取JSON字符串并转换回StringArray
func (a *StringArray) Scan(value interface{}) error {
	if value == nil {
		*a = StringArray{}
		return nil
	}
	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return UnmarshalJSONB(JSONB(b), a)
}

// VideoTimeInterval 结构体
type VideoTimeInterval struct {
	Start int64 `json:"start"` //单位为毫秒
	End   int64 `json:"end"`   //单位为毫秒
}

// VideoTimeIntervalArray 是 VideoTimeInterval 的切片
type VideoTimeIntervalArray []VideoTimeInterval

// Value 实现 driver.Valuer 接口
func (a VideoTimeIntervalArray) Value() (driver.Value, error) {
	// 将切片转换为 JSON
	return json.Marshal(a)
}

// Scan 实现 driver.Scanner 接口
func (a *VideoTimeIntervalArray) Scan(value interface{}) error {
	if value == nil {
		*a = VideoTimeIntervalArray{}
		return nil
	}
	// 类型断言
	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	// 解析 JSON
	return json.Unmarshal(b, a)
}
