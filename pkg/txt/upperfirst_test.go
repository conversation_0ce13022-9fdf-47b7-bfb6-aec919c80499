package txt

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUcFirst(t *testing.T) {
	t.<PERSON>("photo-lover", func(t *testing.T) {
		assert.Equal(t, "Photo-lover", Upper<PERSON>irst("photo-lover"))
	})
	t.<PERSON>("cat", func(t *testing.T) {
		assert.Equal(t, "Cat", UpperFirst("Cat"))
	})
	t.Run("KwaZulu natal", func(t *testing.T) {
		assert.Equal(t, "KwaZulu natal", UpperFirst("KwaZulu natal"))
	})
	t.<PERSON>("empty string", func(t *testing.T) {
		assert.Equal(t, "", UpperFirst(""))
	})
}
