package txt

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSlug(t *testing.T) {
	t.<PERSON>("Empty", func(t *testing.T) {
		assert.Equal(t, "", Slug(""))
	})
	t.Run("BillGates", func(t *testing.T) {
		assert.Equal(t, "william-henry-gates-iii", <PERSON>lug("<PERSON> III"))
	})
	t.Run("Quotes", func(t *testing.T) {
		assert.Equal(t, "william-henry-gates", Slug("william \"Hen<PERSON>y\" gates' "))
	})
	t.<PERSON>("Chinese", func(t *testing.T) {
		assert.Equal(t, "chen-zhao", Slug(" 陈  赵"))
	})
}

func TestSlugToTitle(t *testing.T) {
	t.Run("cute_Kitten", func(t *testing.T) {
		assert.Equal(t, "Cute-Kitten", SlugToTitle("cute-kitten"))
	})
	t.<PERSON>("empty", func(t *testing.T) {
		assert.Equal(t, "", SlugToTitle(""))
	})
}
