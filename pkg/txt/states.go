package txt

// StatesByCountry maps state names by country code.
var StatesByCountry = LookupTableMap{
	"au": StatesAU,
	"br": StatesBR,
	"ca": StatesCA,
	"de": StatesDE,
	"fr": StatesFR,
	"nz": StatesNZ,
	"us": StatesUS,
}

// StatesAU maps common abbreviations for Australian provinces and territories.
var StatesAU = LookupTable{
	"Qld": "Queensland",
	"QLD": "Queensland",
	"NSW": "New South Wales",
	"Vic": "Victoria",
	"VIC": "Victoria",
	"VI":  "Victoria",
	"ACT": "Australian Capital Territory",
	"JBT": "Jervis Bay Territory",
	"TAS": "Tasmania",
	"WA":  "Western Australia",
	"NT":  "Northern Territory",
	"SA":  "South Australia",
	"AQ":  "Australian Antarctic Territory",
	"CX":  "Christmas Island",
	"CC":  "Cocos Islands",
	"HM":  "Heard Island and McDonald Islands",
	"NF":  "Norfolk Island",
	"AUS": "",
}

// StatesBR maps common abbreviations for Brazilian states.
var StatesBR = LookupTable{
	"AC": "Acre",
	"AL": "Alagoas",
	"AM": "Amazonas",
	"AP": "Amapá",
	"BA": "Bahia",
	"CE": "Ceará",
	"DF": "Distrito Federal",
	"ES": "Espírito Santo",
	"GO": "Goiás",
	"MA": "Maranhão",
	"MG": "Minas Gerais",
	"MS": "Mato Grosso do Sul",
	"MT": "Mato Grosso",
	"PA": "Pará",
	"PB": "Paraíba",
	"PE": "Pernambuco",
	"PI": "Piauí",
	"PR": "Paraná",
	"RJ": "Rio de Janeiro",
	"RN": "Rio Grande do Norte",
	"RO": "Rondônia",
	"RR": "Roraima",
	"RS": "Rio Grande do Sul",
	"SC": "Santa Catarina",
	"SE": "Sergipe",
	"SP": "São Paulo",
	"TO": "Tocantins",
}

// StatesCA maps common abbreviations for Canadian provinces and territories.
var StatesCA = LookupTable{
	"AB": "Alberta",
	"BC": "British Columbia",
	"NB": "New Brunswick",
	"NL": "Newfoundland and Labrador",
	"NS": "Nova Scotia",
	"NT": "Northwest Territories",
	"NU": "Nunavut",
	"MB": "Manitoba",
	"ON": "Ontario",
	"PE": "Prince Edward Island",
	"QC": "Quebec",
	"SK": "Saskatchewan",
	"YT": "Yukon",
}

// StatesDE maps common abbreviations for German states.
var StatesDE = LookupTable{
	"BW":                     "Baden-Württemberg",
	"Ba-Wü":                  "Baden-Württemberg",
	"Baden-Wurttemberg":      "Baden-Württemberg",
	"BY":                     "Bayern",
	"Bavaria":                "Bayern",
	"BE":                     "Berlin",
	"BER":                    "Berlin",
	"BB":                     "Brandenburg",
	"HB":                     "Bremen",
	"HH":                     "Hamburg",
	"HE":                     "Hessen",
	"NI":                     "Niedersachsen",
	"NDS":                    "Niedersachsen",
	"Lower Saxony":           "Niedersachsen",
	"Lower-Saxony":           "Niedersachsen",
	"MV":                     "Mecklenburg-Vorpommern",
	"NW":                     "Nordrhein-Westfalen",
	"NRW":                    "Nordrhein-Westfalen",
	"North Rhine-Westphalia": "Nordrhein-Westfalen",
	"RP":                     "Rheinland-Pfalz",
	"RLP":                    "Rheinland-Pfalz",
	"Rhineland-Palatinate":   "Rheinland-Pfalz",
	"SL":                     "Saarland",
	"SN":                     "Sachsen",
	"Saxony":                 "Sachsen",
	"ST":                     "Sachsen-Anhalt",
	"Saxony-Anhalt":          "Sachsen-Anhalt",
	"Saxony Anhalt":          "Sachsen-Anhalt",
	"Sachsen Anhalt":         "Sachsen-Anhalt",
	"SH":                     "Schleswig-Holstein",
	"TH":                     "Thüringen",
	"Thuringia":              "Thüringen",
	"Thuringen":              "Thüringen",
}

// StatesFR maps common abbreviations for French states.
var StatesFR = LookupTable{
	"France métropolitaine": "",
}

// StatesNZ maps common abbreviations for provinces and territories in New Zealand.
var StatesNZ = LookupTable{
	"AUK": "Auckland",
	"BOP": "Bay of Plenty",
	"CAN": "Canterbury",
	"GIS": "Gisborne",
	"HKB": "Hawke's Bay",
	"MBH": "Marlborough",
	"MWT": "Manawatu-Wanganui",
	"NSN": "Nelson",
	"NTL": "Northland",
	"OTA": "Otago",
	"STL": "Southland",
	"TAS": "Tasman",
	"TKI": "Taranaki",
	"WKO": "Waikato",
	"WGN": "Wellington",
	"WTC": "West Coast",
	"CIT": "Chatham Islands",
	"NZ":  "",
}

// StatesUS maps common abbreviations for US states.
var StatesUS = LookupTable{
	"AL":  "Alabama",
	"AK":  "Alaska",
	"AS":  "American Samoa",
	"AZ":  "Arizona",
	"AR":  "Arkansas",
	"CA":  "California",
	"CO":  "Colorado",
	"CT":  "Connecticut",
	"DE":  "Delaware",
	"DC":  "District of Columbia",
	"FM":  "Federated States of Micronesia",
	"FL":  "Florida",
	"GA":  "Georgia",
	"GU":  "Guam",
	"HI":  "Hawaii",
	"ID":  "Idaho",
	"IL":  "Illinois",
	"IN":  "Indiana",
	"IA":  "Iowa",
	"KS":  "Kansas",
	"KY":  "Kentucky",
	"LA":  "Louisiana",
	"MB":  "Manitoba",
	"ME":  "Maine",
	"MH":  "Marshall Islands",
	"MD":  "Maryland",
	"MA":  "Massachusetts",
	"MI":  "Michigan",
	"MN":  "Minnesota",
	"MS":  "Mississippi",
	"MO":  "Missouri",
	"MT":  "Montana",
	"NE":  "Nebraska",
	"NV":  "Nevada",
	"NH":  "New Hampshire",
	"NJ":  "New Jersey",
	"NM":  "New Mexico",
	"NY":  "New York",
	"NC":  "North Carolina",
	"ND":  "North Dakota",
	"NS":  "Nova Scotia",
	"MP":  "Northern Mariana Islands",
	"OH":  "Ohio",
	"OK":  "Oklahoma",
	"OR":  "Oregon",
	"PW":  "Palau",
	"PA":  "Pennsylvania",
	"PR":  "Puerto Rico",
	"P.R": "Puerto Rico",
	"RI":  "Rhode Island",
	"SK":  "Saskatchewan",
	"SC":  "South Carolina",
	"SD":  "South Dakota",
	"TN":  "Tennessee",
	"TX":  "Texas",
	"UT":  "Utah",
	"VT":  "Vermont",
	"VI":  "Virgin Islands",
	"VA":  "Virginia",
	"WA":  "Washington",
	"WV":  "West Virginia",
	"WI":  "Wisconsin",
	"WY":  "Wyoming",
}
