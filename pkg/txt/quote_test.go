package txt

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestQuote(t *testing.T) {
	t.Run("The quick brown fox.", func(t *testing.T) {
		assert.Equal(t, "“The quick brown fox.”", Quote("The quick brown fox."))
	})
	t.<PERSON>("filename.txt", func(t *testing.T) {
		assert.Equal(t, "filename.txt", <PERSON>uo<PERSON>("filename.txt"))
	})
	t.<PERSON>("empty string", func(t *testing.T) {
		assert.Equal(t, "“”", Quote(""))
	})
}

func TestQuoteLower(t *testing.T) {
	t.<PERSON>("The quick brown fox.", func(t *testing.T) {
		assert.Equal(t, "“the quick brown fox.”", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("The quick brown fox."))
	})
	t.Run("filename.txt", func(t *testing.T) {
		assert.Equal(t, "filename.txt", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("filename.txt"))
	})
	t.<PERSON>("empty string", func(t *testing.T) {
		assert.Equal(t, "“”", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(""))
	})
}
