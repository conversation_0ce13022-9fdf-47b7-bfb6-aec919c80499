package txt

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSpaced(t *testing.T) {
	t.Run("Empty", func(t *testing.T) {
		assert.Equal(t, "  ", Spaced(""))
	})
	t.Run("Space", func(t *testing.T) {
		assert.Equal(t, "   ", Spaced(" "))
	})
	t.<PERSON>("Chinese", func(t *testing.T) {
		assert.Equal(t, " 李 ", <PERSON><PERSON>("李"))
	})
	t.<PERSON>("And", func(t *testing.T) {
		assert.Equal(t, " and ", Spaced("and"))
	})
}

func TestStripOr(t *testing.T) {
	t.Run("Empty", func(t *testing.T) {
		assert.Equal(t, "", StripOr(""))
	})
	t.Run("EnOr", func(t *testing.T) {
		assert.Equal(t, "or", StripOr("or"))
	})
	t.Run("SpacedEnOr", func(t *testing.T) {
		assert.Equal(t, "李 or Foo", <PERSON><PERSON>r("李 or <PERSON>oo"))
	})
	t.<PERSON>("Or", func(t *testing.T) {
		assert.Equal(t, "李   Foo", Strip<PERSON>r("李 | Foo"))
	})
}

func TestQueryTooShort(t *testing.T) {
	t.Run("Empty", func(t *testing.T) {
		assert.False(t, QueryTooShort(""))
	})
	t.Run("IsTooShort", func(t *testing.T) {
		assert.True(t, QueryTooShort("aa"))
	})
	t.Run("Chinese", func(t *testing.T) {
		assert.False(t, QueryTooShort("李"))
	})
	t.Run("OK", func(t *testing.T) {
		assert.False(t, QueryTooShort("foo"))
	})
}
