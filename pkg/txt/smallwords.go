package txt

var SmallWords = map[string]bool{
	"a":              true, // English ↓
	"an":             true,
	"as":             true,
	"at":             true,
	"by":             true,
	"in":             true,
	"of":             true,
	"on":             true,
	"or":             true,
	"up":             true,
	"to":             true,
	"and":            true,
	"but":            true,
	"for":            true,
	"nor":            true,
	"the":            true,
	"from":           true,
	"with":           true,
	"de":             true, // French ↓
	"des":            true,
	"en":             true,
	"le":             true,
	"la":             true,
	"van":            true, // Dutch  ↓
	"zu":             true, // German ↓
	"ab":             true,
	"um":             true,
	"bei":            true,
	"aus":            true,
	"das":            true,
	"der":            true,
	"dem":            true,
	"mir":            true,
	"auf":            true,
	"ist":            true,
	"und":            true,
	"uns":            true,
	"von":            true,
	"für":            true,
	"ein":            true,
	"eine":           true,
	"sind":           true,
	"sein":           true,
	"auch":           true,
	"mich":           true,
	"oben":           true,
	"nach":           true,
	"über":           true,
	"ohne":           true,
	"eines":          true,
	"nicht":          true,
	"davor":          true,
	"unter":          true,
	"neben":          true,
	"gegen":          true,
	"viele":          true,
	"trotz":          true,
	"warst":          true,
	"waren":          true,
	"sehen":          true,
	"später":         true,
	"werden":         true,
	"werdet":         true,
	"dessen":         true,
	"gesehen":        true,
	"abseits":        true,
	"entlang":        true,
	"sichtbar":       true,
	"entgegen":       true,
	"zwischen":       true,
	"oberhalb":       true,
	"unterhalb":      true,
	"bezüglich":      true,
	"unsichtbar":     true,
	"einschließlich": true,
}
