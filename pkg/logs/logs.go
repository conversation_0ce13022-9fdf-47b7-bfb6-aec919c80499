package logs

import (
	"fmt"
	"runtime"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func InfoCaller(format string, args ...interface{}) {
	if !gin.IsDebugging() {
		InfoCallers(format, 1, args...)
	}
}

func InfoCallers(format string, skip int, args ...interface{}) {
	if !gin.IsDebugging() {
		if pc, file, line, ok := runtime.Caller(skip); ok {
			funcName := runtime.FuncForPC(pc).Name()
			message := fmt.Sprintf(format, args...)
			logrus.WithFields(logrus.Fields{
				"caller": fmt.Sprintf("%s:%d %s", file, line, funcName),
			}).Info(message)
		}
	}
}

func ErrorCaller(format string, args ...interface{}) {
	if !gin.IsDebugging() {
		ErrorCallers(format, 1, args...)
	}
}

func ErrorCallers(format string, skip int, args ...interface{}) {
	if !gin.IsDebugging() {
		if pc, file, line, ok := runtime.Caller(skip); ok {
			funcName := runtime.FuncForPC(pc).Name()
			message := fmt.Sprintf(format, args...)
			logrus.WithFields(logrus.Fields{
				"caller": fmt.Sprintf("%s:%d %s", file, line, funcName),
			}).Error(message)
		}
	}

}
