package jwtx

import (
	"loop/pkg/lang"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type Claims struct {
	Uid      string
	Username string
	Status   int
	jwt.StandardClaims
}
type AdminClaims struct {
	Uid       string
	Username  string
	RoleLevel int
	Status    int
	jwt.StandardClaims
}

func GetUid(c *gin.Context) string {
	return c.GetString("uid")
}

func GetPlatform(c *gin.Context) string {
	return c.GetHeader("platform")
}
func GetTargetLangCode(c *gin.Context) string {
	lang := lang.GetPreferredLanguage(c)
	return lang.Code()
}

func GenerateToken(uid string, username string, status int, jwtSecret string, accessExpire int64) (string, error) {
	logrus.Printf("GenerateToken jwtSecret: %v uid: %v uname %v status %v", jwtSecret, uid, username, status)
	now := time.Now().Unix()
	claims := Claims{
		uid,
		username,
		status,
		jwt.StandardClaims{
			ExpiresAt: now + accessExpire,
			IssuedAt:  now,
		},
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedString, err := tokenClaims.SignedString([]byte(jwtSecret))

	return signedString, err
}

func ParseToken(token string, jwtSecret string) (*Claims, error) {
	tokenString := strings.TrimPrefix(token, "Bearer ")
	tokenClaims, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(jwtSecret), nil
	})
	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*Claims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}
	return nil, err
}

func GenerateAdminToken(uid string, username string, roleLevel int, status int, jwtSecret string, accessExpire int64) (string, error) {
	logrus.Printf("GenerateToken jwtSecret: %v\n", jwtSecret)
	now := time.Now().Unix()
	claims := AdminClaims{
		uid,
		username,
		roleLevel,
		status,
		jwt.StandardClaims{
			ExpiresAt: now + accessExpire,
			IssuedAt:  now,
		},
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedString, err := tokenClaims.SignedString([]byte(jwtSecret))

	return signedString, err
}

func ParseAdminToken(token string, jwtSecret string) (*AdminClaims, error) {
	tokenString := strings.TrimPrefix(token, "Bearer ")
	tokenClaims, err := jwt.ParseWithClaims(tokenString, &AdminClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(jwtSecret), nil
	})
	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*AdminClaims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}
	return nil, err
}
