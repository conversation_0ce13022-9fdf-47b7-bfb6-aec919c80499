package oss

import (
	"context"
	"fmt"
	"log"
	"loop/internal/client"
	"net/url"
	"strings"
	"time"

	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
)

// GetOssSignedURL 获取预签名的下载URL
func GetOssSignedURL(client *client.Client, url string) string {
	bucket, key, err := parseOSSURL(url)
	log.Println("bucket", bucket, "key", key)
	if err != nil {
		log.Println("parseOSSURL error", err)
		return url
	}
	result, err := client.OssClient.Presign(context.TODO(), &oss.GetObjectRequest{
		Bucket: oss.Ptr(bucket),
		Key:    oss.Ptr(key),
	},
		oss.PresignExpires(1*time.Minute),
	)
	if err != nil {
		log.Println("Presign error", err)
		return url
	}
	return result.URL
}

// ParseOSSURL 从标准OSS URL中提取Bucket和Key
// 输入示例: https://bucket-name.oss-cn-hangzhou.aliyuncs.com/path/to/object.txt
// 输出: bucket="bucket-name", key="path/to/object.txt"
func parseOSSURL(ossURL string) (bucket, key string, err error) {

	// 解析URL结构
	u, err := url.Parse(ossURL)
	if err != nil {
		log.Printf("URL 解析失败: %v", err)
		return "", "", fmt.Errorf("invalid URL format: %v", err)
	}

	// 提取Bucket（域名第一段）
	hostParts := strings.Split(u.Host, ".")

	// 检查是否是标准的 OSS 域名格式
	// 标准格式为: bucket-name.oss-region.aliyuncs.com
	if len(hostParts) < 3 {
		log.Printf("域名部分切分数量不足: %d", len(hostParts))
		return "", "", fmt.Errorf("not a standard OSS endpoint")
	}

	// 获取 bucket 名称
	bucket = hostParts[0]

	// 提取Key（去除路径开头的/）
	key = strings.TrimPrefix(u.Path, "/")

	if bucket == "" || key == "" {
		log.Printf("提取结果无效: bucket=%s, key=%s", bucket, key)
		return "", "", fmt.Errorf("invalid bucket or key")
	}

	return bucket, key, nil
}
