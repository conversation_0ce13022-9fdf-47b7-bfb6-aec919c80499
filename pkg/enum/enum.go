package enum

type CategoryType uint

const (
	UnknownCategoryType CategoryType = iota
	Level                            // 等级
	Scene                            // 场景
	Topic                            // 主题
)

// GetCategoryTypePriority 获取分类类型的优先级
func (c CategoryType) GetPriority() int64 {
	switch c {
	case Level:
		return 10
	case Scene:
		return 100
	case Topic:
		return 1000
	default:
		return 0
	}
}

// GetCategoryTypeName 获取分类类型的中文名称
func (c CategoryType) GetName() string {
	switch c {
	case Level:
		return "等级"
	case Scene:
		return "场景"
	case Topic:
		return "主题"
	default:
		return "未知"
	}
}

// GetCategoryTypeDescription 获取分类类型的描述
func (c CategoryType) GetDescription() string {
	switch c {
	case Level:
		return "英语学习等级分类，如初级、中级、高级等"
	case Scene:
		return "英语使用场景分类，如商务、旅游、日常对话等"
	case Topic:
		return "英语学习主题分类，如语法、词汇、发音等"
	default:
		return ""
	}
}

// 为了向后兼容，保留旧的类型名和函数
type CategotyType = CategoryType

const (
	UnknowCategotyType CategotyType = UnknownCategoryType
)

func (c CategotyType) String() string {
	return CategoryType(c).GetName()
}

type FeaturedContentType uint

const (
	UnknowFeaturedContentType FeaturedContentType = iota
	Series
	Resource
)

func (c FeaturedContentType) String() string {
	switch c {
	case Series:
		return "Series"
	case Resource:
		return "Resource"
	}
	return "Unknown"
}

type RoleLevel int

const (
	//仅能访问公共信息，几乎没有修改权限。
	GuestLevel RoleLevel = iota // 0
	//普通用户，具有基本操作权限。
	UserLevel  // 1
	AdminLevel // 2
)

type SortType int

const (
	Default SortType = iota
	Priority
)

func (c SortType) String() string {
	switch c {
	case Default:
		return ""
	case Priority:
		return "priority"
	}
	return ""
}
func (c SortType) DescString() string {
	switch c {
	case Default:
		return ""
	case Priority:
		return "priority desc"
	}
	return ""
}

type ResourceType int

// 视频类型 1代表远程的资源 2代表本地资源
const (
	UnknowResourceType ResourceType = iota
	RemoteResouce
	LocalResource
)

type VipSubscriptionType int

const (
	UnknowVipSubscriptionType VipSubscriptionType = iota
	MonthlyVip
	YearlyVip
)
