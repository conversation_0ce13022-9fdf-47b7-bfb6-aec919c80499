package dbx

import (
	"context"
	"errors"
	"fmt"
	"loop/pkg/util"
	"reflect"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const table_name = "$Table_Name$"

type TableNameAble interface {
	TableName() string
}

type DBExtension struct {
	*gorm.DB
}

func NewDBWrapper(db *gorm.DB) *DBExtension {
	return &DBExtension{
		DB: db,
	}
}

type UpdateAttrs map[string]interface{}

func (r *DBExtension) GetListOnly(result interface{}) error {
	return r.GetList(result, "", 0, 0, "")
}
func (r *DBExtension) GetList(result interface{}, query interface{}, args ...interface{}) error {
	return r.getListCore(result, "", 0, 0, query, args)
}

func (r *DBExtension) GetOrderedList(result interface{}, order string, query interface{}, args ...interface{}) error {
	return r.getListCore(result, order, 0, 0, query, args)
}

func (r *DBExtension) GetPageRangeList(result interface{}, order string, page, pageSize int, query interface{}, args ...interface{}) error {
	return r.getListCore(result, order, page, pageSize, query, args)
}

func (r *DBExtension) getListCore(result interface{}, order string, page, pageSize int, query interface{}, args []interface{}) error {
	_, err := r.getPageCountCoreInner(result, order, page, pageSize, query, args)
	if err != nil {
		return err
	}
	return nil
}
func (r *DBExtension) GetListPage(result interface{}, page, pageSize int, query interface{}, args ...interface{}) (int64, error) {
	return r.GetQueryPage(result, page, pageSize, query, args...)
}
func (r *DBExtension) GetQueryPage(result interface{}, page, pageSize int, query interface{}, args ...interface{}) (int64, error) {
	return r.getPageCountCoreInner(result, "", page, pageSize, query, args)
}
func (r *DBExtension) GetOrderPage(result interface{}, order string, page, pageSize int) (int64, error) {
	return r.GetOrderQueryPage(result, order, page, pageSize, "")
}
func (r *DBExtension) GetOrderQueryPage(result interface{}, order string, page, pageSize int, query interface{}, args ...interface{}) (int64, error) {
	return r.getPageCountCoreInner(result, order, page, pageSize, query, args)
}
func (r *DBExtension) getPageCountCoreInner(result interface{}, order string, currentPage, pageSize int, query interface{}, args []interface{}) (int64, error) {

	var (
		tableNameAble TableNameAble
		ok            bool
		total         int64
		limit         int
		offset        int
	)

	if query != "" {
		r.Model(result).Where(query, args...).Count(&total)
	} else {
		r.Model(result).Count(&total)
	}

	if currentPage <= 0 {
		currentPage = 0
	} // 当前页小于0则置为0
	switch {
	case pageSize > 100:
		pageSize = 100
	case pageSize <= 0:
		pageSize = int(total)
	}
	limit = pageSize
	offset = (currentPage - 1) * pageSize

	if tableNameAble, ok = query.(TableNameAble); !ok {
		resultType := reflect.TypeOf(result)
		if resultType.Kind() != reflect.Ptr {
			return total, errors.New("result is not a pointer")
		}

		sliceType := resultType.Elem()
		if sliceType.Kind() != reflect.Slice {
			return total, errors.New("result doesn't point to a slice")
		}
		// *Item
		itemPtrType := sliceType.Elem()
		// Item
		itemType := itemPtrType.Elem()

		elemValue := reflect.New(itemType)
		elemValueType := reflect.TypeOf(elemValue)
		tableNameAbleType := reflect.TypeOf((*TableNameAble)(nil)).Elem()

		if elemValueType.Implements(tableNameAbleType) {
			return total, errors.New("neither the query nor result implement TableNameAble")
		}

		tableNameAble = elemValue.Interface().(TableNameAble)
	}

	db := r.Table(tableNameAble.TableName())
	if query != "" {
		db = db.Where(query, args...)
	}

	if len(order) != 0 {
		db = db.Order(order)
	}

	if offset > 0 {
		db = db.Offset(offset)
	}

	if limit > 0 {
		db = db.Limit(limit)
	}

	if err := db.Find(result).Error; err != nil {
		logrus.Error("mysql", err, fmt.Sprintf("failed to query %s, query is %+v, args are %+v, order is %s, limit is %d", tableNameAble.TableName(), query, args, order, limit))
		return total, err
	}

	return total, nil
}

// Update All Fields
func (r *DBExtension) SaveOne(value TableNameAble) error {
	tableNameAble, ok := value.(TableNameAble)
	if !ok {
		logrus.Error("SaveOne value doesn't implement TableNameAble")
		return errors.New("value doesn't implement TableNameAble")
	}

	var err error
	if err = r.Save(value).Error; err != nil {
		logrus.Error("mysql", err, fmt.Sprintf("Failed to save %s, the value is %+v", tableNameAble.TableName(), value))
	}
	return err
}

// Update selected Fields, if attrs is an object, it will ignore default value field; if attrs is map, it will ignore unchanged field.
func (r *DBExtension) Update(attrs interface{}, query interface{}, args ...interface{}) error {
	var (
		tableNameAble TableNameAble
		ok            bool
		tableName     string
	)

	if tableNameAble, ok = query.(TableNameAble); ok {
		tableName = tableNameAble.TableName()
	} else if tableNameAble, ok = attrs.(TableNameAble); ok {
		tableName = tableNameAble.TableName()
	} else if attrMap, isUpdateAttrs := attrs.(UpdateAttrs); isUpdateAttrs {
		tableName = attrMap[table_name].(string)
		delete(attrMap, table_name)
	}

	if tableName == "" {
		return errors.New("can't get table name from both attrs and query")
	}

	var err error
	db := r.Table(tableName).Where(query, args...).Updates(attrs)

	if err = db.Error; err != nil {
		logrus.Error("mysql", err, fmt.Sprintf("failed to update %s, query is %+v, args are %+v, attrs is %+v", tableName, query, args, attrs))
	}

	if db.RowsAffected == 0 {
		logrus.Warn("mysql", nil, fmt.Sprintf("No rows is updated.For %s, query is %+v, args are %+v, attrs is %+v", tableName, query, args, attrs))
	}

	return err
}

func (r *DBExtension) GetOne(result interface{}, query interface{}, args ...interface{}) (found bool, err error) {
	// 参数验证
	if queryStr, ok := query.(string); ok {
		if util.IsSQLInjection(queryStr) {
			return false, errors.New("potential SQL injection detected")
		}
	}

	var tableNameAble TableNameAble
	var ok bool

	if tableNameAble, ok = query.(TableNameAble); !ok {
		if tableNameAble, ok = result.(TableNameAble); !ok {
			return false, errors.New("neither the query nor result implement TableNameAble")
		}
	}

	// 添加审计日志
	r.auditLog("GetOne", fmt.Sprintf("%v", query), args...)

	err = r.Table(tableNameAble.TableName()).Where(query, args...).First(result).Error

	if err == gorm.ErrRecordNotFound {
		return false, nil
	}

	if err != nil {
		logrus.Error("mysql", err, fmt.Sprintf("failed to query %s, the query is %+v, args are %+v", tableNameAble.TableName(), query, args))
		return false, err
	}

	return true, nil
}
func (r *DBExtension) Tx(funcs ...func(db *DBExtension) error) (err error) {
	tx := r.Begin()
	if tx.Error != nil {
		return tx.Error // 如果 Begin 返回错误，则直接返回
	}

	// 创建一个新的 DBExtension 实例用于事务
	txExtension := NewDBWrapper(tx)

	// 使用 defer 来回滚事务，如果有 panic 发生或者 err 不为 nil
	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			err = fmt.Errorf("panic occurred: %v", p) // p 是引发 panic 的实际值
		} else if err != nil {
			tx.Rollback() // 如果函数链中有错误发生，也回滚事务
		} else {
			// 尝试提交事务
			if commitErr := tx.Commit().Error; commitErr != nil {
				err = commitErr // 如果提交失败，设置 err 为提交的错误
			}
		}
	}()

	for _, f := range funcs {
		err = f(txExtension) // 执行传入的函数，使用新的 DBExtension 实例
		if err != nil {
			return // 如果有错误发生，返回，defer 中的回滚会被触发
		}
	}

	return // 返回提交的结果，或者 nil
}

func (r *DBExtension) ExecSql(result interface{}, sql string, args ...interface{}) error {
	err := r.Raw(sql, args...).Scan(result).Error

	if err != nil {
		logrus.Error("mysql", err, fmt.Sprintf("failed to execute sql %s, args are %+v", sql, args))
	}

	return err
}

func (r *DBExtension) Count(count *int64, query interface{}) error {
	return r.countCore(count, "", query)
}

func (r *DBExtension) CountBy(count *int64, byField string, query interface{}) error {
	return r.countCore(count, byField, query)
}

func (r *DBExtension) countCore(count *int64, byField string, query interface{}) error {
	tableNameAble, ok := query.(TableNameAble)

	if !ok {
		return errors.New("the query doesn't implement TableNameAble")
	}

	tableName := tableNameAble.TableName()

	db := r.Table(tableName).Where(query)

	if byField != "" {
		db = db.Select("count(?)", byField)
	}

	if err := db.Count(count).Error; err != nil {
		logrus.Error("mysql", err, fmt.Sprintf("failed to count %s, query is %+v, byField is %s", tableNameAble.TableName(), query, byField))
		return err
	}

	return nil
}

// Upsert 通用的插入或更新方法
func (r *DBExtension) Upsert(result interface{}, uniqueColumns []string, assignmentColumns []string) (bool, error) {
	var (
		tableNameAble TableNameAble
		ok            bool
	)

	if tableNameAble, ok = result.(TableNameAble); !ok {
		return false, errors.New("neither the query nor result implement TableNameAble")
	}

	// 使用反射获取记录类型
	value := reflect.ValueOf(result)
	if value.Kind() != reflect.Ptr || value.IsNil() {
		return false, errors.New("record must be a non-nil pointer")
	}

	// 创建唯一约束的列定义
	columns := make([]clause.Column, len(uniqueColumns))
	for i, colName := range uniqueColumns {
		columns[i] = clause.Column{Name: colName}
	}

	// 执行插入或更新操作
	resultDB := r.DB.Table(tableNameAble.TableName()).Clauses(clause.OnConflict{
		Columns:   columns,                                     // 唯一约束字段
		DoUpdates: clause.AssignmentColumns(assignmentColumns), // 冲突时更新字段
		DoNothing: true,
	}).Create(result)

	if resultDB.Error != nil {
		return false, resultDB.Error
	}

	// 根据 RowsAffected 判断是插入还是更新
	isInsert := resultDB.RowsAffected == 1
	return isInsert, nil
}
func (r *DBExtension) InsertIfNotExists(result interface{}, uniqueColumns []string) (bool, error) {
	var (
		tableNameAble TableNameAble
		ok            bool
	)

	if tableNameAble, ok = result.(TableNameAble); !ok {
		return false, errors.New("neither the query nor result implement TableNameAble")
	}

	value := reflect.ValueOf(result)
	if value.Kind() != reflect.Ptr || value.IsNil() {
		return false, errors.New("record must be a non-nil pointer")
	}

	columns := make([]clause.Column, len(uniqueColumns))
	for i, colName := range uniqueColumns {
		columns[i] = clause.Column{Name: colName}
	}

	resultDB := r.DB.Table(tableNameAble.TableName()).Clauses(clause.OnConflict{
		Columns:   columns,
		DoNothing: true, // 冲突时不更新任何字段
	}).Create(result)

	if resultDB.Error != nil {
		return false, resultDB.Error
	}

	// 根据 RowsAffected 判断是否为插入
	isInsert := resultDB.RowsAffected == 1
	return isInsert, nil
}

// SafeExecSQL 安全执行SQL的包装方法
func (r *DBExtension) SafeExecSQL(result interface{}, sql string, args ...interface{}) error {
	// 检查SQL注入
	if util.IsSQLInjection(sql) {
		return errors.New("potential SQL injection detected")
	}

	// 添加审计日志
	r.auditLog("SafeExecSQL", sql, args...)

	return r.Raw(sql, args...).Scan(result).Error
}

// DeleteMultiWithValidation 带验证的批量删除
func (r *DBExtension) DeleteMultiWithValidation(instance interface{}, ids []string) error {
	if err := util.ValidateIDs(ids, 1000); err != nil {
		return err
	}

	return r.Where("id IN ?", ids).Delete(instance).Error
}

// SafeTransaction 安全的事务处理
func (r *DBExtension) SafeTransaction(fn func(*DBExtension) error) error {
	return r.Transaction(func(tx *gorm.DB) error {
		// 设置事务超时
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		txExtension := NewDBWrapper(tx.WithContext(ctx))
		return fn(txExtension)
	})
}

// auditLog 审计日志记录
func (r *DBExtension) auditLog(operation string, query string, args ...interface{}) {
	logrus.WithFields(logrus.Fields{
		"operation": operation,
		"query":     query,
		"args":      args,
		"timestamp": time.Now(),
	}).Info("SQL Audit Log")
}
