/*
Package clean provides validation, sanitation, and normalization of input values.

Copyright (c) 2018 - 2023 PhotoPrism UG. All rights reserved.

	This program is free software: you can redistribute it and/or modify
	it under Version 3 of the GNU Affero General Public License (the "AGPL"):
	<https://docs.photoprism.app/license/agpl>

	This program is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU Affero General Public License for more details.

	The AGPL is supplemented by our Trademark and Brand Guidelines,
	which describe how our Brand Assets may be used:
	<https://www.photoprism.app/trademark>

Feel free to send an <NAME_EMAIL> if you have questions,
want to support our work, or just want to say hello.

Additional information can be found in our Developer Guide:
<https://docs.photoprism.app/developer-guide/>
*/
package clean

import "strings"

const MaxLength = 4096

func reject(s string, maxLength int) bool {
	if maxLength > 0 && len(s) > maxLength {
		return true
	}

	if strings.Contains(s, "${") || strings.Contains(s, "ldap://") {
		return true
	}

	return false
}
