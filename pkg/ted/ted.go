package ted

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"

	"github.com/PuerkitoBio/goquery"
	"github.com/sirupsen/logrus"
)

// "https://www.ted.com/talks/morgan_spurlock_the_greatest_ted_talk_ever_sold?language=zh"
func ParseTedJson(tedUrl string) (*TedResp, error) {
	fmt.Printf("tedUrl: %v\n", tedUrl)
	if tedUrl == "" {
		return nil, errors.New("tedUrl is empty")
	}
	resp, err := http.Get(tedUrl)
	if err != nil {
		logrus.Debug(err)
		return nil, err
	}
	defer resp.Body.Close()

	// 检查 HTTP 响应状态
	if resp.StatusCode != 200 {
		return nil, err
	}

	// 解析 HTML
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		logrus.Error(err)
		return nil, err
	}

	// 查找具有特定 ID 的 <script> 标签
	nextDataScript := doc.Find("#__NEXT_DATA__").First()
	if nextDataScript.Length() == 0 {
		logrus.Error("No element with ID '__NEXT_DATA__'")
		return nil, err
	}

	// 获取该元素的文本内容，即 JSON 数据
	dataJson := nextDataScript.Text()

	// 解析 JSON 数据
	var allData AllData
	if err := json.Unmarshal([]byte(dataJson), &allData); err != nil {
		logrus.Error(err)
		return nil, err
	}
	// 现在解析 VideoData 中的 PlayerData JSON 字符串
	var playerData PlayerData
	err = json.Unmarshal([]byte(allData.Props.PageProps.VideoData.PlayerData), &playerData)
	if err != nil {
		logrus.Error("JSON unmarshal error:", err)
		return nil, err
	}

	videoData := allData.Props.PageProps.VideoData

	mp4_url := ""
	if len(playerData.Resources.H264) != 0 {
		mp4_url = playerData.Resources.H264[0].File
	}
	resp, err = http.Get(playerData.Resources.HLS.Metadata)
	if err != nil {
		logrus.Error(err)
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logrus.Error(err)
		return nil, err
	}

	var metadata Metadata
	err = json.Unmarshal(body, &metadata)
	if err != nil {
		logrus.Error(err)
		return nil, err
	}

	tedResp := TedResp{
		Id:                   videoData.Id,
		ShortenedUrl:         allData.Props.PageProps.ShortenedUrl,
		Description:          videoData.Description,
		PlayerUrl:            mp4_url,
		PresenterDisplayName: videoData.PresenterDisplayName,
		Title:                videoData.Title,
		SocialTitle:          videoData.SocialTitle,
		PublishedAt:          videoData.PublishedAt,
		Duration:             videoData.Duration,
		ViewedCount:          videoData.ViewedCount,
		Canonical:            playerData.Canonical,
		Thumb:                playerData.Thumb,
		Slug:                 playerData.Slug,
		M3u8:                 playerData.Resources.HLS.Stream,
		Metadata:             playerData.Resources.HLS.Metadata,
		Subtitles:            metadata.Subtitles,
		Languages:            playerData.Languages,
		YoutubeUrl:           "https://www.youtube.com/watch?v=" + playerData.PlayerDataExternal.Code,
	}
	return &tedResp, nil
}

type AllData struct {
	Props Props `json:"props"`
}
type Props struct {
	PageProps PageProps `json:"pageProps"`
}
type PageProps struct {
	VideoData    VideoData `json:"videoData"`
	ShortenedUrl string    `json:"shortenedUrl"`
}
type VideoData struct {
	Id                   string `json:"id"`
	PlayerData           string `json:"playerData"`
	Description          string `json:"description"`
	PresenterDisplayName string `json:"presenterDisplayName"`
	Title                string `json:"title"`
	SocialTitle          string `json:"socialTitle"`
	PublishedAt          string `json:"publishedAt"`
	Duration             int    `json:"duration"`
	ViewedCount          int    `json:"viewedCount"`
}
type PlayerData struct {
	Resources          Resources          `json:"resources"`
	Canonical          string             `json:"canonical"`
	Thumb              string             `json:"thumb"`
	Slug               string             `json:"slug"`
	Languages          []LanguageResp     `json:"languages"`
	PlayerDataExternal PlayerDataExternal `json:"external"`
}
type PlayerDataExternal struct {
	Code    string `json:"code"`
	Service string `json:"service"`
}
type Resources struct {
	HLS  HLS    `json:"hls"`
	H264 []H264 `json:"h264"`
}

type HLS struct {
	Stream   string `json:"stream"`
	Metadata string `json:"metadata"`
}
type H264 struct {
	File string `json:"file"`
}
type Metadata struct {
	Subtitles []SubtitleResp `json:"subtitles"`
}

type TedResp struct {
	Id                   string         `json:"id"`
	ShortenedUrl         string         `json:"shortenedUrl"`
	PlayerUrl            string         `json:"playerUrl"`
	Description          string         `json:"description"`
	PresenterDisplayName string         `json:"presenterDisplayName"`
	Title                string         `json:"title"`
	SocialTitle          string         `json:"socialTitle"`
	PublishedAt          string         `json:"publishedAt"`
	Duration             int            `json:"duration"`
	ViewedCount          int            `json:"viewedCount"`
	Canonical            string         `json:"canonical"`
	Thumb                string         `json:"thumb"`
	Slug                 string         `json:"slug"`
	M3u8                 string         `json:"m3u8"`
	Metadata             string         `json:"metadata"`
	Subtitles            []SubtitleResp `json:"subtitles"`
	Languages            []LanguageResp `json:"languages"`
	YoutubeUrl           string         `json:"youTubeUrl"`
}
type SubtitleResp struct {
	Code   string `json:"code"`
	Name   string `json:"name"`
	Webvtt string `json:"webvtt"`
}
type LanguageResp struct {
	LanguageName string `json:"languageName"`
	Endonym      string `json:"endonym"`
	LanguageCode string `json:"languageCode"`
	IanaCode     string `json:"ianaCode"`
}
