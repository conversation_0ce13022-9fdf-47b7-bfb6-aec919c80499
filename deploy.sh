#!/bin/bash

# 检查是否传递了 -f 参数
FORCE_CLEAN=false
while getopts "f" opt; do
    case $opt in
        f)
            FORCE_CLEAN=true
            ;;
        *)
            echo "Usage: $0 [-f]"
            exit 1
            ;;
    esac
done
# 停止并删除容器（忽略错误）
echo "停止并删除旧容器..."
docker stop loop || true
docker rm loop || true

# 删除旧镜像（忽略错误）
echo "删除旧镜像..."
docker rmi loop:v1 || true

# 清理悬空镜像（避免无镜像时报错）
echo "清理悬空镜像..."
docker images | grep "none" | awk '{print $3}' | xargs -r docker rmi || true

# 构建并启动服务
echo "开始构建和启动服务..."
docker compose down --rmi local  # 清理 compose 管理的镜像
docker compose up -d --build     # 强制重建镜像并启动


# 如果传递了 -f 参数，则清理缓存和未使用的资源
if [ "$FORCE_CLEAN" = true ]; then
    echo "正在强制清理docker的缓存数据和无用数据..."
    docker builder prune -f
    docker volume prune -f
    docker system prune -a -f
fi
