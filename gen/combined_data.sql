-- 合并所有数据生成脚本
-- 分类类型ID说明：
-- 1: 等级 (Level)
-- 2: 场景 (Scene)
-- 3: 主题 (Topic)

-- ==================== 1. 分类数据 ====================
-- 清空现有分类数据
DELETE FROM categories;

-- 插入等级分类（2个）
INSERT INTO categories (id, name, description, priority, category_type_id, created_at, updated_at) VALUES
('1000000000000000001', '初级', '适合英语初学者的内容', 1, '1', NOW(), NOW()),
('1000000000000000002', '中级', '适合有一定英语基础的学习者', 2, '1', NOW(), NOW());

-- 插入场景分类（2个）
INSERT INTO categories (id, name, description, priority, category_type_id, created_at, updated_at) VALUES
('1000000000000000005', '商务英语', '职场商务场景下的英语表达', 1, '2', NOW(), NOW()),
('1000000000000000006', '旅游英语', '出国旅游常用英语对话和表达', 2, '2', NOW(), NOW());

-- 插入主题分类（2个）
INSERT INTO categories (id, name, description, priority, category_type_id, created_at, updated_at) VALUES
('1000000000000000011', '语法', '英语语法规则和用法讲解', 1, '3', NOW(), NOW()),
('1000000000000000013', '发音', '英语发音技巧和练习', 2, '3', NOW(), NOW());

-- ==================== 2. 剧集数据 ====================
-- 清空现有剧集数据
DELETE FROM series;

-- 生成8个剧集（每个3分类组合），每个剧集生成zh和en两个版本
INSERT INTO series (id, cover, statement, priority, created_at, updated_at) VALUES
('2000000000000000001', 'https://img0.baidu.com/it/u=*********,2928866931&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', '初级_商务英语_语法_zh', 1, NOW(), NOW()),
('2000000000000001001', 'https://img0.baidu.com/it/u=*********,2928866931&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', '初级_商务英语_语法_en', 2, NOW(), NOW()),
('2000000000000000002', 'https://img2.baidu.com/it/u=3333164510,1393598344&fm=253&app=138&f=JPEG?w=889&h=500', '初级_商务英语_发音_zh', 3, NOW(), NOW()),
('2000000000000001002', 'https://img2.baidu.com/it/u=3333164510,1393598344&fm=253&app=138&f=JPEG?w=889&h=500', '初级_商务英语_发音_en', 4, NOW(), NOW()),
('2000000000000000003', 'https://img2.baidu.com/it/u=3361726407,2419164608&fm=253&app=138&f=JPEG?w=889&h=500', '初级_旅游英语_语法_zh', 5, NOW(), NOW()),
('2000000000000001003', 'https://img2.baidu.com/it/u=3361726407,2419164608&fm=253&app=138&f=JPEG?w=889&h=500', '初级_旅游英语_语法_en', 6, NOW(), NOW()),
('2000000000000000004', 'https://img2.baidu.com/it/u=840288949,3916195722&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', '初级_旅游英语_发音_zh', 7, NOW(), NOW()),
('2000000000000001004', 'https://img2.baidu.com/it/u=840288949,3916195722&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', '初级_旅游英语_发音_en', 8, NOW(), NOW()),
('2000000000000000005', 'https://img1.baidu.com/it/u=730698555,1138503747&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', '中级_商务英语_语法_zh', 9, NOW(), NOW()),
('2000000000000001005', 'https://img1.baidu.com/it/u=730698555,1138503747&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', '中级_商务英语_语法_en', 10, NOW(), NOW()),
('2000000000000000006', 'https://img0.baidu.com/it/u=15396030,1143539239&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500', '中级_商务英语_发音_zh', 11, NOW(), NOW()),
('2000000000000001006', 'https://img0.baidu.com/it/u=15396030,1143539239&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500', '中级_商务英语_发音_en', 12, NOW(), NOW()),
('2000000000000000007', 'https://img1.baidu.com/it/u=2712714610,638168045&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800', '中级_旅游英语_语法_zh', 13, NOW(), NOW()),
('2000000000000001007', 'https://img1.baidu.com/it/u=2712714610,638168045&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800', '中级_旅游英语_语法_en', 14, NOW(), NOW()),
('2000000000000000008', 'https://img2.baidu.com/it/u=1747872389,1158100567&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500', '中级_旅游英语_发音_zh', 15, NOW(), NOW()),
('2000000000000001008', 'https://img2.baidu.com/it/u=1747872389,1158100567&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500', '中级_旅游英语_发音_en', 16, NOW(), NOW());

-- ==================== 2.1 剧集多语言表 ====================
-- 清空现有剧集多语言数据
DELETE FROM series_relations;

-- 每个剧集插入zh和en两个语种的多语言内容
INSERT INTO series_relations (series_id, lang_code, title, description, created_at, updated_at) VALUES
('2000000000000000001', 'zh-CN', '初级_商务英语_语法', '适合初学者的商务英语语法剧集', NOW(), NOW()),
('2000000000000000001', 'en-US', 'Beginner_Business_Grammar', 'Business English grammar for beginners', NOW(), NOW()),
('2000000000000001001', 'zh-CN', '初级_商务英语_语法', '适合初学者的商务英语语法剧集', NOW(), NOW()),
('2000000000000001001', 'en-US', 'Beginner_Business_Grammar', 'Business English grammar for beginners', NOW(), NOW()),
('2000000000000000002', 'zh-CN', '初级_商务英语_发音', '适合初学者的商务英语发音剧集', NOW(), NOW()),
('2000000000000000002', 'en-US', 'Beginner_Business_Pronunciation', 'Business English pronunciation for beginners', NOW(), NOW()),
('2000000000000001002', 'zh-CN', '初级_商务英语_发音', '适合初学者的商务英语发音剧集', NOW(), NOW()),
('2000000000000001002', 'en-US', 'Beginner_Business_Pronunciation', 'Business English pronunciation for beginners', NOW(), NOW()),
('2000000000000000003', 'zh-CN', '初级_旅游英语_语法', '适合初学者的旅游英语语法剧集', NOW(), NOW()),
('2000000000000000003', 'en-US', 'Beginner_Travel_Grammar', 'Travel English grammar for beginners', NOW(), NOW()),
('2000000000000001003', 'zh-CN', '初级_旅游英语_语法', '适合初学者的旅游英语语法剧集', NOW(), NOW()),
('2000000000000001003', 'en-US', 'Beginner_Travel_Grammar', 'Travel English grammar for beginners', NOW(), NOW()),
('2000000000000000004', 'zh-CN', '初级_旅游英语_发音', '适合初学者的旅游英语发音剧集', NOW(), NOW()),
('2000000000000000004', 'en-US', 'Beginner_Travel_Pronunciation', 'Travel English pronunciation for beginners', NOW(), NOW()),
('2000000000000001004', 'zh-CN', '初级_旅游英语_发音', '适合初学者的旅游英语发音剧集', NOW(), NOW()),
('2000000000000001004', 'en-US', 'Beginner_Travel_Pronunciation', 'Travel English pronunciation for beginners', NOW(), NOW()),
('2000000000000000005', 'zh-CN', '中级_商务英语_语法', '适合中级学习者的商务英语语法剧集', NOW(), NOW()),
('2000000000000000005', 'en-US', 'Intermediate_Business_Grammar', 'Business English grammar for intermediate learners', NOW(), NOW()),
('2000000000000001005', 'zh-CN', '中级_商务英语_语法', '适合中级学习者的商务英语语法剧集', NOW(), NOW()),
('2000000000000001005', 'en-US', 'Intermediate_Business_Grammar', 'Business English grammar for intermediate learners', NOW(), NOW()),
('2000000000000000006', 'zh-CN', '中级_商务英语_发音', '适合中级学习者的商务英语发音剧集', NOW(), NOW()),
('2000000000000000006', 'en-US', 'Intermediate_Business_Pronunciation', 'Business English pronunciation for intermediate learners', NOW(), NOW()),
('2000000000000001006', 'zh-CN', '中级_商务英语_发音', '适合中级学习者的商务英语发音剧集', NOW(), NOW()),
('2000000000000001006', 'en-US', 'Intermediate_Business_Pronunciation', 'Business English pronunciation for intermediate learners', NOW(), NOW()),
('2000000000000000007', 'zh-CN', '中级_旅游英语_语法', '适合中级学习者的旅游英语语法剧集', NOW(), NOW()),
('2000000000000000007', 'en-US', 'Intermediate_Travel_Grammar', 'Travel English grammar for intermediate learners', NOW(), NOW()),
('2000000000000001007', 'zh-CN', '中级_旅游英语_语法', '适合中级学习者的旅游英语语法剧集', NOW(), NOW()),
('2000000000000001007', 'en-US', 'Intermediate_Travel_Grammar', 'Travel English grammar for intermediate learners', NOW(), NOW()),
('2000000000000000008', 'zh-CN', '中级_旅游英语_发音', '适合中级学习者的旅游英语发音剧集', NOW(), NOW()),
('2000000000000000008', 'en-US', 'Intermediate_Travel_Pronunciation', 'Travel English pronunciation for intermediate learners', NOW(), NOW()),
('2000000000000001008', 'zh-CN', '中级_旅游英语_发音', '适合中级学习者的旅游英语发音剧集', NOW(), NOW()),
('2000000000000001008', 'en-US', 'Intermediate_Travel_Pronunciation', 'Travel English pronunciation for intermediate learners', NOW(), NOW());

-- ==================== 3. 资源数据 ====================
-- 清空现有资源数据
DELETE FROM resources;

-- 每个剧集2个视频（中英文）
INSERT INTO resources (id, video_url, cover, duration, author, priority, origin_reourcelation_id, tags, created_at, updated_at) VALUES
-- 剧集视频
('3000000000000000001', 'https://example.com/videos/初级_商务英语_语法_中文.mp4', 'https://img0.baidu.com/it/u=*********,2928866931&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', 180, 'AI生成', 1, 0, '["初级", "商务英语", "语法"]', NOW(), NOW()),
('3000000000000000002', 'https://example.com/videos/初级_商务英语_语法_英文.mp4', 'https://img2.baidu.com/it/u=3333164510,1393598344&fm=253&app=138&f=JPEG?w=889&h=500', 180, 'AI生成', 2, 0, '["初级", "商务英语", "语法"]', NOW(), NOW()),
('3000000000000000003', 'https://example.com/videos/初级_商务英语_发音_中文.mp4', 'https://img2.baidu.com/it/u=3361726407,2419164608&fm=253&app=138&f=JPEG?w=889&h=500', 180, 'AI生成', 3, 0, '["初级", "商务英语", "发音"]', NOW(), NOW()),
('3000000000000000004', 'https://example.com/videos/初级_商务英语_发音_英文.mp4', 'https://img2.baidu.com/it/u=840288949,3916195722&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', 180, 'AI生成', 4, 0, '["初级", "商务英语", "发音"]', NOW(), NOW()),
('3000000000000000005', 'https://example.com/videos/初级_旅游英语_语法_中文.mp4', 'https://img1.baidu.com/it/u=730698555,1138503747&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', 180, 'AI生成', 5, 0, '["初级", "旅游英语", "语法"]', NOW(), NOW()),
('3000000000000000006', 'https://example.com/videos/初级_旅游英语_语法_英文.mp4', 'https://img0.baidu.com/it/u=15396030,1143539239&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500', 180, 'AI生成', 6, 0, '["初级", "旅游英语", "语法"]', NOW(), NOW()),
('3000000000000000007', 'https://example.com/videos/初级_旅游英语_发音_中文.mp4', 'https://img1.baidu.com/it/u=2712714610,638168045&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800', 180, 'AI生成', 7, 0, '["初级", "旅游英语", "发音"]', NOW(), NOW()),
('3000000000000000008', 'https://example.com/videos/初级_旅游英语_发音_英文.mp4', 'https://img2.baidu.com/it/u=1747872389,1158100567&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500', 180, 'AI生成', 8, 0, '["初级", "旅游英语", "发音"]', NOW(), NOW()),
('3000000000000000009', 'https://example.com/videos/中级_商务英语_语法_中文.mp4', 'https://img0.baidu.com/it/u=*********,2928866931&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', 180, 'AI生成', 9, 0, '["中级", "商务英语", "语法"]', NOW(), NOW()),
('3000000000000000010', 'https://example.com/videos/中级_商务英语_语法_英文.mp4', 'https://img2.baidu.com/it/u=3333164510,1393598344&fm=253&app=138&f=JPEG?w=889&h=500', 180, 'AI生成', 10, 0, '["中级", "商务英语", "语法"]', NOW(), NOW()),
('3000000000000000011', 'https://example.com/videos/中级_商务英语_发音_中文.mp4', 'https://img2.baidu.com/it/u=3361726407,2419164608&fm=253&app=138&f=JPEG?w=889&h=500', 180, 'AI生成', 11, 0, '["中级", "商务英语", "发音"]', NOW(), NOW()),
('3000000000000000012', 'https://example.com/videos/中级_商务英语_发音_英文.mp4', 'https://img2.baidu.com/it/u=840288949,3916195722&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', 180, 'AI生成', 12, 0, '["中级", "商务英语", "发音"]', NOW(), NOW()),
('3000000000000000013', 'https://example.com/videos/中级_旅游英语_语法_中文.mp4', 'https://img1.baidu.com/it/u=730698555,1138503747&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', 180, 'AI生成', 13, 0, '["中级", "旅游英语", "语法"]', NOW(), NOW()),
('3000000000000000014', 'https://example.com/videos/中级_旅游英语_语法_英文.mp4', 'https://img0.baidu.com/it/u=15396030,1143539239&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500', 180, 'AI生成', 14, 0, '["中级", "旅游英语", "语法"]', NOW(), NOW()),
('3000000000000000015', 'https://example.com/videos/中级_旅游英语_发音_中文.mp4', 'https://img1.baidu.com/it/u=2712714610,638168045&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800', 180, 'AI生成', 15, 0, '["中级", "旅游英语", "发音"]', NOW(), NOW()),
('3000000000000000016', 'https://example.com/videos/中级_旅游英语_发音_英文.mp4', 'https://img2.baidu.com/it/u=1747872389,1158100567&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500', 180, 'AI生成', 16, 0, '["中级", "旅游英语", "发音"]', NOW(), NOW()),
-- 不属于剧集的视频（8组，每组2个）
('3000000000000000021', 'https://example.com/videos/独立_初级_商务英语_语法_中文.mp4', 'https://img0.baidu.com/it/u=*********,2928866931&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', 120, '独立作者', 21, 0, '["初级", "商务英语", "语法"]', NOW(), NOW()),
('3000000000000000022', 'https://example.com/videos/独立_初级_商务英语_语法_英文.mp4', 'https://img2.baidu.com/it/u=3333164510,1393598344&fm=253&app=138&f=JPEG?w=889&h=500', 120, '独立作者', 22, 0, '["初级", "商务英语", "语法"]', NOW(), NOW()),
('3000000000000000023', 'https://example.com/videos/独立_初级_商务英语_发音_中文.mp4', 'https://img2.baidu.com/it/u=3361726407,2419164608&fm=253&app=138&f=JPEG?w=889&h=500', 120, '独立作者', 23, 0, '["初级", "商务英语", "发音"]', NOW(), NOW()),
('3000000000000000024', 'https://example.com/videos/独立_初级_商务英语_发音_英文.mp4', 'https://img2.baidu.com/it/u=840288949,3916195722&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', 120, '独立作者', 24, 0, '["初级", "商务英语", "发音"]', NOW(), NOW()),
('3000000000000000025', 'https://example.com/videos/独立_初级_旅游英语_语法_中文.mp4', 'https://img1.baidu.com/it/u=730698555,1138503747&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', 120, '独立作者', 25, 0, '["初级", "旅游英语", "语法"]', NOW(), NOW()),
('3000000000000000026', 'https://example.com/videos/独立_初级_旅游英语_语法_英文.mp4', 'https://img0.baidu.com/it/u=15396030,1143539239&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500', 120, '独立作者', 26, 0, '["初级", "旅游英语", "语法"]', NOW(), NOW()),
('3000000000000000027', 'https://example.com/videos/独立_初级_旅游英语_发音_中文.mp4', 'https://img1.baidu.com/it/u=2712714610,638168045&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800', 120, '独立作者', 27, 0, '["初级", "旅游英语", "发音"]', NOW(), NOW()),
('3000000000000000028', 'https://example.com/videos/独立_初级_旅游英语_发音_英文.mp4', 'https://img2.baidu.com/it/u=1747872389,1158100567&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500', 120, '独立作者', 28, 0, '["初级", "旅游英语", "发音"]', NOW(), NOW()),
('3000000000000000029', 'https://example.com/videos/独立_中级_商务英语_语法_中文.mp4', 'https://img0.baidu.com/it/u=*********,2928866931&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', 120, '独立作者', 29, 0, '["中级", "商务英语", "语法"]', NOW(), NOW()),
('3000000000000000030', 'https://example.com/videos/独立_中级_商务英语_语法_英文.mp4', 'https://img2.baidu.com/it/u=3333164510,1393598344&fm=253&app=138&f=JPEG?w=889&h=500', 120, '独立作者', 30, 0, '["中级", "商务英语", "语法"]', NOW(), NOW()),
('3000000000000000031', 'https://example.com/videos/独立_中级_商务英语_发音_中文.mp4', 'https://img2.baidu.com/it/u=3361726407,2419164608&fm=253&app=138&f=JPEG?w=889&h=500', 120, '独立作者', 31, 0, '["中级", "商务英语", "发音"]', NOW(), NOW()),
('3000000000000000032', 'https://example.com/videos/独立_中级_商务英语_发音_英文.mp4', 'https://img2.baidu.com/it/u=840288949,3916195722&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', 120, '独立作者', 32, 0, '["中级", "商务英语", "发音"]', NOW(), NOW()),
('3000000000000000033', 'https://example.com/videos/独立_中级_旅游英语_语法_中文.mp4', 'https://img1.baidu.com/it/u=730698555,1138503747&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500', 120, '独立作者', 33, 0, '["中级", "旅游英语", "语法"]', NOW(), NOW()),
('3000000000000000034', 'https://example.com/videos/独立_中级_旅游英语_语法_英文.mp4', 'https://img0.baidu.com/it/u=15396030,1143539239&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500', 120, '独立作者', 34, 0, '["中级", "旅游英语", "语法"]', NOW(), NOW()),
('3000000000000000035', 'https://example.com/videos/独立_中级_旅游英语_发音_中文.mp4', 'https://img1.baidu.com/it/u=2712714610,638168045&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800', 120, '独立作者', 35, 0, '["中级", "旅游英语", "发音"]', NOW(), NOW()),
('3000000000000000036', 'https://example.com/videos/独立_中级_旅游英语_发音_英文.mp4', 'https://img2.baidu.com/it/u=1747872389,1158100567&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500', 120, '独立作者', 36, 0, '["中级", "旅游英语", "发音"]', NOW(), NOW());

-- ==================== 4. 剧集资源关联数据 ====================
-- 清空现有剧集资源关联数据
DELETE FROM series_resource_relations;

-- 每个剧集2个视频（中英文）
INSERT INTO series_resource_relations (series_id, resource_id, created_at, updated_at) VALUES
-- 初级_商务英语_语法
('2000000000000000001', '3000000000000000001', NOW(), NOW()),
('2000000000000001001', '3000000000000001001', NOW(), NOW()),
-- 初级_商务英语_发音
('2000000000000000002', '3000000000000000003', NOW(), NOW()),
('2000000000000001002', '3000000000000001003', NOW(), NOW()),
-- 初级_旅游英语_语法
('2000000000000000003', '3000000000000000005', NOW(), NOW()),
('2000000000000001003', '3000000000000001005', NOW(), NOW()),
-- 初级_旅游英语_发音
('2000000000000000004', '3000000000000000007', NOW(), NOW()),
('2000000000000001004', '3000000000000001007', NOW(), NOW()),
-- 中级_商务英语_语法
('2000000000000000005', '3000000000000000009', NOW(), NOW()),
('2000000000000001005', '3000000000000001009', NOW(), NOW()),
-- 中级_商务英语_发音
('2000000000000000006', '3000000000000000011', NOW(), NOW()),
('2000000000000001006', '3000000000000001011', NOW(), NOW()),
-- 中级_旅游英语_语法
('2000000000000000007', '3000000000000000013', NOW(), NOW()),
('2000000000000001007', '3000000000000001013', NOW(), NOW()),
-- 中级_旅游英语_发音
('2000000000000000008', '3000000000000000015', NOW(), NOW()),
('2000000000000001008', '3000000000000001015', NOW(), NOW());

-- ==================== 5. 分类资源关联数据 ====================
-- 清空现有分类资源关联数据
DELETE FROM category_resource_relations;

-- 每个视频关联3个分类
-- 这里只写部分，实际应补全所有组合
INSERT INTO category_resource_relations (resource_id, category_id, created_at, updated_at) VALUES
('3000000000000000001', '1000000000000000001', NOW(), NOW()),
('3000000000000000001', '1000000000000000005', NOW(), NOW()),
('3000000000000000001', '1000000000000000011', NOW(), NOW()),
('3000000000000000002', '1000000000000000001', NOW(), NOW()),
('3000000000000000002', '1000000000000000005', NOW(), NOW()),
('3000000000000000002', '1000000000000000011', NOW(), NOW()),
('3000000000000000003', '1000000000000000001', NOW(), NOW()),
('3000000000000000003', '1000000000000000005', NOW(), NOW()),
('3000000000000000003', '1000000000000000013', NOW(), NOW()),
('3000000000000000004', '1000000000000000001', NOW(), NOW()),
('3000000000000000004', '1000000000000000005', NOW(), NOW()),
('3000000000000000004', '1000000000000000013', NOW(), NOW()),
('3000000000000000005', '1000000000000000001', NOW(), NOW()),
('3000000000000000005', '1000000000000000006', NOW(), NOW()),
('3000000000000000005', '1000000000000000011', NOW(), NOW()),
('3000000000000000006', '1000000000000000001', NOW(), NOW()),
('3000000000000000006', '1000000000000000006', NOW(), NOW()),
('3000000000000000006', '1000000000000000011', NOW(), NOW()),
('3000000000000000007', '1000000000000000001', NOW(), NOW()),
('3000000000000000007', '1000000000000000006', NOW(), NOW()),
('3000000000000000007', '1000000000000000013', NOW(), NOW()),
('3000000000000000008', '1000000000000000001', NOW(), NOW()),
('3000000000000000008', '1000000000000000006', NOW(), NOW()),
('3000000000000000008', '1000000000000000013', NOW(), NOW()),
('3000000000000000009', '1000000000000000002', NOW(), NOW()),
('3000000000000000009', '1000000000000000005', NOW(), NOW()),
('3000000000000000009', '1000000000000000011', NOW(), NOW()),
('3000000000000000010', '1000000000000000002', NOW(), NOW()),
('3000000000000000010', '1000000000000000005', NOW(), NOW()),
('3000000000000000010', '1000000000000000011', NOW(), NOW()),
('3000000000000000011', '1000000000000000002', NOW(), NOW()),
('3000000000000000011', '1000000000000000005', NOW(), NOW()),
('3000000000000000011', '1000000000000000013', NOW(), NOW()),
('3000000000000000012', '1000000000000000002', NOW(), NOW()),
('3000000000000000012', '1000000000000000005', NOW(), NOW()),
('3000000000000000012', '1000000000000000013', NOW(), NOW()),
('3000000000000000013', '1000000000000000002', NOW(), NOW()),
('3000000000000000013', '1000000000000000006', NOW(), NOW()),
('3000000000000000013', '1000000000000000011', NOW(), NOW()),
('3000000000000000014', '1000000000000000002', NOW(), NOW()),
('3000000000000000014', '1000000000000000006', NOW(), NOW()),
('3000000000000000014', '1000000000000000011', NOW(), NOW()),
('3000000000000000015', '1000000000000000002', NOW(), NOW()),
('3000000000000000015', '1000000000000000006', NOW(), NOW()),
('3000000000000000015', '1000000000000000013', NOW(), NOW()),
('3000000000000000016', '1000000000000000002', NOW(), NOW()),
('3000000000000000016', '1000000000000000006', NOW(), NOW()),
('3000000000000000016', '1000000000000000013', NOW(), NOW());
-- 独立视频同理补全

-- ==================== 6. 分类剧集关联数据 ====================
-- 清空现有分类剧集关联数据
DELETE FROM category_series_relations;

-- 每个剧集关联3个分类（中英文都要）
INSERT INTO category_series_relations (series_id, category_id, created_at, updated_at) VALUES
('2000000000000000001', '1000000000000000001', NOW(), NOW()),
('2000000000000000001', '1000000000000000005', NOW(), NOW()),
('2000000000000000001', '1000000000000000011', NOW(), NOW()),
('2000000000000001001', '1000000000000000001', NOW(), NOW()),
('2000000000000001001', '1000000000000000005', NOW(), NOW()),
('2000000000000001001', '1000000000000000011', NOW(), NOW()),
('2000000000000000002', '1000000000000000001', NOW(), NOW()),
('2000000000000000002', '1000000000000000005', NOW(), NOW()),
('2000000000000000002', '1000000000000000013', NOW(), NOW()),
('2000000000000001002', '1000000000000000001', NOW(), NOW()),
('2000000000000001002', '1000000000000000005', NOW(), NOW()),
('2000000000000001002', '1000000000000000013', NOW(), NOW()),
('2000000000000000003', '1000000000000000001', NOW(), NOW()),
('2000000000000000003', '1000000000000000006', NOW(), NOW()),
('2000000000000000003', '1000000000000000011', NOW(), NOW()),
('2000000000000001003', '1000000000000000001', NOW(), NOW()),
('2000000000000001003', '1000000000000000006', NOW(), NOW()),
('2000000000000001003', '1000000000000000011', NOW(), NOW()),
('2000000000000000004', '1000000000000000001', NOW(), NOW()),
('2000000000000000004', '1000000000000000006', NOW(), NOW()),
('2000000000000000004', '1000000000000000013', NOW(), NOW()),
('2000000000000001004', '1000000000000000001', NOW(), NOW()),
('2000000000000001004', '1000000000000000006', NOW(), NOW()),
('2000000000000001004', '1000000000000000013', NOW(), NOW()),
('2000000000000000005', '1000000000000000002', NOW(), NOW()),
('2000000000000000005', '1000000000000000005', NOW(), NOW()),
('2000000000000000005', '1000000000000000011', NOW(), NOW()),
('2000000000000001005', '1000000000000000002', NOW(), NOW()),
('2000000000000001005', '1000000000000000005', NOW(), NOW()),
('2000000000000001005', '1000000000000000011', NOW(), NOW()),
('2000000000000000006', '1000000000000000002', NOW(), NOW()),
('2000000000000000006', '1000000000000000005', NOW(), NOW()),
('2000000000000000006', '1000000000000000013', NOW(), NOW()),
('2000000000000001006', '1000000000000000002', NOW(), NOW()),
('2000000000000001006', '1000000000000000005', NOW(), NOW()),
('2000000000000001006', '1000000000000000013', NOW(), NOW()),
('2000000000000000007', '1000000000000000002', NOW(), NOW()),
('2000000000000000007', '1000000000000000006', NOW(), NOW()),
('2000000000000000007', '1000000000000000011', NOW(), NOW()),
('2000000000000001007', '1000000000000000002', NOW(), NOW()),
('2000000000000001007', '1000000000000000006', NOW(), NOW()),
('2000000000000001007', '1000000000000000011', NOW(), NOW()),
('2000000000000000008', '1000000000000000002', NOW(), NOW()),
('2000000000000000008', '1000000000000000006', NOW(), NOW()),
('2000000000000000008', '1000000000000000013', NOW(), NOW()),
('2000000000000001008', '1000000000000000002', NOW(), NOW()),
('2000000000000001008', '1000000000000000006', NOW(), NOW()),
('2000000000000001008', '1000000000000000013', NOW(), NOW());

-- ==================== 7. 资源关系数据 ====================
-- 清空现有资源关系数据
DELETE FROM resource_relations;
INSERT INTO resource_relations (resource_id, lang_code, title, description, subtitle_url, created_at, updated_at) VALUES
('3000000000000000001', 'zh-CN', '初级_商务英语_语法_中文', '自动生成', 'https://example.com/subtitles/初级_商务英语_语法_中文.srt', NOW(), NOW()),
('3000000000000000002', 'en-US', '初级_商务英语_语法_英文', 'Auto generated', 'https://example.com/subtitles/初级_商务英语_语法_英文.srt', NOW(), NOW()),
('3000000000000000003', 'zh-CN', '初级_商务英语_发音_中文', '自动生成', 'https://example.com/subtitles/初级_商务英语_发音_中文.srt', NOW(), NOW()),
('3000000000000000004', 'en-US', '初级_商务英语_发音_英文', 'Auto generated', 'https://example.com/subtitles/初级_商务英语_发音_英文.srt', NOW(), NOW()),
('3000000000000000005', 'zh-CN', '初级_旅游英语_语法_中文', '自动生成', 'https://example.com/subtitles/初级_旅游英语_语法_中文.srt', NOW(), NOW()),
('3000000000000000006', 'en-US', '初级_旅游英语_语法_英文', 'Auto generated', 'https://example.com/subtitles/初级_旅游英语_语法_英文.srt', NOW(), NOW()),
('3000000000000000007', 'zh-CN', '初级_旅游英语_发音_中文', '自动生成', 'https://example.com/subtitles/初级_旅游英语_发音_中文.srt', NOW(), NOW()),
('3000000000000000008', 'en-US', '初级_旅游英语_发音_英文', 'Auto generated', 'https://example.com/subtitles/初级_旅游英语_发音_英文.srt', NOW(), NOW()),
('3000000000000000009', 'zh-CN', '中级_商务英语_语法_中文', '自动生成', 'https://example.com/subtitles/中级_商务英语_语法_中文.srt', NOW(), NOW()),
('3000000000000000010', 'en-US', '中级_商务英语_语法_英文', 'Auto generated', 'https://example.com/subtitles/中级_商务英语_语法_英文.srt', NOW(), NOW()),
('3000000000000000011', 'zh-CN', '中级_商务英语_发音_中文', '自动生成', 'https://example.com/subtitles/中级_商务英语_发音_中文.srt', NOW(), NOW()),
('3000000000000000012', 'en-US', '中级_商务英语_发音_英文', 'Auto generated', 'https://example.com/subtitles/中级_商务英语_发音_英文.srt', NOW(), NOW()),
('3000000000000000013', 'zh-CN', '中级_旅游英语_语法_中文', '自动生成', 'https://example.com/subtitles/中级_旅游英语_语法_中文.srt', NOW(), NOW()),
('3000000000000000014', 'en-US', '中级_旅游英语_语法_英文', 'Auto generated', 'https://example.com/subtitles/中级_旅游英语_语法_英文.srt', NOW(), NOW()),
('3000000000000000015', 'zh-CN', '中级_旅游英语_发音_中文', '自动生成', 'https://example.com/subtitles/中级_旅游英语_发音_中文.srt', NOW(), NOW()),
('3000000000000000016', 'en-US', '中级_旅游英语_发音_英文', 'Auto generated', 'https://example.com/subtitles/中级_旅游英语_发音_英文.srt', NOW(), NOW());
-- 独立视频同理补全

-- ==================== 8. 精选内容数据 ====================
-- 清空现有精选内容数据
DELETE FROM featured_contents;

-- 插入精选内容数据（content_type: 1=剧集，2=视频），剧集部分补全中英文
INSERT INTO featured_contents (id, content_id, content_type, lang_code, created_at, updated_at) VALUES
('4000000000000000001', '2000000000000000001', 1, 'zh-CN', NOW(), NOW()),
('4000000000000001001', '2000000000000001001', 1, 'en-US', NOW(), NOW()),
('4000000000000000002', '2000000000000000005', 1, 'zh-CN', NOW(), NOW()),
('4000000000000001002', '2000000000000001005', 1, 'en-US', NOW(), NOW()),
('4000000000000000003', '3000000000000000001', 2, 'zh-CN', NOW(), NOW()),
('4000000000000000004', '3000000000000000002', 2, 'en-US', NOW(), NOW()),
('4000000000000000005', '3000000000000000008', 2, 'en-US', NOW(), NOW());