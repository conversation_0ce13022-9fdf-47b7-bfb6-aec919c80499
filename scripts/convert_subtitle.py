import re
import argparse

def convert_srt(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    converted_lines = []
    buffer = []
    is_text_block = False

    for line in lines:
        if re.match(r'^\d+$', line.strip()):
            if buffer:
                converted_lines.append(' '.join(buffer))
                buffer = []
            converted_lines.append(line.strip())
            is_text_block = False
        elif re.match(r'^\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3}$', line.strip()):
            if buffer:
                converted_lines.append(' '.join(buffer))
                buffer = []
            converted_lines.append(line.strip())
            is_text_block = True
        else:
            if is_text_block and line.strip():
                buffer.append(line.strip().lower())
            elif line.strip() == '':
                if buffer:
                    converted_lines.append(' '.join(buffer))
                    buffer = []
                converted_lines.append('')

    if buffer:
        converted_lines.append(' '.join(buffer))

    with open(output_file, 'w', encoding='utf-8') as file:
        for line in converted_lines:
            file.write(line + '\n')

def main():
    parser = argparse.ArgumentParser(description='Convert multi-line subtitles in SRT to single line.')
    parser.add_argument('input_file', help='Path to the input SRT file')
    parser.add_argument('output_file', help='Path to the output SRT file')

    args = parser.parse_args()
    convert_srt(args.input_file, args.output_file)

if __name__ == '__main__':
    main()