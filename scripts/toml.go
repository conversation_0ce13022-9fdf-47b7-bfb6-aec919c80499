package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"
	"text/template"
)

// 定义一个模板字符串来生成 Go 代码
const goTemplateStr = `// Code generated by go script; DO NOT EDIT.
package i18n

{{- range $key, $value := . }}
var {{ $key }} = {{ $value }}
{{- end }}
`

// transformValue 检查字符串值，并在发现 "#%" 时替换为 "{#KeyName}"
func transformValue(key, value string) string {
	if strings.Contains(value, "%") {
		// 将 "#%" 替换为 "{#KeyName}"
		return fmt.Sprintf("`{#%s}`", key)
	}
	// 直接返回键名
	return fmt.Sprintf("`%s`", key)
}

func main() {
	// 打开 TOML 文件
	file, err := os.Open("../i18n/en.toml")
	if err != nil {
		log.Fatal(err)
	}
	defer file.Close()

	keyValuePairs := make(map[string]string)
	scanner := bufio.NewScanner(file)

	// 读取文件并保存键值对
	for scanner.Scan() {
		line := scanner.Text()
		if line == "" || strings.HasPrefix(line, "#") {
			continue // 忽略空行和注释
		}
		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(strings.Trim(parts[1], `"`)) // 移除值周围的引号
			transformedValue := transformValue(key, value)
			keyValuePairs[key] = transformedValue
		}
	}

	if err := scanner.Err(); err != nil {
		log.Fatal(err)
	}

	// 解析模板
	tmpl, err := template.New("go-code").Parse(goTemplateStr)
	if err != nil {
		log.Fatal(err)
	}

	// 创建 Go 文件
	outputFile, err := os.Create("../pkg/i18n/var.go")
	if err != nil {
		log.Fatal(err)
	}
	defer outputFile.Close()

	// 执行模板以填充数据
	err = tmpl.Execute(outputFile, keyValuePairs)
	if err != nil {
		log.Fatal(err)
	}

	log.Println("Go file created successfully.")
}
