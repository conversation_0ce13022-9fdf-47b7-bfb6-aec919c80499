# 数据初始化脚本

## 概述

`init_admin_data.go` 是一个Go脚本，用于替代原来的SQL文件(`init/admin.sql`)来初始化系统的基础数据。

## 功能

该脚本会初始化以下数据：

1. **系统角色** (`sys_roles`)
   - admin (管理员角色，等级2)
   - normal (普通角色，等级0)

2. **系统用户** (`sys_users`)
   - admin用户 (用户名: admin, 密码已加密)

3. **分类数据** (`categories`)
   - 等级分类：零基础、中级、高级
   - 场景分类：演讲、考试、面试、出国、写作
   - 主题分类：TED、YouTube、博客、影视、书籍

4. **会员等级** (`vips`)
   - 普通会员 (等级1)
   - PRO会员 (等级100)
   - ULTRA会员 (等级1000)

5. **商品信息** (`trade_products`)
   - Android端商品：月卡、年卡、终身会员
   - iOS端商品：月卡、年卡、终身会员

6. **权益组** (`benefit_groups`)
   - 视频大小限制组
   - AI调用次数组
   - 字幕对话次数组

7. **权益信息** (`benefits`)
   - 每个会员等级对应的不同权益

8. **会员权益关联** (`vip_benefits`)
   - 会员等级与权益的关联关系

## 使用方法

### 1. 编译脚本

```bash
cd scripts
go build -o init_admin_data init_admin_data.go
```

### 2. 运行脚本

```bash
./init_admin_data
```

### 3. 或者直接运行Go文件

```bash
go run init_admin_data.go
```

### 4. 运行示例

成功运行后会看到类似输出：
```
Admin data initialization completed successfully!
```

脚本会自动：
- 创建所有必要的数据库表（如果不存在）
- 清理现有数据
- 插入初始化数据
- 建立正确的关联关系

## 配置要求

- 脚本会自动读取 `../configs/config.yaml` 配置文件
- 确保数据库连接配置正确
- 确保数据库已创建并且表结构已存在（通过GORM AutoMigrate创建）

## 注意事项

1. **数据清理**: 脚本会在插入新数据前删除对应表的现有数据
2. **依赖关系**: 脚本按照正确的顺序初始化数据，确保外键关系正确
3. **错误处理**: 如果任何步骤失败，脚本会停止执行并显示错误信息
4. **幂等性**: 可以多次运行脚本，每次都会重新初始化数据

## 与SQL文件的对比

### 优势
- **类型安全**: 使用Go结构体，编译时检查类型
- **维护性**: 代码结构清晰，易于维护和修改
- **可扩展**: 容易添加新的初始化逻辑
- **错误处理**: 更好的错误处理和日志记录

### SQL文件的问题
- 硬编码的ID值
- 复杂的子查询
- 难以维护和修改
- 缺乏类型检查

## 开发建议

如果需要添加新的初始化数据：

1. 在对应的初始化函数中添加数据
2. 确保遵循正确的依赖顺序
3. 添加适当的错误处理
4. 测试脚本确保正常工作

## 故障排除

如果遇到问题：

1. 检查数据库连接配置
2. 确保数据库表已存在
3. 检查日志输出的错误信息
4. 确认配置文件路径正确
