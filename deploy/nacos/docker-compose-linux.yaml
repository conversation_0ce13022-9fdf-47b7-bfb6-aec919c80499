version: '3.3'

services:
  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: nacos-standalone
    environment:
      - TZ=Asia/Shanghai
      - PREFER_HOST_MODE=hostname
      - MODE=standalone
      - MYSQL_SERVICE_HOST=mysql
      - MYSQL_SERVICE_DB_NAME=nacos
      - MYSQL_SERVICE_PORT=3306
      - MYSQL_SERVICE_DB_PARAM=characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useUnicode=true&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
      - NACOS_AUTH_ENABLE=true
      - NACOS_AUTH_IDENTITY_KEY=mikaelzero
      - NACOS_AUTH_IDENTITY_VALUE=mikaelzero
      - NACOS_AUTH_TOKEN=SecretKey012345678901234567890123456789012345678901234567890123456789
    # volumes:
    #   - ./standalone-logs/:/home/<USER>/logs
    ports:
      - "8848:8848"