#!/bin/bash
# init-grafana.sh

# 等待 Grafana 启动
echo "Waiting for <PERSON><PERSON> to start..."
while ! nc -z grafana 3000; do
  sleep 1
done
echo "<PERSON><PERSON> started"

# Grafana credentials
GRAFANA_USER='admin'
GRAFANA_PASSWORD='111111'
GRAFANA_URL='http://grafana:3000'


curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Grafana111",
    "role": "admin"
  }' \
  http://grafana:3000/api/auth/keys

# 登录并获取 session cookie
# login_response=$(curl -s -H 'Content-Type: application/json' -X POST -d "{\"user\":\"${GRAFANA_USER}\",\"password\":\"${GRAFANA_PASSWORD}\"}" ${GRAFANA_URL}/login)
login_response=$(curl --location --cookie-jar cookies.txt --header 'Content-Type: application/x-www-form-urlencoded' --data-urlencode 'user=admin' --data-urlencode 'password=111111' http://grafana:3000/login)

echo "login_response = $login_response"

# 从响应中提取 auth cookie
auth_cookie=$(echo $login_response | grep -o 'grafana_sess=[^;]*')

echo "auth_cookie = $auth_cookie"

curl -v -X POST "********************************/api/alert-notifications" \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer ${auth_cookie}" \
  -d '{
    "name": "Gmail-Alerts",
    "type": "email",
    "settings": {
      "addresses": "<EMAIL>"
    }
  }'
