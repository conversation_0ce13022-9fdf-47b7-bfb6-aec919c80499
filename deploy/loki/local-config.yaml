ruler:
  # 触发告警事件后的回调查询地址
  # 如果用grafana的话就配置成grafana/explore
  external_url: grafana/explore

  # alertmanager地址
  # alertmanager_url: <alertmanager_endpoint>
  # enable_alertmanager_v2: true

  # 启用loki rules API
  enable_api: true

  # 对rules分片，支持ruler多实例
  enable_sharding: true

  # ruler服务的一致性哈希环配置，用于支持多实例和分片
  ring:
    kvstore:
      consul:
        host: <consul-endpoint>:8500
      store: consul

  # rules临时规则文件存储路径
  rule_path: /tmp/rules

  # rules规则存储
  # 主要支持本地存储（local）和对象文件系统（azure, gcs, s3, swift）
  storage:
    type: local
    local:
      directory: /loki/rules

  # rules规则加载时间
  flush_period: 1m
