
  global:
    scrape_interval:     15s #  # 默认抓取周期
    evaluation_interval: 15s # Evaluate rules every 15 seconds. The default is every 1 minute.
    # scrape_timeout is set to the global default (10s).

  # Alertmanager configuration 告警地址
  alerting:
    alertmanagers:
    - static_configs:
      - targets: ['192.168.21.23:9093']
  # Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
  rule_files:
    - "/opt/bitnami/prometheus/conf/group.yml"
  # A scrape configuration containing exactly one endpoint to scrape:
  # Here it's Prometheus itself.
  scrape_configs:
    - job_name: '服务' #服务的名称
      scrape_interval: 5s
      metrics_path: /metrics  #获取指标的url
      file_sd_configs: #替换static_configs
        - files:
          - /opt/prometheus/config/nodes/nodes.json #指定文件路径
          refresh_interval: 10s #重新加载文件列表目标时间间隔 