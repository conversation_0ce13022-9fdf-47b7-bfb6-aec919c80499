version: '3.5'
# 网络配置
networks:
  backend:
    driver: ${NETWORKS_DRIVER}

# 服务容器配置
services:
  prometheus:
    build:
      context: ./prometheus
    environment:
      - TZ=${TZ}
    privileged: true
    volumes:
      - ${DATA_PATH_HOST}/prometheus/prometheus:/etc/prometheus
      - ${DATA_PATH_HOST}/prometheus/prometheus_data:/prometheus
      - ./prometheus/nodes.json:/opt/prometheus/config/nodes/nodes.json
      - ./prometheus/prometheus.yml:/opt/bitnami/prometheus/conf/prometheus.yml  # 将 prometheus 配置文件挂载到容器里
      - ./prometheus/group.yml:/opt/bitnami/prometheus/conf/group.yml  # 将 prometheus 配置文件挂载到容器里
    ports:
      - "${PROMETHEUS_PORT}:9090"                     # 设置容器9090端口映射指定宿主机端口，用于宿主机访问可视化web
    networks:
      - backend
    restart: always

  # 添加告警模块
  alertmanager:
    build:
      context: ./alertmanager
    container_name: alertmanager
    ports:
      - "${ALERTMANAGER_PORT}:9093"
    volumes:
      - "./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml"
      - ${DATA_PATH_HOST}/prometheus/data/alertmanager:/alertmanager/data
      - "./alertmanager/templetes:/etc/alertmanager/templates"
    networks:
      - backend
    restart: always

  grafana:
    build:
      context: ./grafana
    user: root
    environment:
      - TZ=${TZ}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SECURITY_ADMIN_PASSWORD=111111
    volumes:
      - ${DATA_PATH_HOST}/grafana/grafana_data:/var/lib/grafana
    ports:
      - "${GRAFANA_PORT}:3000"                        # 设置容器3000端口映射指定宿主机端口，用于宿主机访问可视化web
    networks:
      - backend
    restart: always