version: '3.3'

services:
  mysql:
    container_name: mysql
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=loop
    ports:
      - "3306:3306"
    networks:
      - app-network
  #redis容器 - Redis container
  redis:
    image: redis:latest
    container_name: redis
    ports:
      - "6379:6379"
    environment:
      TZ: Asia/Shanghai
    command: >
      redis-server 
      --requirepass 123456 
      --appendonly yes
      --bind 0.0.0.0
      --protected-mode no
    volumes:
      - redis_data:/data
    privileged: true
    restart: always
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  redis_data:
  