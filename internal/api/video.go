package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewVideoApi(repo *data.VideoRepo, settingRepo *data.SettingRepo, model *model.DbModel) *VideoApi {
	return &VideoApi{repo: repo, settingRepo: settingRepo}
}

type VideoApi struct {
	Apis
	repo        *data.VideoRepo
	settingRepo *data.SettingRepo
}

func (a *VideoApi) UpdateCutsomSubtitle(c *gin.Context) {
	var req request.SubtitleUpdateReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.UpdateCutsomSubtitle(c, req)
	})
}

func (a *VideoApi) UpdateVideoDetail(c *gin.Context) {
	var req request.VideoDetailUpdateReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.UpdateVideoDetail(c, req)
	})
}

func (a *VideoApi) GetVideoLocalDetail(c *gin.Context) {
	var req request.VideoDetailReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetVideoDetail(c, req)
	})
}
func (a *VideoApi) ChangeResourceName(c *gin.Context) {
	var req request.ResourceNameModReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.ChangeResourceName(c, req)
	})
}
