package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewUserAdminApi(repo *data.UserAdminRepo, model *model.DbModel) *UserAdminApi {
	return &UserAdminApi{repo: repo, model: model}
}

type UserAdminApi struct {
	Apis
	model *model.DbModel
	repo  *data.UserAdminRepo
}

func (a *UserAdminApi) LoginAdmin(c *gin.Context) {
	var req request.UserAdminLoginReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.LoginAdmin(c, req)
	})
}

func (a *UserAdminApi) Delete(c *gin.Context) {
	var req request.IdReq
	a.processRequestQuery(c, &req, func(r any) any {
		return data.DeleteById[model.User](a.model, req.Id)
	})
}
func (a *UserAdmin<PERSON>pi) DeleteMulti(c *gin.Context) {
	var req request.IdsReq
	a.processRequestQuery(c, &req, func(r any) any {
		return data.DeleteMultiId[model.User](a.model, req.Id)
	})
}

func (a *UserAdminApi) GetList(c *gin.Context) {
	var req request.ListReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetUserList(c, req)
	})
}
