package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewCategoryTypeApi(repo *data.CategoryTypeRepo, model *model.DbModel) *CategoryTypeApi {
	return &CategoryTypeApi{repo: repo, model: model}
}

type CategoryTypeApi struct {
	Apis
	model *model.DbModel
	repo  *data.CategoryTypeRepo
}

func (a *CategoryTypeApi) GetById(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return data.GetOne[model.CategoryType](a.model, req.Id)
	})
}

func (a *CategoryTypeApi) GetAll(c *gin.Context) {
	var req request.EmptyReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetAll(c)
	})
}
