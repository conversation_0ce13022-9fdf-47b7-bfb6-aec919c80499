package api

import (
	"loop/internal/data"
	"loop/internal/request"
	"loop/pkg/ted"
	"loop/pkg/web"
	"net/http"

	"github.com/gin-gonic/gin"
)

func NewV1Api(repo *data.V1Repo) *V1Api {
	return &V1Api{repo: repo}
}

type V1Api struct {
	Apis
	repo *data.V1Repo
}

func (a *V1Api) GetTedSource(c *gin.Context) {
	var req request.TedReq
	if err := c.ShouldBind(&req); err != nil {
		AbortBadRequest(c, err)
		return
	}

	tedResp, err := ted.ParseTedJson(req.Url)
	if err != nil {
		c.JSON(http.StatusOK, web.JsonInternalError(err))
	}
	c.JSON(http.StatusOK, web.JsonData(tedResp))
}
func (a *V1Api) GetLangs(c *gin.Context) {
	a.processRequest(c, nil, func(r any) any {
		return a.repo.GetLangs()
	})
}
func (a *V1Api) GetNativeLangs(c *gin.Context) {
	a.processRequest(c, nil, func(r any) any {
		return a.repo.GetNativeLangs()
	})
}
func (a *V1Api) GetTargetLangs(c *gin.Context) {
	a.processRequest(c, nil, func(r any) any {
		return a.repo.GetTargetLangs()
	})
}
