package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewResourceApi(repo *data.ResourceRepo, model *model.DbModel) *ResourceApi {
	return &ResourceApi{repo: repo, model: model}
}

type ResourceApi struct {
	Apis
	model *model.DbModel
	repo  *data.ResourceRepo
}

func (a *ResourceApi) GetResourceHome(c *gin.Context) {
	var req request.EmptyReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetResourceHome(c, req)
	})
}

func (a *ResourceApi) GetResourceHomeItems(c *gin.Context) {
	var req request.ResourceHomeResourceReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetResourceHomeByCategory(c, req)
	})
}

// GetResourceTags 获取资源的标签
func (a *ResourceApi) GetResourceTags(c *gin.Context) {
	var req request.ResourceTagsReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetResourceTags(c, req)
	})
}

// SetResourceTags 设置资源的标签
func (a *ResourceApi) SetResourceTags(c *gin.Context) {
	var req request.SetResourceTagsReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.SetResourceTags(c, req)
	})
}

// AddResourceTag 添加资源标签
func (a *ResourceApi) AddResourceTag(c *gin.Context) {
	var req request.ResourceTagReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.AddResourceTag(c, req)
	})
}

// RemoveResourceTag 移除资源标签
func (a *ResourceApi) RemoveResourceTag(c *gin.Context) {
	var req request.ResourceTagReq
	a.processRequestQuery(c, &req, func(r any) any {
		return a.repo.RemoveResourceTag(c, req)
	})
}

// GetResourcesByTags 根据标签获取资源
func (a *ResourceApi) GetResourcesByTags(c *gin.Context) {
	var req request.ResourcesByTagsReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetResourcesByTags(c, req)
	})
}

// GetAllResources 获取所有资源
// @Summary 获取所有资源
// @Description 获取所有资源，支持分页和语言筛选
// @Tags 资源
// @Accept json
// @Produce json
// @Param langCode query string false "语言代码，可选，如果不提供则使用请求头中的目标语言"
// @Param page query int false "页码，从1开始，默认为1"
// @Param pageSize query int false "每页数量，默认为20，最大为100"
// @Success 200 {object} web.JsonResult{data=response.PagedResourcesResp}
// @Router /api/v1/resources/all [get]
func (a *ResourceApi) GetAllResources(c *gin.Context) {
	var req request.GetAllResourcesReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetAllResources(c, req)
	})
}
