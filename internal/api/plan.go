package api

import (
	"loop/internal/data"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewPlanApi(repo *data.PlanRepo) *PlanApi {
	return &PlanApi{repo: repo}
}

type PlanApi struct {
	Apis
	repo *data.PlanRepo
}

// GetQuestionnaire 获取用户的问卷信息
// @Summary 获取用户问卷
// @Description 获取用户的学习问卷信息
// @Tags 学习计划
// @Accept json
// @Produce json
// @Success 200 {object} web.JsonResult{data=response.UserQuestionnaireResp}
// @Router /api/v1/plan/questionnaire [get]
func (a *PlanApi) GetQuestionnaire(c *gin.Context) {
	a.processRequest(c, nil, func(r any) any {
		return a.repo.GetQuestionnaire(c)
	})
}

// GeneratePlan 为用户生成学习计划
// @Summary 生成学习计划
// @Description 基于用户问卷生成学习计划
// @Tags 学习计划
// @Accept json
// @Produce json
// @Param request body request.GeneratePlanReq false "生成计划参数"
// @Success 200 {object} web.JsonResult{data=response.LearningPlanResp}
// @Router /api/v1/plan/generate [post]
func (a *PlanApi) GeneratePlan(c *gin.Context) {
	var req request.GeneratePlanReq
	a.processRequest(c, &req, func(r any) any {
		reqTyped := r.(*request.GeneratePlanReq)
		return a.repo.GeneratePlan(c, *reqTyped)
	})
}

// GetPlan 获取用户当前活跃的学习计划
// @Summary 获取学习计划
// @Description 获取用户当前活跃的学习计划
// @Tags 学习计划
// @Accept json
// @Produce json
// @Param planId query string false "计划ID，如果不提供则获取用户当前活跃的计划"
// @Success 200 {object} web.JsonResult{data=response.LearningPlanResp}
// @Router /api/v1/plan [get]
func (a *PlanApi) GetPlan(c *gin.Context) {
	var req request.GetPlanReq
	a.processRequest(c, &req, func(r any) any {
		reqTyped := r.(*request.GetPlanReq)
		return a.repo.GetPlan(c, *reqTyped)
	})
}

// InvalidatePlan 使用户的学习计划失效
// @Summary 使学习计划失效
// @Description 使用户的学习计划失效
// @Tags 学习计划
// @Accept json
// @Produce json
// @Param request body request.InvalidatePlanReq false "使计划失效参数"
// @Success 200 {object} web.JsonResult
// @Router /api/v1/plan/invalidate [post]
func (a *PlanApi) InvalidatePlan(c *gin.Context) {
	var req request.InvalidatePlanReq
	a.processRequest(c, &req, func(r any) any {
		reqTyped := r.(*request.InvalidatePlanReq)
		return a.repo.InvalidatePlan(c, *reqTyped)
	})
}
