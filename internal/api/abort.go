package api

import (
	"net/http"

	"loop/pkg/i18n"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
)

func Abort(c *gin.Context, code int, msg string) {
	resp := web.JsonResult{
		Code: code,
		Msg:  i18n.T(c, msg),
	}
	c.AbortWithStatusJSON(code, resp)
}

func AbortWithErr(c *gin.Context, code int, msg string, err error) {
	resp := web.JsonResult{
		Code: code,
		Msg:  i18n.T(c, msg),
	}
	// 生产环境隐藏底层报错
	if err != nil && gin.Mode() != gin.ReleaseMode {
		resp.Error = err.Error()
	}
	c.AbortWithStatusJSON(code, resp)
}

// AbortUnauthorized aborts with status code 401.
func AbortUnauthorized(c *gin.Context) {
	Abort(c, http.StatusUnauthorized, i18n.ErrUnauthorized)
}

// AbortForbidden aborts with status code 403.
func AbortForbidden(c *gin.Context) {
	Abort(c, http.StatusForbidden, i18n.ErrForbidden)
}

// AbortNotFound aborts with status code 404.
func AbortNotFound(c *gin.Context) {
	Abort(c, http.StatusNotFound, i18n.ErrNotFound)
}
func AbortBadRequest(c *gin.Context, e error) {
	AbortWithErr(c, http.StatusBadRequest, i18n.ErrBadRequest, e)
}

// AbortEntityNotFound aborts with status code 404.
func AbortEntityNotFound(c *gin.Context) {
	Abort(c, http.StatusNotFound, i18n.ErrEntityNotFound)
}
