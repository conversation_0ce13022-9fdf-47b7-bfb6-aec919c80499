package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/jwtx"
	"loop/pkg/web"
	"net/http"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

func NewUserApi(repo *data.UserRepo, settingRepo *data.SettingRepo, model *model.DbModel) *UserApi {
	return &UserApi{repo: repo, settingRepo: settingRepo, model: model}
}

type UserApi struct {
	Apis
	model       *model.DbModel
	repo        *data.UserRepo
	settingRepo *data.SettingRepo
}

func (a *UserApi) Login(c *gin.Context) {
	var req request.UserLoginReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.Login(c, req)
	})
}

func (a *UserApi) Register(c *gin.Context) {
	var req request.UserRegisterReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.Register(c, req)
	})
}

func (a *UserApi) LoginByApple(c *gin.Context) {
	var req request.AppleLoginReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.LoginByApple(c, req)
	})
}

func (a *UserApi) Update(c *gin.Context) {
	var req request.UpdateUserInfoReq
	req.Id = jwtx.GetUid(c)
	a.processRequest(c, &req, func(r any) any {
		return data.UpdateOne[model.User](a.model, req)
	})
}

func (a *UserApi) GetById(c *gin.Context) {
	uid := jwtx.GetUid(c)
	req := request.IdReq{Id: uid}
	a.processRequest(c, &req, func(r any) any {
		return data.GetOneWithResp[model.User, response.UserInfoResp](a.model, req.Id)
	})
}
func (a *UserApi) FetchConfig(c *gin.Context) {
	res := a.settingRepo.FetchConfig(c)

	if res == nil {
		AbortEntityNotFound(c)
		return
	}
	c.JSON(http.StatusOK, res)
}
func (a *UserApi) UpdateUserPlayerConfig(c *gin.Context) {
	var req request.UpdateUserPlayerConfigRequest
	a.processRequestJson(c, &req, func(r any) any {
		return a.settingRepo.UpdateUserPlayerConfig(c, req)
	})
}

func (a *UserApi) GetWatchHistory(c *gin.Context) {
	var req request.ListReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetWatchHistory(c, req)
	})
}

func (a *UserApi) DeleteWatchHistory(c *gin.Context) {
	var req request.WatchHistoryDeleteReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.DeleteWatchHistory(c, req)
	})
}

func (a *UserApi) CurrentUser(c *gin.Context) (*response.UserInfoResp, error) {
	return a.repo.CurrentUser(c)
}

// UserLogout 用户登出
func (a *UserApi) Logout(c *gin.Context) {
	s := sessions.Default(c)
	s.Clear()
	s.Save()
	c.JSON(200, web.JsonResult{
		Code: 0,
		Msg:  "登出成功",
	})
}

func (a *UserApi) DeleteAccount(c *gin.Context) {
	var req request.EmptyReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.DeleteAccount(c)
	})
}

type SubtitleReq struct {
	Url string `form:"url" json:"url" binding:"required"`
}

// GetUserBenefits
// @Summary 获取用户权益列表
// @Description 获取当前用户的所有权益列表
// @Tags 用户
// @Accept json
// @Produce json
// @Success 200 {object} web.JsonResult{data=[]model.UserBenefit}
// @Router /api/v1/user/benefits [get]
func (a *UserApi) GetUserBenefits(c *gin.Context) {
	a.processRequest(c, nil, func(r any) any {
		return a.repo.GetUserBenefits(c)
	})
}

// GetBenefitsByGroup
// @Summary 获取用户指定组的权益列表
// @Description 获取当前用户指定权益组的所有权益列表
// @Tags 用户
// @Accept json
// @Produce json
// @Param groupCode query string true "权益组编码"
// @Success 200 {object} web.JsonResult{data=[]model.UserBenefit}
// @Router /api/v1/user/benefits/group [get]
func (a *UserApi) GetBenefitsByGroup(c *gin.Context) {
	var req request.GetUserBenefitsByGroupReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetBenefitsByGroup(c, req)
	})
}

// ConsumeBenefit
// @Summary 消耗用户权益
// @Description 消耗指定数量的用户权益
// @Tags 用户

func (a *UserApi) UpdateUserVideoLimit(c *gin.Context) {
	var req request.UpdateUserVideoLimitReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.UpdateUserVideoLimit(c, req)
	})
}

// UpdateUserAICallLimit
// @Summary 更新AI调用次数限制
// @Description 扣减用户的AI调用次数权益
// @Tags 用户
// @Accept json
// @Produce json
// @Param request body request.UpdateUserAICallLimitReq true "请求参数"
// @Success 200 {object} web.JsonResult
// @Router /api/v1/user/ai-call-limit [post]
func (a *UserApi) UpdateUserAICallLimit(c *gin.Context) {
	var req request.UpdateUserAICallLimitReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.UpdateUserAICallLimit(c, req)
	})
}
