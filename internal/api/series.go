package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewSeriesApi(repo *data.SeriesRepo, model *model.DbModel) *SeriesApi {
	return &SeriesApi{repo: repo, model: model}
}

type SeriesApi struct {
	Apis
	model *model.DbModel
	repo  *data.SeriesRepo
}

func (a *SeriesApi) GetSeriesDetail(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetSeriesDetail(c, req)
	})
}
