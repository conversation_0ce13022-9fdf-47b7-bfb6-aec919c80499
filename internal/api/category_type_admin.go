package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewCategoryTypeAdminApi(repo *data.CategoryTypeAdminRepo, model *model.DbModel) *CategoryTypeAdminApi {
	return &CategoryTypeAdminApi{repo: repo, model: model}
}

type CategoryTypeAdminApi struct {
	Apis
	model *model.DbModel
	repo  *data.CategoryTypeAdminRepo
}

func (a *CategoryTypeAdminApi) Add(c *gin.Context) {
	var req request.ResCategoryTypeReq
	a.processRequest(c, &req, func(r any) any {
		return data.SaveOneOrUpdate[model.CategoryType](a.model, c, req, req.Id)
	})
}

func (a *CategoryTypeAdminApi) GetById(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return data.GetOne[model.CategoryType](a.model, req.Id)
	})
}

func (a *CategoryTypeAdminApi) GetList(c *gin.Context) {
	var req request.ListReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetList(c, req)
	})
}

func (a *CategoryTypeAdminApi) GetAll(c *gin.Context) {
	var req request.EmptyReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetAll(c)
	})
}

func (a *CategoryTypeAdminApi) Delete(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.Delete(c, req)
	})
}

func (a *CategoryTypeAdminApi) DeleteMulti(c *gin.Context) {
	var req request.IdsReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.DeleteMulti(c, req)
	})
}

func (a *CategoryTypeAdminApi) SetPriority(c *gin.Context) {
	var req request.PriorityReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.SetPriority(c, req)
	})
}
