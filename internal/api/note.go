package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewNoteApi(repo *data.NotesRepo, model *model.DbModel) *NoteApi {
	return &NoteApi{repo: repo, model: model}
}

type NoteApi struct {
	Apis
	model *model.DbModel
	repo  *data.NotesRepo
}

func (a *NoteApi) AddOrUpdateLocalNote(c *gin.Context) {
	var req request.NotesAddReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.AddOrUpdateNote(c, req)
	})
}
func (a *NoteApi) DeleteLocalNote(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.DeleteNote(c, req)
	})
}

func (a *NoteApi) GetLocalNoteList(c *gin.Context) {
	var req request.NotesReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetNoteList(c, req)
	})
}

func (a *NoteApi) GetLocalNote(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetNote(c, req)
	})
}
