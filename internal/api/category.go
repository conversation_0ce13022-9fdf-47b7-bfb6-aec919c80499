package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewCategoryApi(repo *data.CategoryRepo, model *model.DbModel) *CategoryApi {
	return &CategoryApi{repo: repo, model: model}
}

type CategoryApi struct {
	Apis
	model *model.DbModel
	repo  *data.CategoryRepo
}

func (a *CategoryApi) GetById(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return data.GetOne[model.Category](a.model, req.Id)
	})
}
