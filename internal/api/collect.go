package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewCollectApi(repo *data.CollectRepo, settingRepo *data.SettingRepo, model *model.DbModel) *CollectApi {
	return &CollectApi{repo: repo, settingRepo: settingRepo, model: model}
}

type CollectApi struct {
	Apis
	model       *model.DbModel
	repo        *data.CollectRepo
	settingRepo *data.SettingRepo
}

func (a *CollectApi) AddNoteCollect(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.AddNoteCollect(c, req)
	})
}
func (a *CollectApi) DeleteLocalNoteCollect(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.DeleteLocalNoteCollect(c, req)
	})
}

func (a *CollectApi) AddSentenceCollect(c *gin.Context) {
	var req request.SentenceCollectAddReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.AddSentenceCollect(c, req)
	})
}

func (a *CollectApi) RemoveSentenceCollect(c *gin.Context) {
	var req request.SentenceCollectAddReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.RemoveSentenceCollect(c, req)
	})
}
