package model

import (
	"loop/internal/config"

	"go4.org/syncutil/singleflight"
)

type Note struct {
	Model
	Uid            string `gorm:"not null;index:idx_user_watch,priority:1" json:"uid"`
	ResourceId     string `gorm:"not null"`               // 视频资源ID
	ResourceType   int    `gorm:"type:tinyint;default:0"` //视频类型 1代表远程的资源 2代表本地资源
	Content        string `gorm:"type:LONGTEXT" json:"content"`
	VideoStartTime int64  `gorm:"type:bigint;default:0" json:"videoStartTime"` // 视频中的开始时间
	VideoEndTime   int64  `gorm:"type:bigint;default:0" json:"videoEndTime"`   // 视频中的结束时间
}

func (Note) TableName() string {
	return "notes"
}

func NewNoteModel(dbModel *DbModel, config *config.Config) *NoteModel {
	return &NoteModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type NoteModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func (r *NoteModel) GetNoteList(uid string, resourceId string, resourceType int) ([]*Note, error) {
	var userLocalNotesList []*Note
	err := r.GetList(&userLocalNotesList, &Note{Uid: uid, ResourceId: resourceId, ResourceType: resourceType})
	if err != nil {
		return nil, err
	}
	return userLocalNotesList, nil
}

// func (r *NoteModel) UpdateIndexes(uid string, subtitleId string, indexChangeMap map[string]int) error {
// 	var notes []*Note
// 	err := r.GetList(&notes, &Note{Uid: uid, UserCustomSubtitleId: subtitleId})
// 	if err != nil {
// 		return err
// 	}
// 	if len(notes) == 0 {
// 		return nil
// 	}
// 	// 将 indexChangeMap 从 map[string]int 转换为 map[int]int
// 	intIndexChangeMap := make(map[int]int)
// 	newIndexSet := make(map[int]bool) // 用于跟踪已处理的 newIndex
// 	for k, v := range indexChangeMap {
// 		intKey, err := strconv.Atoi(k)
// 		if err != nil {
// 			fmt.Printf("Error converting index key from string to int: %s\n", k)
// 			continue // 跳过无法转换的键
// 		}
// 		intIndexChangeMap[intKey] = v
// 	}
// 	// 使用集合优化查找过程
// 	noteIndexSet := make(map[int]bool)
// 	indexKeys := make([]string, 0, len(notes))
// 	// for _, note := range notes {
// 	// 	if note.Index != nil {
// 	// 		noteIndexSet[*note.Index] = true
// 	// 		indexKeys = append(indexKeys, strconv.Itoa(*note.Index))
// 	// 	}
// 	// }
// 	// 构建 CASE 语句和处理删除逻辑
// 	caseStatements := make([]string, 0)
// 	deleteIndexes := make([]string, 0)
// 	for oldIndex, newIndex := range intIndexChangeMap {
// 		if _, exists := noteIndexSet[oldIndex]; exists {
// 			if newIndexExists := newIndexSet[newIndex]; newIndexExists || newIndex == -1 {
// 				// 如果 newIndex 已存在或者为 -1，则添加到 deleteIndexes
// 				deleteIndexes = append(deleteIndexes, strconv.Itoa(oldIndex))
// 			} else {
// 				// 只有当 oldIndex 和 newIndex 不相同，且 newIndex 未被处理过时，才添加到 caseStatements
// 				if oldIndex != newIndex {
// 					caseStatements = append(caseStatements, fmt.Sprintf("WHEN %d THEN %d", oldIndex, newIndex))
// 				}
// 				newIndexSet[newIndex] = true // 标记此 newIndex 为已处理
// 			}
// 		}
// 	}
// 	r.Tx(
// 		func(txDb *dbx.DBExtension) error {
// 			fmt.Printf("deleteIndexes: %v\n", deleteIndexes)
// 			if len(deleteIndexes) > 0 {
// 				queryDelete := fmt.Sprintf(`
// 					DELETE FROM notes
// 					WHERE uid = '%s' AND subtitle_id = '%s'
// 					AND `+"`index`"+` IN (%s)
// 				`, uid, subtitleId, strings.Join(deleteIndexes, ", "))
// 				if err := txDb.Exec(queryDelete).Error; err != nil {
// 					return err
// 				}
// 			}
// 			return nil
// 		},
// 		func(txDb *dbx.DBExtension) error {
// 			if len(caseStatements) > 0 {
// 				// 构建并执行更新SQL命令
// 				queryUpdate := fmt.Sprintf(`
// 			UPDATE notes
// 			SET `+"`index`"+` = CASE `+"`index`"+`
// 			%s
// 			END
// 			WHERE uid = '%s' AND subtitle_id = '%s'
// 			AND `+"`index`"+` IN (%s)
// 			`, strings.Join(caseStatements, " "), uid, subtitleId, strings.Join(indexKeys, ", "))

// 				if err := txDb.Exec(queryUpdate).Error; err != nil {
// 					return err
// 				}
// 			}
// 			return nil
// 		},
// 	)

// 	return nil
// }
