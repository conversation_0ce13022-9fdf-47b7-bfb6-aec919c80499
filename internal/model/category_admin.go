package model

import (
	"loop/internal/config"

	"go4.org/syncutil/singleflight"
)

func NewCategoryAdminModel(dbModel *DbModel, config *config.Config) *CategoryAdminModel {
	return &CategoryAdminModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type CategoryAdminModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

// 初始化的size  默认拉取多少个  0代表拉取所有
func (r *CategoryAdminModel) GetCategoryList(pageSize int, currentPage int) ([]*Category, error) {
	result := []*Category{}
	if pageSize == 0 {
		err := r.GetList(&result, "")
		if err != nil {
			return nil, err
		}
	} else {
		err := r.GetPageRangeList(&result, "", currentPage, pageSize, "")
		if err != nil {
			return nil, err
		}
	}
	return result, nil
}
