package model

import (
	"loop/internal/config"

	"go4.org/syncutil/singleflight"
)

func NewCollectModel(dbModel *DbModel, config *config.Config) *CollectModel {
	return &CollectModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type CollectModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func (r *CollectModel) GetUserLocalNoteCollect(uid string, userLocalNoteId string) (*NoteCollectRelations, error) {
	if userLocalNoteId == "" {
		return nil, nil
	}
	query := NoteCollectRelations{LocalNoteId: userLocalNoteId, Uid: uid}
	result := NoteCollectRelations{}
	if found, err := r.GetOne(&result, query); !found {
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	return &result, nil
}
