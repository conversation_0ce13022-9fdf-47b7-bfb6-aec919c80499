package model

import (
	"errors"
	"fmt"
	"loop/internal/config"
	"loop/pkg/dbx"
	"loop/pkg/util"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/apple"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"go4.org/syncutil/singleflight"
)

// 支付相关的时间戳都为毫秒
const (
	TradeOrderStatusWaitPay   = 1
	TradeOrderStatusPayFinish = 2
	TradeOrderStatusPayFailed = 3

	UserSubscriptionStatusContract    = 1 //签约
	UserSubscriptionStatusTermination = 2 //解约

	// 订阅操作：1-签约，2-续约，3-解约,4-重新订阅,5-升级，6-降级
	UserSubscriptionOperationContract    = 1
	UserSubscriptionOperationRenewal     = 2
	UserSubscriptionOperationTermination = 3
	UserSubscriptionOperationReview      = 4
	UserSubscriptionOperationUpgrade     = 5
	UserSubscriptionOperationDowngrade   = 6

	// 会员等级
	VIP_LEVEL_NORMAL = 1
	VIP_LEVEL_PRO    = 100
	VIP_LEVEL_ULTITA = 1000
)

func NewVipModel(dbModel *DbModel, config *config.Config) *VipModel {
	return &VipModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type VipModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

// 会员
type VIP struct {
	ModelAutoId
	Name string `gorm:"size:64" comment:"名称"`
	// 暂时固定：30 天为普通会员，年会员为PRO会员，终身会员为ULTRA会员
	Level int `gorm:"default:1" comment:"会员等级"`
}

func (VIP) TableName() string {
	return "vips"
}

// 用户会员表
type UserVIPRelations struct {
	ModelAutoId
	Uid             string `gorm:"not null;index:idx_user_watch,priority:1"`
	IsVip           int    `gorm:"default:0" comment:"状态：0-非会员，1-是会员"`
	VipID           uint   `comment:"会员ID"`
	IsSubscription  int    `gorm:"default:0" comment:"是否为处于订阅中：0-否，1-是"`
	ExpireDate      string `gorm:"size:20" comment:"到期日期"`
	ExpireTimestamp int64  `comment:"到期日期时间戳,毫秒"`
}

func (UserVIPRelations) TableName() string {
	return "user_vip_relations"
}

// 用户会员流水表 会员的时间根据这个流水来统计
type UserVIPFlow struct {
	ModelAutoId
	Uid                string `gorm:"not null;index:idx_user_watch,priority:1"`
	Title              string `gorm:"type:text;comment:日志标题"`
	Type               int    `gorm:"default:1" comment:"产生流水的类型：1-订阅，2-兑换码"`
	Operation          int    `gorm:"default:1" comment:"操作：1-开通，2-关闭"`
	OperationTimestamp int64  `gorm:"type:bigint;default:0" comment:"开通/关闭时间戳"`
	Days               int    `gorm:"default:0" comment:"时间变更（天数）"`
	Terminal           int    `gorm:"default:0" comment:"终端：0-未知，1-Android，2-IOS"`
	BizID              string `gorm:"size:64" comment:"业务来源ID，如订单ID、兑换code"`
}

func (UserVIPFlow) TableName() string {
	return "user_vip_flows"
}

// 会员的商品表 和ios中的商品订阅对应上 不允许修改 是固定的
type TradeProduct struct {
	ModelAutoId
	Name           string  `gorm:"size:64" comment:"名称"`
	Type           int     `gorm:"default:1" comment:"商品类型：1-会员"`
	IsSubscription int     `gorm:"default:1" comment:"是否为订阅商品：0-否，1-是"`
	Price          float64 `gorm:"type:decimal(10,2)" comment:"实际价格"`
	OriginPrice    float64 `gorm:"type:decimal(10,2)" comment:"原价价格"`
	IosProductId   string  `gorm:"size:128;not null;comment:第三方支付平台商品编码,给ios用，固定的"` //类似com.merachmonthtest.www
	Currency       string  `gorm:"size:128;not null;comment:币种"`                    //三个字母的ISO 4217货币代码
	Terminal       int     `gorm:"default:1" comment:"终端：1-Android，2-IOS"`
	Days           int     `gorm:"default:0" comment:"商品对应的会员天数,-1为永久"`
	VipID          uint    `comment:"会员ID"`
}

func (TradeProduct) TableName() string {
	return "trade_products"
}

// 订单表
type UserPurchaseOrder struct {
	ModelAutoId
	OrderNo string `gorm:"size:64" comment:"订单编号"`
	//苹果为 originalTransactionId 首次先根据appAccountToken来判断绑定uid  后续根据originalTransactionId来获取uid
	OutTransactionId           string  `gorm:"size:64" comment:"外部交易号，apple 对应TransactionId"`
	AppleOriginalTransactionId string  `gorm:"size:64" comment:"苹果的针对每个用户绑定的唯一的交易ID，只要交易过就不会改变"`
	Uid                        string  `gorm:"not null;index:idx_user_watch,priority:1"`
	ProductID                  uint    `comment:"商品ID"`
	ProductName                string  `gorm:"size:64" comment:"名称"`
	ProductType                int     `gorm:"default:1" comment:"商品类型：1-会员"`
	UserSubscriptionID         uint    `comment:"订阅ID"`
	Currency                   string  `gorm:"size:128;not null;comment:币种"`
	Amount                     float64 `gorm:"type:decimal(10,2)" comment:"订单金额"`
	PaymentProvider            int     `gorm:"type:tinyint;not null" comment:"三方支付：1-苹果内购，2-微信 3-支付宝"`
	PaidTimestamp              int64   `comment:"支付时间戳"`
	Status                     int     `gorm:"default:1" comment:"订单状态：1-待支付，2-交易完成，3-交易失败"`
	PaymentFailureReason       string  `gorm:"type:text;comment:支付失败原因"` // 可存储具体错误码和描述
	FailureTimestamp           int64   `gorm:"comment:支付失败时间戳"`
}

func (UserPurchaseOrder) TableName() string {
	return "user_purchase_orders"
}

// 订阅 已订阅的，处于订单待支付的不会有这个信息 一个用户只有一个订阅  代表当前的订阅状态
// 订阅里的属性只和订阅挂钩 如果是送的会员，和这里无关，送的会员从流水里查询
type UserSubscription struct {
	ModelAutoId
	Uid                        string  `gorm:"not null;index:idx_user_watch,priority:1"`
	ProductID                  uint    `comment:"商品ID"`
	ProductName                string  `gorm:"size:64" comment:"名称"`
	PaymentProvider            int     `gorm:"type:tinyint;not null" comment:"三方支付：1-苹果内购，2-微信 3-支付宝"`
	OutTransactionId           string  `gorm:"size:64" comment:"外部交易号，apple 对应TransactionId"`
	AppleOriginalTransactionId string  `gorm:"size:64" comment:"苹果的针对每个用户绑定的唯一的交易ID，只要交易过就不会改变"`
	FirstCycleAmount           float64 `gorm:"type:decimal(10,2)" comment:"订阅第一个周期金额"`
	NextCycleAmount            float64 `gorm:"type:decimal(10,2)" comment:"订阅下一个周期金额"`
	NextPaidDate               string  `gorm:"size:20" comment:"下一次支付日期"`
	NextPaidTimestamp          int64   `comment:"下一次支付日期时间戳"`
	SignTimestamp              int64   `comment:"签约时间"`
	CancelTimestamp            int64   `comment:"解约时间"`
	Status                     int     `gorm:"default:1" comment:"订阅状态：1-签约，0-解约"`
	Currency                   string  `gorm:"size:128;not null;comment:币种"`
	Desc                       string  `gorm:"default:1" comment:"状态描述，比如注销"`
}

func (UserSubscription) TableName() string {
	return "user_subscriptions"
}

// 订阅日志表
type UserSubscriptionLog struct {
	ModelAutoId
	Uid                string `gorm:"not null;index:idx_user_watch,priority:1"`
	ProductID          uint   `gorm:"type:bigint;default:0"`
	ProductName        string `gorm:"size:64" comment:"名称"`
	PaymentProvider    int    `comment:"三方支付：1-苹果内购，2-微信 3-支付宝"`
	UserSubscriptionID uint   `gorm:"default:0" comment:"用户订阅ID"`
	Operation          int    `gorm:"default:1" comment:"订阅操作：1-签约，2-续约，3-解约,4-重新订阅,5-升级，6-降级"`
}

func (UserSubscriptionLog) TableName() string {
	return "user_subscription_logs"
}

// 管理VIP兑换码的生成、使用状态
type PromotionCode struct {
	ModelAutoId
	Status            int    `gorm:"default:0" comment:"0未兑换，1已兑换" json:"status"`
	Code              string `gorm:"size:64" json:"code"`
	Days              int    `gorm:"default:0" json:"days"`
	Uid               string `comment:"兑换的用户ID" json:"uid"`
	RedeemedTimestamp int64  `comment:"兑换时间" json:"redeemedTimestamp"`
}

func (PromotionCode) TableName() string {
	return "promotion_codes"
}
func (r *VipModel) CreatePayOrder(uid string, productId uint, platform string) (string, error) {
	terminal := 1
	if platform == util.PlatformIOS {
		terminal = 2
	} else if platform == util.PlatformAndroid {
		terminal = 1
	}
	query := TradeProduct{ModelAutoId: ModelAutoId{Id: productId}, Terminal: terminal}
	result := TradeProduct{}
	if found, err := r.GetOne(&result, query); !found {
		if err != nil {
			return "", err
		}
		return "", nil
	}
	//创建订单信息
	newUUID := uuid.NewString()
	saver := UserPurchaseOrder{
		OrderNo:         newUUID,
		Uid:             uid,
		ProductID:       result.Id,
		ProductName:     result.Name,
		ProductType:     result.Type,
		Currency:        result.Currency,
		Amount:          result.Price,
		PaymentProvider: 1,
	}
	err := r.SaveOne(&saver)
	if err != nil {
		return "", err
	}
	return newUUID, nil
}

func (r *VipModel) AddPromotionCode(days int) error {
	newUUID := uuid.NewString()
	redeemCode := PromotionCode{
		Days: days,
		Code: newUUID,
	}
	err := r.SaveOne(&redeemCode)
	if err != nil {
		return err
	}
	return nil
}
func (r *VipModel) ExchangeCode(c *gin.Context, uid string, code string) error {
	redeemCode := PromotionCode{}
	terminal := util.GetTerminal(c)
	if found, _ := r.GetOne(&redeemCode, PromotionCode{Code: code}); !found {
		return errors.New("找不到兑换码")
	}
	if found, _ := r.GetOne(&User{}, User{Model: Model{Id: uid}}); !found {
		return errors.New("找不到用户")
	}
	if redeemCode.Status != 0 || redeemCode.Uid != "" {
		return errors.New("兑换码已被使用")
	}
	err := r.Tx(
		func(txDb *dbx.DBExtension) error {
			userVIPFlow := UserVIPFlow{
				Uid:                uid,
				Title:              "兑换码核销，使用者 ：" + uid + " 兑换码: " + redeemCode.Code,
				Type:               2,
				Operation:          2,
				OperationTimestamp: time.Now().UnixMilli(),
				Days:               redeemCode.Days,
				Terminal:           terminal,
				BizID:              code,
			}
			err := r.SaveOne(&userVIPFlow)
			if err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			var userVIPRelations UserVIPRelations
			found, err := txDb.GetOne(&userVIPRelations, UserVIPRelations{Uid: uid})
			if err != nil {
				return err
			}
			//统计用户会员总天数
			days, err := r.sumDaysByUserVipFlow(txDb, uid)
			if err != nil {
				return err
			}

			expireTimestamp := time.Now().UnixMilli() + int64(days)*24*60*60*1000
			if !found {
				userVIPRelations = UserVIPRelations{
					Uid:             uid,
					IsVip:           1,
					VipID:           r.getProVipID(),
					IsSubscription:  0,
					ExpireDate:      util.TsFormat2String(expireTimestamp),
					ExpireTimestamp: expireTimestamp,
				}
				err := txDb.SaveOne(&userVIPRelations)
				if err != nil {
					return err
				}
			} else {
				userVIPRelations.ExpireDate = util.TsFormat2String(expireTimestamp)
				userVIPRelations.IsVip = 1
				userVIPRelations.VipID = r.getProVipID()
				userVIPRelations.ExpireTimestamp = expireTimestamp
				if err := txDb.Update(userVIPRelations, " id = ?", userVIPRelations.Id); err != nil {
					return err
				}
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			redeemCode.Uid = uid
			redeemCode.Status = 1
			redeemCode.RedeemedTimestamp = time.Now().UnixMilli()
			if err := txDb.Update(redeemCode, " id = ?", redeemCode.Id); err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			// 为用户创建对应的权益
			benefitModel := NewBenefitModel(r.DbModel, r.config)

			// 根据VIP等级分配权益
			err := benefitModel.AssignBenefitsByVipLevel(uid, VIP_LEVEL_PRO)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return err
	}
	return nil
}

// 添加一个辅助方法来获取PRO会员的VipID
func (r *VipModel) getProVipID() uint {
	var vip VIP
	found, _ := r.GetOne(&vip, VIP{Level: VIP_LEVEL_PRO})
	if !found {
		return 0
	}
	return vip.Id
}

// transactionId
// 定义: 这是每个单独交易的唯一标识符。
// 用途: 用于标识特定的购买交易。
// 变化: 每次新的购买或续订都会生成一个新的 transactionId。
// originalTransactionId
// 定义: 这是首次购买订阅或可续订商品时生成的交易标识符。
// 用途: 用于标识原始购买交易。
// 变化: 对于自动续订的订阅，所有续订交易都会共享同一个 originalTransactionId。
// 使用场景
// transactionId: 用于跟踪和验证每次具体的交易。
// originalTransactionId: 用于追溯订阅的初始购买，特别是在处理续订或恢复购买时。
func (r *VipModel) ReceiveApplePayResult(notificationV2Payload *apple.NotificationV2Payload, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) error {
	logrus.Info("ReceiveApplePayResult NotificationType=", notificationV2Payload.NotificationType, "Subtype=", notificationV2Payload.Subtype)
	tradeProduct := TradeProduct{}
	if found, err := r.GetOne(&tradeProduct, TradeProduct{Terminal: 2, IosProductId: renewalInfo.ProductId}); !found {
		if err != nil {
			return err
		}
		logrus.Error("ReceiveApplePayResult not found tartget tradeProduct IosProductId=", transactionInfo.ProductId)
		return errors.New("not found TradeProduct")
	}
	vipData := VIP{}
	if found, err := r.GetOne(&vipData, VIP{ModelAutoId: ModelAutoId{Id: tradeProduct.VipID}}); !found {
		if err != nil {
			return err
		}
		logrus.Error("ReceiveApplePayResult not found tartget vip id=", tradeProduct.VipID)
		return errors.New("not found VIP")
	}
	userPurchaseOrder := UserPurchaseOrder{}
	found, err := r.GetOne(&userPurchaseOrder, UserPurchaseOrder{OrderNo: transactionInfo.AppAccountToken, PaymentProvider: 1})
	if err != nil {
		return err
	}
	fmt.Printf("tradeOrder: %v\n", userPurchaseOrder)
	fmt.Printf("found: %v\n", found)
	if !found {
		logrus.Info("ReceiveApplePayResult appAccountToken is empty , transactionId=", transactionInfo.TransactionId)
		//如果 AppAccountToken 为空，说明是用户自己不在app操作订阅 根据OriginalTransactionId来获取
		if found, err := r.GetOne(&userPurchaseOrder, UserPurchaseOrder{OutTransactionId: transactionInfo.OriginalTransactionId, PaymentProvider: 1}); !found {
			if err != nil {
				return err
			}
			logrus.Error("ReceiveApplePayResult not found tartget tradeOrder uid=", userPurchaseOrder.Uid)
			return errors.New("not found order")
		}
	}

	handler := r.selectHandler(notificationV2Payload)
	err = handler.Handle(r, tradeProduct, vipData, userPurchaseOrder, notificationV2Payload, renewalInfo, transactionInfo)
	if err != nil {
		userPurchaseOrder.Status = 3
		userPurchaseOrder.PaymentFailureReason = err.Error()
		userPurchaseOrder.FailureTimestamp = time.Now().UnixMilli()
		r.Update(userPurchaseOrder, " id =?", userPurchaseOrder.Id)
	}
	return err
}
