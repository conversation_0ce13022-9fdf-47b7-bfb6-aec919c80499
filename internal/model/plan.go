package model

import (
	"fmt"
	"loop/internal/config"
	"loop/pkg/dbx"
	"time"

	"go4.org/syncutil/singleflight"
)

// UserQuestionnaire 存储用户的问卷回答
type UserQuestionnaire struct {
	Model
	Uid               string `gorm:"not null;index:idx_user_questionnaire,priority:1"`
	MotivationSource  string `gorm:"type:text;comment:用户的动力来源"`
	DesiredAbility    string `gorm:"type:text;comment:用户想提高的能力"`
	CurrentLevel      string `gorm:"size:10;comment:用户当前级别"`
	TargetLevel       string `gorm:"size:10;comment:用户目标级别"`
	DailyStudyMinutes int    `gorm:"type:int;comment:用户每天学习时间(分钟)"`
}

func (UserQuestionnaire) TableName() string {
	return "user_questionnaires"
}

// LearningPlan 表示用户的学习计划
type LearningPlan struct {
	Model
	Uid             string    `gorm:"not null;index:idx_user_plan,priority:1"`
	StartLevel      string    `gorm:"size:10;not null;comment:起始级别"`
	TargetLevel     string    `gorm:"size:10;not null;comment:目标级别"`
	StartDate       time.Time `gorm:"not null;comment:计划开始日期"`
	EndDate         time.Time `gorm:"comment:计划结束日期"`
	Status          int       `gorm:"type:tinyint;default:1;comment:状态 1进行中 0已作废"`
	QuestionnaireId string    `gorm:"size:20;comment:关联的问卷ID"`
}

func (LearningPlan) TableName() string {
	return "learning_plans"
}

// LearningPlanStage 表示学习计划中的一个阶段
type LearningPlanStage struct {
	Model
	PlanId       string    `gorm:"not null;index:idx_plan_stage,priority:1;comment:关联的计划ID"`
	StageDesc    string    `gorm:"size:20;comment:阶段描述，如 A0-1, A1-2 等"`
	StartDate    time.Time `gorm:"comment:阶段开始日期"`
	EndDate      time.Time `gorm:"comment:阶段结束日期"`
	Objective    string    `gorm:"type:text;comment:阶段目标"`
	SortOrder    int       `gorm:"type:int;default:0;comment:排序顺序"`
	ResourceIds  string    `gorm:"type:text;comment:资源ID列表，JSON格式"`
	LsCountsJSON string    `gorm:"type:text;comment:资源LS次数列表，JSON格式，与ResourceIds对应"`
	WeeksJSON    string    `gorm:"type:text;comment:按周划分的学习计划，JSON格式"`
}

func (LearningPlanStage) TableName() string {
	return "learning_plan_stages"
}

func NewPlanModel(dbModel *DbModel, config *config.Config) *PlanModel {
	return &PlanModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{},
	}
}

type PlanModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

// GetUserQuestionnaire 获取用户的问卷信息
func (p *PlanModel) GetUserQuestionnaire(uid string) (*UserQuestionnaire, error) {
	res, err := p.sg.Do(fmt.Sprintf("get_user_questionnaire_%s", uid), func() (any, error) {
		query := UserQuestionnaire{Uid: uid}
		questionnaire := UserQuestionnaire{}
		found, err := p.GetOne(&questionnaire, query)
		if err != nil {
			return nil, err
		}
		if !found {
			return nil, nil
		}
		return &questionnaire, nil
	})
	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, nil
	}
	data, ok := res.(*UserQuestionnaire)
	if !ok {
		return nil, fmt.Errorf("type assertion to *UserQuestionnaire failed")
	}
	return data, nil
}

// GetActiveLearningPlan 获取用户当前活跃的学习计划
func (p *PlanModel) GetActiveLearningPlan(uid string) (*LearningPlan, error) {
	res, err := p.sg.Do(fmt.Sprintf("get_active_learning_plan_%s", uid), func() (any, error) {
		query := LearningPlan{Uid: uid, Status: 1}
		plan := LearningPlan{}
		found, err := p.GetOne(&plan, query)
		if err != nil {
			return nil, err
		}
		if !found {
			return nil, nil
		}
		return &plan, nil
	})
	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, nil
	}
	data, ok := res.(*LearningPlan)
	if !ok {
		return nil, fmt.Errorf("type assertion to *LearningPlan failed")
	}
	return data, nil
}

// GetPlanStages 获取学习计划的所有阶段
func (p *PlanModel) GetPlanStages(planId string) ([]*LearningPlanStage, error) {
	var stages []*LearningPlanStage
	err := p.GetList(&stages, "plan_id = ? ORDER BY sort_order ASC", planId)
	if err != nil {
		return nil, err
	}
	return stages, nil
}

// InvalidateUserPlans 将用户所有现有计划标记为无效
func (p *PlanModel) InvalidateUserPlans(uid string) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		if err := txDb.Model(&LearningPlan{}).Where("uid = ? AND status = ?", uid, 1).Update("status", 0).Error; err != nil {
			return err
		}
		return nil
	})
}
