package model

import (
	"loop/internal/config"
	"loop/pkg/enum"

	"go4.org/syncutil/singleflight"
)

func NewSeriesModel(dbModel *DbModel, config *config.Config) *SeriesModel {
	return &SeriesModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type SeriesModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func (r *SeriesModel) GetSeriesFeatureContent() ([]Series, error) {
	featuredContentIds, err := r.GetFeatureContentIds()
	if err != nil {
		return nil, err
	}
	var serieses []Series
	err = r.GetList(&serieses, "id IN ?", featuredContentIds)
	if err != nil {
		return nil, err
	}
	return serieses, nil
}
func (s *SeriesModel) GetFeatureContentIds() ([]string, error) {
	// 因为精选的比较少 可以直接查出所有的精选 然后再到列表里去匹配
	var featuredContents []FeaturedContent
	err := s.GetList(&featuredContents, FeaturedContent{ContentType: int(enum.Series)})
	if err != nil {
		return nil, err
	}
	featuredContentIds := make([]string, len(featuredContents))
	for idx, series := range featuredContents {
		featuredContentIds[idx] = series.ContentID
	}
	return featuredContentIds, nil
}
