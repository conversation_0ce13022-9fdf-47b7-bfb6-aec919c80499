package model

import (
	"loop/internal/config"

	"go4.org/syncutil/singleflight"
)

func NewCategoryTypeModel(dbModel *DbModel, config *config.Config) *CategoryTypeModel {
	return &CategoryTypeModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type CategoryTypeModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

// GetById 根据ID获取分类类型
func (c *CategoryTypeModel) GetById(id string) (*CategoryType, error) {
	result := CategoryType{}
	if found, err := c.GetOne(&result, "id = ?", id); !found {
		return nil, err
	}
	return &result, nil
}

// GetByIds 根据ID列表获取分类类型
func (c *CategoryTypeModel) GetByIds(ids []string) ([]*CategoryType, error) {
	result := []*CategoryType{}
	err := c.GetList(&result, "id IN ?", ids)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetAll 获取所有分类类型
func (c *CategoryTypeModel) GetAll() ([]*CategoryType, error) {
	result := []*CategoryType{}
	err := c.Order("priority ASC, id ASC").Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}

// CheckNameExists 检查名称是否已存在
func (c *CategoryTypeModel) CheckNameExists(name string, excludeId string) (bool, error) {
	var count int64
	query := c.Model(&CategoryType{}).Where("name = ?", name)
	if excludeId != "" {
		query = query.Where("id != ?", excludeId)
	}
	err := query.Count(&count).Error
	return count > 0, err
}
