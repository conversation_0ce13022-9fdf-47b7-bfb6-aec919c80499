package model

import (
	"errors"
	"loop/internal/config"
	"loop/internal/constants"
	"loop/pkg/dbx"
	"time"

	"go4.org/syncutil/singleflight"
	"gorm.io/gorm"
)

// 权益组表
type BenefitGroup struct {
	ModelAutoId
	Name        string `gorm:"size:64;comment:组名" json:"name"`
	Code        string `gorm:"size:64;comment:组编号" json:"code"`
	Status      int    `gorm:"default:0;comment:状态：0-下线，1-上线" json:"status"`
	Description string `gorm:"size:255;comment:权益描述"`
}

func (BenefitGroup) TableName() string {
	return "benefit_groups"
}

//权益系统
// 1. 权益组对应多个权益
// 2. 一个权益组下的多个权益只有一个生效，子权益代表不同的等级

// 权益表
type Benefit struct {
	ModelAutoId
	Name             string                `gorm:"size:64;comment:权益名"`
	Code             constants.BenefitCode `gorm:"size:64;comment:权益code"`
	Level            int                   `gorm:"default:10;comment:权益等级"`
	CycleType        int                   `gorm:"comment:权益周期类型：1-日，2-周，3-月，4-季，5-年，6-无周期"`
	CycleCount       int                   `gorm:"default:1;comment: 权益周期数量"`
	BenefitCount     int                   `gorm:"default:1;comment:权益数量"`
	Sort             int                   `gorm:"default:1;comment:排序 越大越靠前"`
	Status           int                   `gorm:"default:0;comment:状态：0-禁用，1-启用"`
	BenefitGroupID   uint                  `gorm:"comment:组id"`
	BenefitGroupName string                `gorm:"size:64;comment:组名"`
	BenefitGroupCode string                `gorm:"size:64;comment:组编号"`
	Description      string                `gorm:"size:255;comment:权益描述"`
}

func (Benefit) TableName() string {
	return "benefits"
}

// UserBenefit 用户权益表
type UserBenefit struct {
	ModelAutoId
	Uid                  string                `gorm:"comment:用户ID"`
	BenefitGroupCode     string                `gorm:"size:64;comment:权益组编号"`
	BenefitID            uint                  `gorm:"comment:权益id"`
	BenefitName          string                `gorm:"size:64;comment:权益名"`
	BenefitCode          constants.BenefitCode `gorm:"size:32;comment:权益code"`
	BenefitGroupID       uint                  `gorm:"comment:权益组ID"`
	Remain               int                   `gorm:"comment:权益剩余数量"`
	Total                int                   `gorm:"comment:权益总数量"`
	LastRefreshTime      time.Time             `gorm:"comment:最后一次次刷新时间"`
	LastRefreshTimestamp int64                 `gorm:"comment:最后一次刷新时间戳"`
	NextRefreshTime      time.Time             `gorm:"comment:下次刷新时间"`
	NextRefreshTimestamp int64                 `gorm:"comment:下次刷新时间戳"`
	Status               int                   `gorm:"default:0;comment:状态：0-失效，1-启用"`
	Source               string                `gorm:"size:64;comment:来源"`
}

func (UserBenefit) TableName() string {
	return "user_benefits"
}

// 用户权益流水表
type UserBenefitLog struct {
	ModelAutoId
	Uid              string `gorm:"comment:用户ID"`
	Type             int    `gorm:"default:1;comment:产生流水的类型"`
	Operation        int    `gorm:"default:1;comment:操作：1-增加，2-消耗"`
	UserBenefitID    uint   `gorm:"comment:用户在用权益id"`
	BenefitGroupCode string `gorm:"size:64;comment:权益组编号"`
	BenefitID        uint   `gorm:"comment:权益id"`
	BenefitName      string `gorm:"size:64;comment:权益名"`
	Count            int    `gorm:"comment:变动数量"`
	Remain           int    `gorm:"comment:变动后剩余数量"`
	Terminal         int8   `gorm:"comment:终端：0-未知，1-Android，2-IOS"`
}

func (UserBenefitLog) TableName() string {
	return "user_benefit_logs"
}

// 会员权益表
type VipBenefit struct {
	ModelAutoId
	VipID           uint                  `gorm:"column:vip_id;comment:会员主键"`
	VipLevel        uint                  `gorm:"column:vip_level;comment:会员等级"`
	BenefitGroupID  uint                  `gorm:"column:benefit_group_id;comment:权益组主键"`
	BenefitID       uint                  `gorm:"column:benefit_id;comment:权益主键"`
	BenefitCode     constants.BenefitCode `gorm:"column:benefit_code;type:varchar(50);comment:权益代码"`
	CreateTime      time.Time             `gorm:"comment:创建时间"`
	CreateTimestamp int64                 `gorm:"comment:创建时间戳"`
}

func (VipBenefit) TableName() string {
	return "vip_benefits"
}

type BenefitModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func NewBenefitModel(dbModel *DbModel, config *config.Config) *BenefitModel {
	return &BenefitModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{},
	}
}

// CreateBenefit 创建权益
func (m *BenefitModel) CreateBenefit(benefit *Benefit) error {
	return m.DB.Transaction(func(tx *gorm.DB) error {
		// 检查权益组是否存在

		// 检查权益名称是否重复
		var count int64
		if err := tx.Model(&Benefit{}).Where("name = ?", benefit.Name).Count(&count).Error; err != nil {
			return err
		}
		if count > 0 {
			return errors.New("权益名称已存在")
		}

		// 检查权益编码是否重复
		if err := tx.Model(&Benefit{}).Where("code = ?", benefit.Code).Count(&count).Error; err != nil {
			return err
		}
		if count > 0 {
			return errors.New("权益编码已存在")
		}
		var group BenefitGroup
		if err := tx.First(&group, benefit.BenefitGroupID).Error; err == nil {
			// 设置权益组相关信息
			benefit.BenefitGroupName = group.Name
			benefit.BenefitGroupCode = group.Code
		}

		// 创建权益
		return tx.Create(benefit).Error
	})
}

// GetBenefitByID 根据ID获取权益
func (m *BenefitModel) GetBenefitByID(id int64) (*Benefit, error) {
	var benefit Benefit
	err := m.DB.First(&benefit, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &benefit, nil
}

// GetUserBenefits 获取用户的权益列表
func (m *BenefitModel) GetUserBenefits(uid string) ([]*UserBenefit, error) {
	var benefits []*UserBenefit
	err := m.DB.Where(&UserBenefit{Uid: uid, Status: 1}).Find(&benefits).Error
	if err != nil {
		return nil, err
	}
	return benefits, nil
}

// GetAllBenefits 获取所有启用的权益
func (m *BenefitModel) GetAllBenefits(pageSize int, currentPage int, name string, benefitGroupCode string, status int) ([]*Benefit, int64, error) {
	var benefits []*Benefit
	var total int64

	// 构建查询条件
	query := m.DB.Model(&Benefit{})

	// 添加查询条件
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	if benefitGroupCode != "" {
		query = query.Where("benefit_group_code = ?", benefitGroupCode)
	}
	if status > 0 {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	if pageSize > 0 {
		offset := (currentPage - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 执行查询
	if err := query.Order("sort DESC").Find(&benefits).Error; err != nil {
		return nil, 0, err
	}

	return benefits, total, nil
}

// UpdateBenefit 更新权益
func (m *BenefitModel) UpdateBenefit(benefit *Benefit) (*Benefit, error) {
	err := m.DB.Transaction(func(tx *gorm.DB) error {
		// 检查权益是否存在
		var existingBenefit Benefit
		if err := tx.First(&existingBenefit, benefit.Id).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return errors.New("权益不存在")
			}
			return err
		}

		// 如果要更新权益组，先检查新的权益组是否存在
		if benefit.BenefitGroupID > 0 && benefit.BenefitGroupID != existingBenefit.BenefitGroupID {
			var group BenefitGroup
			if err := tx.First(&group, benefit.BenefitGroupID).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					return errors.New("权益组不存在")
				}
				return err
			}
			benefit.BenefitGroupName = group.Name
			benefit.BenefitGroupCode = group.Code
		}

		// 如果要更新名称，检查名称是否重复
		if benefit.Name != "" && benefit.Name != existingBenefit.Name {
			var count int64
			if err := tx.Model(&Benefit{}).Where("name = ? AND id != ?", benefit.Name, benefit.Id).Count(&count).Error; err != nil {
				return err
			}
			if count > 0 {
				return errors.New("权益名称已存在")
			}
		}

		// 如果要更新编码，检查编码是否重复
		if benefit.Code != "" && benefit.Code != existingBenefit.Code {
			var count int64
			if err := tx.Model(&Benefit{}).Where("code = ? AND id != ?", benefit.Code, benefit.Id).Count(&count).Error; err != nil {
				return err
			}
			if count > 0 {
				return errors.New("权益编码已存在")
			}
		}

		// 更新非空字段
		if err := tx.Model(&existingBenefit).Updates(benefit).Error; err != nil {
			return err
		}

		// 重新查询完整的权益信息
		if err := tx.First(benefit, benefit.Id).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return benefit, nil
}

// AssignBenefitToUser 给用户分配指定的权益
func (m *BenefitModel) AssignBenefitToUser(uid string, codes []string) error {
	// 1. 验证用户是否存在
	var user User
	if found, _ := m.GetOne(&user, User{Model: Model{Id: uid}}); !found {
		return errors.New("用户不存在")
	}

	// 2. 获取所有权益信息
	var benefits []Benefit
	if err := m.DB.Where(&Benefit{
		Status: 1,
	}).Where("code IN ?", codes).Find(&benefits).Error; err != nil {
		return err
	}

	if len(benefits) == 0 {
		return errors.New("未找到有效的权益")
	}

	// 3. 在事务中为用户分配权益
	return m.DB.Transaction(func(tx *gorm.DB) error {
		now := time.Now()

		// 3.1 获取用户现有的权益
		var existingBenefits []UserBenefit
		if err := tx.Where(&UserBenefit{
			Uid: uid,
		}).Find(&existingBenefits).Error; err != nil {
			return err
		}

		// 3.2 准备批量更新和插入的数据
		var (
			toUpdate    []UserBenefit
			toCreate    []UserBenefit
			benefitLogs []UserBenefitLog
		)

		// 3.3 处理每个权益
		for _, benefit := range benefits {
			// 查找是否存在该权益组的记录
			var found bool
			for i := range existingBenefits {
				if existingBenefits[i].BenefitGroupCode == benefit.BenefitGroupCode {
					found = true
					nextRefreshTime := m.calculateNextRefreshTime(now, benefit.CycleType)

					// 更新现有记录
					existingBenefits[i].BenefitID = benefit.Id
					existingBenefits[i].BenefitName = benefit.Name
					existingBenefits[i].BenefitCode = benefit.Code
					existingBenefits[i].Total = benefit.BenefitCount
					existingBenefits[i].Remain = benefit.BenefitCount
					existingBenefits[i].Status = 1
					existingBenefits[i].LastRefreshTime = now
					existingBenefits[i].LastRefreshTimestamp = now.Unix()
					existingBenefits[i].NextRefreshTime = nextRefreshTime
					existingBenefits[i].NextRefreshTimestamp = nextRefreshTime.Unix()

					toUpdate = append(toUpdate, existingBenefits[i])

					// 添加更新日志
					benefitLogs = append(benefitLogs, UserBenefitLog{
						Uid:              uid,
						Type:             2, // 分配
						Operation:        1, // 增加
						UserBenefitID:    existingBenefits[i].Id,
						BenefitGroupCode: benefit.BenefitGroupCode,
						BenefitID:        benefit.Id,
						BenefitName:      benefit.Name,
						Count:            benefit.BenefitCount,
						Remain:           benefit.BenefitCount,
					})
					break
				}
			}

			if !found {
				// 创建新记录
				nextRefreshTime := m.calculateNextRefreshTime(now, benefit.CycleType)
				newBenefit := UserBenefit{
					Uid:                  uid,
					BenefitGroupCode:     benefit.BenefitGroupCode,
					BenefitID:            benefit.Id,
					BenefitName:          benefit.Name,
					BenefitCode:          benefit.Code,
					Total:                benefit.BenefitCount,
					Remain:               benefit.BenefitCount,
					Status:               1,
					LastRefreshTime:      now,
					LastRefreshTimestamp: now.Unix(),
					NextRefreshTime:      nextRefreshTime,
					NextRefreshTimestamp: nextRefreshTime.Unix(),
					Source:               "system",
				}
				toCreate = append(toCreate, newBenefit)
			}
		}

		// 3.4 批量更新现有记录
		if len(toUpdate) > 0 {
			for _, ub := range toUpdate {
				if err := tx.Model(&UserBenefit{}).Where(&UserBenefit{
					ModelAutoId: ModelAutoId{Id: ub.Id},
				}).Updates(ub).Error; err != nil {
					return err
				}
			}
		}

		// 3.5 批量创建新记录
		if len(toCreate) > 0 {
			if err := tx.CreateInBatches(toCreate, len(toCreate)).Error; err != nil {
				return err
			}

			// 为新创建的记录添加日志
			for _, ub := range toCreate {
				benefitLogs = append(benefitLogs, UserBenefitLog{
					Uid:              uid,
					Type:             2, // 分配
					Operation:        1, // 增加
					UserBenefitID:    ub.Id,
					BenefitGroupCode: ub.BenefitGroupCode,
					BenefitID:        ub.BenefitID,
					BenefitName:      ub.BenefitName,
					Count:            ub.Total,
					Remain:           ub.Remain,
				})
			}
		}

		// 3.6 批量创建日志
		if len(benefitLogs) > 0 {
			return tx.CreateInBatches(benefitLogs, len(benefitLogs)).Error
		}

		return nil
	})
}

// CreateBenefitGroup 创建权益组
func (m *BenefitModel) CreateBenefitGroup(group *BenefitGroup) error {
	return m.DB.Create(group).Error
}

// UpdateBenefitGroup 更新权益组
func (m *BenefitModel) UpdateBenefitGroup(group *BenefitGroup) error {
	return m.DB.Model(&BenefitGroup{}).Where(&BenefitGroup{ModelAutoId: ModelAutoId{Id: group.Id}}).Updates(group).Error
}

// GetBenefitGroupByID 根据ID获取权益组
func (m *BenefitModel) GetBenefitGroupByID(id int64) (*BenefitGroup, error) {
	var group BenefitGroup
	err := m.DB.First(&group, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &group, nil
}

// GetBenefitGroupByCode 根据Code获取权益组
func (m *BenefitModel) GetBenefitGroupByCode(code string) (*BenefitGroup, error) {
	var group BenefitGroup
	err := m.DB.Where(&BenefitGroup{Code: code}).First(&group).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &group, nil
}

// GetAllBenefitGroups 获取所有权益组
func (m *BenefitModel) GetAllBenefitGroups(pageSize int, currentPage int) ([]*BenefitGroup, error) {
	var groups []*BenefitGroup
	if pageSize == 0 {
		err := m.GetList(&groups, "")
		if err != nil {
			return nil, err
		}
	} else {
		err := m.GetPageRangeList(&groups, "", currentPage, pageSize, "")
		if err != nil {
			return nil, err
		}
	}
	return groups, nil
}

// GetBenefitsByGroupCode 根据权益组编码获取所有生效的权益
func (m *BenefitModel) GetBenefitsByGroupCode(groupCode string) ([]*Benefit, error) {
	var benefits []*Benefit
	err := m.DB.Where(&Benefit{
		BenefitGroupCode: groupCode,
		Status:           1, // 只获取启用的权益
	}).Order("sort DESC").Find(&benefits).Error
	if err != nil {
		return nil, err
	}
	return benefits, nil
}

// GetBenefitGroupPage 权益组分页查询
func (m *BenefitModel) GetBenefitGroupPage(pageSize int, currentPage int, name string, code string) ([]*BenefitGroup, int64, error) {
	var groups []*BenefitGroup
	var total int64

	// 构建查询条件
	query := m.DB.Model(&BenefitGroup{})
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	if code != "" {
		query = query.Where("code LIKE ?", "%"+code+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	if pageSize > 0 {
		offset := (currentPage - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	if err := query.Find(&groups).Error; err != nil {
		return nil, 0, err
	}

	return groups, total, nil
}

// UpdateBenefitGroupStatus 更新权益组状态
func (m *BenefitModel) UpdateBenefitGroupStatus(id uint, status int) error {
	return m.DB.Model(&BenefitGroup{}).Where(&BenefitGroup{ModelAutoId: ModelAutoId{Id: id}}).Update("status", status).Error
}

// GetUserBenefitsByUid 根据用户ID获取用户权益列表
func (m *BenefitModel) GetUserBenefitsByUid(uid string) ([]*UserBenefit, error) {
	var benefits []*UserBenefit
	err := m.DB.Where(&UserBenefit{
		Uid:    uid,
		Status: 1,
	}).Order("next_refresh_time ASC").Find(&benefits).Error
	if err != nil {
		return nil, err
	}
	return benefits, nil
}

// GetUserBenefitsByGroup 获取指定用户的指定组权益
func (m *BenefitModel) GetUserBenefitsByGroup(uid string, groupCode string) ([]*UserBenefit, error) {
	var benefits []*UserBenefit
	err := m.DB.Where(&UserBenefit{
		Uid:              uid,
		BenefitGroupCode: groupCode,
		Status:           1,
	}).Order("next_refresh_time ASC").Find(&benefits).Error
	if err != nil {
		return nil, err
	}
	return benefits, nil
}

// AssignBenefitsByVipLevel 根据VIP等级为用户分配相应的权益
func (m *BenefitModel) AssignBenefitsByVipLevel(uid string, vipLevel int) error {
	// 1. 直接通过VIP等级查询对应的权益组关系
	var vipBenefits []*VipBenefit
	if err := m.DB.Where(&VipBenefit{VipLevel: uint(vipLevel)}).Find(&vipBenefits).Error; err != nil {
		return err
	}

	if len(vipBenefits) == 0 {
		return errors.New("该VIP等级没有关联任何权益组")
	}

	// 2. 直接从VipBenefit中获取权益编码列表
	var benefitCodes []string
	for _, vb := range vipBenefits {
		if vb.BenefitCode != "" {
			benefitCodes = append(benefitCodes, string(vb.BenefitCode))
		}
	}

	// 3. 如果BenefitCode为空，则通过BenefitID查询
	if len(benefitCodes) == 0 {
		for _, vb := range vipBenefits {
			if vb.BenefitID > 0 {
				var benefit Benefit
				if err := m.DB.First(&benefit, vb.BenefitID).Error; err != nil {
					if err == gorm.ErrRecordNotFound {
						continue
					}
					return err
				}
				benefitCodes = append(benefitCodes, string(benefit.Code))
			}
		}
	}

	if len(benefitCodes) == 0 {
		return errors.New("找不到有效的权益编码")
	}

	// 4. 分配权益
	return m.AssignBenefitToUser(uid, benefitCodes)
}

// ConsumeBenefit 消耗用户权益
func (m *BenefitModel) ConsumeBenefit(uid string, benefitGroupCode string, count int) error {
	// 2. 获取用户的权益信息
	var userBenefit UserBenefit
	if found, err := m.GetOne(&userBenefit, UserBenefit{
		Uid:              uid,
		BenefitGroupCode: benefitGroupCode,
		Status:           1, // 只查询启用状态的权益
	}); !found || err != nil {
		if err != nil {
			return err
		}
		return errors.New("用户没有该权益或权益已失效")
	}

	// 3. 检查权益是否过期
	now := time.Now()
	if userBenefit.NextRefreshTime.Before(now) {
		// 如果已过期，需要刷新权益
		benefit := Benefit{}
		if found, err := m.GetOne(&benefit, Benefit{BenefitGroupCode: benefitGroupCode}); !found || err != nil {
			if err != nil {
				return err
			}
			return errors.New("权益不存在")
		}

		// 更新剩余次数和刷新时间
		userBenefit.Remain = benefit.BenefitCount
		userBenefit.Total = benefit.BenefitCount
		userBenefit.LastRefreshTime = now
		userBenefit.LastRefreshTimestamp = now.UnixMilli()
		userBenefit.NextRefreshTime = m.calculateNextRefreshTime(now, benefit.CycleType)
		userBenefit.NextRefreshTimestamp = userBenefit.NextRefreshTime.UnixMilli()
	}

	// 4. 检查剩余次数是否足够
	if userBenefit.Remain < count {
		return errors.New("权益剩余次数不足")
	}

	// 5. 在事务中更新权益使用记录
	err := m.Tx(func(tx *dbx.DBExtension) error {
		// 更新剩余次数
		userBenefit.Remain -= count
		if err := tx.Update(userBenefit, "id = ?", userBenefit.Id); err != nil {
			return err
		}

		// 记录消耗日志
		benefitLog := UserBenefitLog{
			Uid:              uid,
			BenefitGroupCode: benefitGroupCode,
			Count:            count,
			Type:             2,
			Operation:        2,
			UserBenefitID:    userBenefit.Id,
			BenefitID:        userBenefit.BenefitID,
			BenefitName:      userBenefit.BenefitName,
			Remain:           userBenefit.Remain,
			Terminal:         0,
		}
		if err := tx.SaveOne(&benefitLog); err != nil {
			return err
		}

		return nil
	})

	return err
}

const (
	CycleTypeDaily   = 1 // 每日
	CycleTypeWeekly  = 2 // 每周
	CycleTypeMonthly = 3 // 每月
	CycleTypeNone    = 6 // 无周期
)

// calculateNextRefreshTime 计算下次刷新时间
func (m *BenefitModel) calculateNextRefreshTime(now time.Time, cycleType int) time.Time {
	switch cycleType {
	case CycleTypeDaily: // 每日
		return now.AddDate(0, 0, 1)
	case CycleTypeWeekly: // 每周
		return now.AddDate(0, 0, 7)
	case CycleTypeMonthly: // 每月
		return now.AddDate(0, 1, 0)
	default: // 无限期
		return now.AddDate(100, 0, 0) // 设置一个很长的时间
	}
}
