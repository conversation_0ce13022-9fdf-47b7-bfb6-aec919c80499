package model

import (
	"fmt"
	"loop/internal/config"

	"go4.org/syncutil/singleflight"
)

func NewUserAdminModel(dbModel *DbModel, config *config.Config) *UserAdminModel {
	return &UserAdminModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type UserAdminModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func (r *UserAdminModel) FindAdminByUsername(username string) (*SysUser, error) {
	res, err := r.sg.Do(fmt.Sprintf("find_by_username_%s", username), func() (interface{}, error) {
		query := SysUser{UserName: username}
		user := SysUser{}
		if found, err := r.GetOne(&user, query); !found {
			return nil, err
		}
		return &user, nil
	})
	if err != nil {
		return nil, err
	}
	user, ok := res.(*SysUser)
	if !ok {
		return nil, fmt.Errorf("type assertion to *User failed")
	}
	return user, nil
}
