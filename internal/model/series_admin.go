package model

import (
	"loop/internal/config"
	"loop/internal/request"
	"loop/pkg/dbx"
	"loop/pkg/enum"

	"github.com/jinzhu/copier"
	"go4.org/syncutil/singleflight"
)

func NewSeriesAdminModel(dbModel *DbModel, config *config.Config) *SeriesAdminModel {
	return &SeriesAdminModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type SeriesAdminModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func (r *SeriesAdminModel) AddSeries(req request.SeriesAddReq) error {
	var seriesId = req.Id
	var isUpdate = req.Id != ""
	err := r.Tx(
		func(txDb *dbx.DBExtension) error {
			if isUpdate {
				if found, _ := txDb.GetOne(&Series{}, Series{Model: Model{Id: req.Id}}); !found {
					isUpdate = false
				}
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			var saver Series
			copier.Copy(&saver, &req)
			if isUpdate {
				if err := txDb.Update(&saver, "id = ?", req.Id); err != nil {
					return err
				}
			} else {
				if err := txDb.Save(&saver).Error; err != nil {
					return err
				}
				seriesId = saver.Id
			}

			return nil
		},
		func(txDb *dbx.DBExtension) error {
			// 处理SeriesRelation表
			if isUpdate {
				// 先删除所有的剧集多语言信息
				if err := txDb.Delete(&SeriesRelation{}, SeriesRelation{SeriesId: seriesId}).Error; err != nil {
					return err
				}
			}

			// 准备批量插入的SeriesRelation数据
			var seriesRelations []SeriesRelation
			for _, relation := range req.SeriesRelations {
				var rel SeriesRelation
				copier.Copy(&rel, &relation)
				rel.SeriesId = seriesId
				seriesRelations = append(seriesRelations, rel)
			}

			if err := txDb.CreateInBatches(seriesRelations, len(seriesRelations)).Error; err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			// 准备批量插入的数据
			var entries []CategorySeriesRelations
			if isUpdate {
				//先删除所有的资源对应的分类
				if err := txDb.Delete(&CategorySeriesRelations{}, CategorySeriesRelations{SeriesId: seriesId}).Error; err != nil {
					return err
				}
			}
			for _, categoryId := range req.CategoryIds {
				entries = append(entries, CategorySeriesRelations{
					SeriesId:   seriesId,
					CategoryId: categoryId,
				})
			}

			if err := txDb.CreateInBatches(entries, len(entries)).Error; err != nil {
				return err
			}
			return nil
		},
	)
	return err
}

func (r *SeriesAdminModel) DeleteSeries(resourceIds []string) error {
	err := r.Tx(
		func(txDb *dbx.DBExtension) error {
			return txDb.Where("id IN ?", resourceIds).Delete(&Series{}).Error
		},
		func(txDb *dbx.DBExtension) error {
			return txDb.Where("series_id IN ?", resourceIds).Delete(&SeriesRelation{}).Error
		},
		func(txDb *dbx.DBExtension) error {
			return txDb.Where("series_id IN ?", resourceIds).Delete(&CategorySeriesRelations{}).Error
		},
		func(txDb *dbx.DBExtension) error {
			return txDb.Where("series_id IN ?", resourceIds).Delete(&SeriesResourceRelations{}).Error
		},
		func(txDb *dbx.DBExtension) error {
			return txDb.Where("content_id IN ? AND content_type = ?", resourceIds, int(enum.Series)).Delete(&FeaturedContent{}).Error
		},
	)
	return err
}
