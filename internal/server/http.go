package server

import (
	"fmt"
	"loop/internal/config"
	"net/http"

	"github.com/gin-gonic/gin"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(config *config.Config, ginHandler *gin.Engine) *http.Server {
	readTimeout := config.Server.ReadTimeout
	writeTimeout := config.Server.WriteTimeout
	endPoint := fmt.Sprintf(":%d", config.Server.HttpPort)
	maxHeaderBytes := 1 << 20

	srv := &http.Server{
		Addr:           endPoint,
		Handler:        ginHandler,
		ReadTimeout:    readTimeout,
		WriteTimeout:   writeTimeout,
		MaxHeaderBytes: maxHeaderBytes,
	}
	return srv
}
