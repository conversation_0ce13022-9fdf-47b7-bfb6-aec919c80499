package response

import "loop/pkg/types"

type LoginUserInfoResp struct {
	User  *UserInfoResp `json:"user"`
	Token string        `json:"token"`
}

type UserInfoResp struct {
	Id             string `json:"id"`
	UserName       string `json:"username"`
	Nickname       string `json:"nickname"`
	Status         int    `json:"status"`
	Avatar         string `json:"avatar"`
	NativeLangCode string `json:"nativeLangCode"`
	TargetLangCode string `json:"targetLangCode"`
}
type LoginAdminUserInfoResp struct {
	User  *UserAdminInfoResp `json:"user"`
	Token string             `json:"token"`
}
type UserAdminInfoResp struct {
	Id       string `json:"id"`
	UserName string `json:"username"`
	Nickname string `json:"nickname"`
	Status   int    `json:"status"`
	Avatar   string `json:"avatar"`
	RoleName string `json:"roleName"`
}

type UserConfigResp struct {
	UserPlayerConfig   *UserPlayerConfigResp `json:"playerConfig"`
	MotherTongueConfig *MotherTongueResp     `json:"motherTongue"`
}
type UserPlayerConfigResp struct {
	SubtitleFontSize              int             `json:"subtitleFontSize"`
	SingleRepeatCount             int             `json:"singleRepeatCount"`
	ShowSubtitleNum               int             `json:"showSubtitleNum"`
	SubtitleTextBottomHeightRatio float32         `json:"subtitleTextBottomHeightRatio"`
	ShowSubtitleWhenRecordEnd     bool            `json:"showSubtitleWhenRecordEnd"`
	AutoPlayRecordWhenRecordEnd   bool            `json:"autoPlayRecordWhenRecordEnd"`
	AutoRecord                    bool            `json:"autoRecord"`
	AutoStopRecord                bool            `json:"autoStopRecord"`
	OpenSingleRepeat              bool            `json:"openSingleRepeat"`
	CoverSubtitle                 bool            `json:"coverSubtitle"`
	MenuSort                      *types.IntArray `json:"menuSort"`
}
type WatchHistoryResp struct {
	Id           string                    `json:"id"`
	Uid          string                    `json:"uid"`
	LastTime     string                    `json:"lastTime"` //最近一次打开的时间
	Position     int64                     `json:"position"` //当前进度
	ResourceId   string                    `json:"resourceId"`
	ResourceType int                       `json:"resourceType"`
	Resource     *WatchHistoryResourceResp `json:"resource"`
}
type WatchHistoryResourceResp struct {
	Name            string            `json:"name"`
	Position        int64             `json:"position"`
	LocalVideoPaths types.StringArray `json:"localVideoPaths"`
	Cover           string            `json:"cover"`
	VideoURL        string            `json:"videoURL"`
}
