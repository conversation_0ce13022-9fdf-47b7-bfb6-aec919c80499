package response

import (
	"time"
)

// StudyResourceResp 学习资源响应
type StudyResourceResp struct {
	Id          int64  `json:"id"`          // 资源ID
	StageDesc   string `json:"stage_desc"`  // 阶段描述
	Type        string `json:"type"`        // 类型：video-视频 audio-音频 reading-阅读 exercise-练习 quiz-测验
	Title       string `json:"title"`       // 标题
	Description string `json:"description"` // 描述
	Url         string `json:"url"`         // 资源URL
	Duration    int    `json:"duration"`    // 时长(秒)
	Order       int    `json:"order"`       // 顺序
	Status      string `json:"status"`      // 状态：not_started-未开始 in_progress-进行中 completed-已完成
	Progress    int    `json:"progress"`    // 进度(0-100)
	StartTime   int64  `json:"start_time"`  // 开始时间
	EndTime     int64  `json:"end_time"`    // 结束时间
}

// StageResp 学习阶段响应
type StageResp struct {
	Id          string              `json:"id"`
	PlanId      string              `json:"planId"`
	Name        string              `json:"name"`
	Description string              `json:"description"`
	Progress    float64             `json:"progress"`
	Status      int                 `json:"status"` // 状态：0-未开始 1-进行中 2-已完成
	Sort        int                 `json:"sort"`
	Resources   []StudyResourceResp `json:"resources,omitempty"`
}

// StudyPlanResp 学习计划响应
type StudyPlanResp struct {
	Id           string     `json:"id"`
	UserId       string     `json:"userId"`
	StartLevel   int        `json:"startLevel"`
	TargetLevel  int        `json:"targetLevel"`
	DailyMinutes int        `json:"dailyMinutes"`
	Status       int        `json:"status"` // 状态：0-未开始 1-进行中 2-已完成
	CurrentStage int        `json:"currentStage"`
	TotalStages  int        `json:"totalStages"`
	Progress     float64    `json:"progress"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	CompletedAt  *time.Time `json:"completedAt,omitempty"`

	// 问卷信息
	MotivationSource string   `json:"motivationSource"`
	ImproveSkills    []string `json:"improveSkills"`
	CurrentLevel     int      `json:"currentLevel"`
	DailyStudyTime   int      `json:"dailyStudyTime"`

	// 阶段信息
	Stages []StageResp `json:"stages,omitempty"`
}

// LevelDescResp 等级描述响应
type LevelDescResp struct {
	Level       string `json:"level"`
	Description string `json:"description"`
}
