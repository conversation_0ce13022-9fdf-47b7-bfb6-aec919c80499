package response

type ResourceResp struct {
	Id               string `json:"id"`
	VideoURL         string `json:"videoUrl"`
	Cover            string `json:"cover"`
	Duration         int64  `json:"duration"`
	Type             string `json:"type"`
	PublishedAt      string `json:"publishedAt"`
	Author           string `json:"author"`
	Priority         int    `json:"priority"`
	DefaultLangTitle string `json:"defaultLangTitle"`
}

type ResourceKVResp struct {
	NativeLangId uint   `json:"nativeLangId"`
	Content      string `json:"content"`
}

type ResourceHomeResp struct {
	FeatureContent   []ResourceHomeFeatureContentResp `json:"recommend"`
	CategoryTypeList []ResourceHomeCategoryTypeResp   `json:"categoryTypeList"`
}
type ResourceHomeFeatureContentResp struct {
	Id          string `json:"id"`
	Cover       string `json:"cover"`
	ContentType int    `json:"contentType"`
	Name        string `json:"name"`
	VideoURL    string `json:"videoUrl"`
}
type ResourceHomeCategoryTypeResp struct {
	Name         string         `json:"name"`
	CategoryResp []CategoryResp `json:"categories"`
}

type ResourceHomeItemResp struct {
	Id          string   `json:"id"`
	ContentType int      `json:"contentType"`
	Name        string   `json:"name"`
	Cover       string   `json:"cover"`
	VideoURL    string   `json:"videoUrl"`
	Tags        []string `json:"tags,omitempty"`     // 资源标签
	Duration    int64    `json:"duration,omitempty"` // 视频时长（秒）
	Author      string   `json:"author,omitempty"`   // 作者
}

// PagedResourcesResp 分页资源响应
type PagedResourcesResp struct {
	Items      []ResourceHomeItemResp `json:"items"`      // 资源列表
	Total      int64                  `json:"total"`      // 总数
	Page       int                    `json:"page"`       // 当前页码
	PageSize   int                    `json:"pageSize"`   // 每页数量
	TotalPages int64                  `json:"totalPages"` // 总页数
}

type SeriesWithLang struct {
	Id          string `json:"id"`
	Cover       string `json:"cover"`
	Statement   string `json:"statement"`
	Priority    int64  `json:"priority"`
	Title       string `json:"title"`
	Description string `json:"description"`
}
type ResourceWithLang struct {
	Id          string
	Cover       string
	ContentType int
	Title       string
	VideoURL    string
	Duration    int64
	Author      string
	// 其他你需要的字段
}
