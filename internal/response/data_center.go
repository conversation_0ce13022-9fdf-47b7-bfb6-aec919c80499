package response

type DataEpisodeResp struct {
	Id                 string `json:"id"`
	Uid                string `json:"uid"`
	ResourceId         string `json:"resourceId"`
	ResourceType       int    `json:"resourceType"`
	EpisodeName        string `json:"episodeName"` //剧集名称
	Status             int    `json:"status"`
	TargetLsTimes      int64  `json:"targetLsTimes"`
	TargetDesc         string `json:"targetDesc"` //剧集对应的目标文案
	CurrentLsTimes     int64  `json:"currentLsTimes"`
	TotalLearnDuration int64  `json:"totalLearnDuration"`
	TotalLearnDayTimes int64  `json:"totalLearnDayTimes"` //累计学习天数
}

// 今日学习数据
type DataEpisodeDailyTodayResp struct {
	ResourceId   string `json:"resourceId"`
	ResourceType int    `json:"resourceType"`
	EpisodeName  string `json:"episodeName"` //剧集名称

	CurrentLsTimes     int64 `json:"currentLsTimes"`
	TargetLsTimes      int64 `json:"targetLsTimes"`      //目标次数
	TotalLearnDuration int64 `json:"totalLearnDuration"` //累计学习时长
}

// 累计学习
type DataEpisodeTotalResp struct {
	TotalLearnDuration  int64 `json:"totalLearnDuration"`  //累计学习时长
	TotalLearnDayTimes  int64 `json:"totalLearnDayTimes"`  //累计学习天数
	TotalLearnVideoSize int   `json:"totalLearnVideoSize"` //累计视频个数
}

type DataEpisodeHomeResp struct {
	Daily    *[]DataEpisodeDailyTodayResp `json:"daily"`
	Total    *DataEpisodeTotalResp        `json:"total"`
	Episodes *[]DataEpisodeResp           `json:"episodes"` //这里是对应时间是否有学习的数据
}

type DataEpisodeChartResp struct {
	TotalLearnDuration int64                     `json:"totalLearnDuration"` //周累计学习时长
	Datas              []DataEpisodeChartSubResp `json:"datas"`
}

type DataEpisodeChartSubResp struct {
	Duration        int64  `json:"duration"`
	StartDate       int64  `json:"startDate"`
	EndDate         int64  `json:"endDate"`
	StartDateString string `json:"startDateString"`
	EndDateString   string `json:"endDateString"`
}
type EpisodeLsData struct {
	TotalLearnDuration int64 `json:"totalLearnDuration"`
	FinishTime         int64 `json:"finishTime"`
	LsTimes            int   `json:"lsTimes"`
}

type EpisodeLsDataResp struct {
	Episodes      DataEpisodeResp `json:"episode"`
	EpisodeLsData []EpisodeLsData `json:"episodeLsData"`
}
