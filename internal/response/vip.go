package response

type ProductListItemResp struct {
	Id             uint    `json:"id"`
	Name           string  `json:"name"`
	Type           int     `json:"type"`
	IsSubscription int     `json:"isSubscription"`
	Price          float64 `json:"price"`
	OriginPrice    float64 `json:"originPrice"`
	OutProductCode string  `json:"outProductCode"`
	LangCode       string  `json:"langCode"`
	IosProductId   string  `json:"iosProductId"`
}
type CreatePayOrderResp struct {
	UUID string `json:"uuid"`
}
type UserVipResp struct {
	IsVip             int     `json:"isVip"`
	IsSubscription    int     `json:"isSubscription"`
	VipID             uint    `json:"vipID"`
	ExpireDate        string  `json:"expireDate"`
	ExpireTimestamp   int64   `json:"expireTimestamp"`
	ProductID         uint    `json:"productID"`
	ProductName       string  `json:"productName"`
	FirstCycleAmount  float64 `json:"firstCycleAmount"`
	NextCycleAmount   float64 `json:"nextCycleAmount"`
	NextPaidDate      string  `json:"nextPaidDate"`
	NextPaidTimestamp int64   `json:"nextPaidTimestamp"`
}
type TradeOrderResp struct {
	OrderNo                    string  `json:"orderNo"`
	OutTransactionId           string  `json:"outTransactionId"`
	AppleOriginalTransactionId string  `json:"appleOriginalTransactionId"`
	Uid                        string  `json:"uid"`
	ProductID                  uint    `json:"productID"`
	ProductName                string  `json:"productName"`
	ProductType                int     `json:"productType"`
	UserSubscriptionID         uint    `json:"userSubscriptionID"`
	Currency                   string  `json:"currency"`
	Amount                     float64 `json:"amount"`
	PaymentProvider            int     `json:"paymentProvider"`
	PaidTimestamp              int64   `json:"paidTimestamp"`
	Status                     int     `json:"status"`
}
