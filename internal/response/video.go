package response

import "loop/pkg/types"

// 返回给app的 本地视频的详情信息
type VideoDetailResp struct {
	ResourceId            string                       `json:"resourceId"`
	ResourceType          int                          `json:"resourceType"`
	SubtitleUrl           string                       `json:"subtitleUrl"`
	NativeLangSubtitleUrl string                       `json:"nativeLangSubtitleUrl"`
	OriginSubtitleUrl     string                       `json:"originSubtitleUrl"`
	Skips                 types.VideoTimeIntervalArray `json:"skipList"`
	Notes                 []NotesResp                  `json:"notes"`
	Position              int64                        `json:"position"`
	SentenceCollects      types.VideoTimeIntervalArray `json:"sentenceCollects"`
	CurrentLsTimes        int64                        `json:"currentLsTimes"`
	PlayUrl               string                       `json:"playUrl"`
}

type NotesResp struct {
	Id             string `json:"id"`
	Content        string `json:"content"`
	VideoStartTime int64  `json:"videoStartTime"`
	VideoEndTime   int64  `json:"videoEndTime"`
}
