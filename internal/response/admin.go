package response

type CategoryAdminResp struct {
	CategoryResp
	Priority int `json:"priority"`
}

type ResourceListItemAdminResp struct {
	ResourceResp
	IsFeaturedContent bool `json:"isFeaturedContent"`
}
type ResourceDetailAdminResp struct {
	ResourceResp
	ResourceRelations      []ResourceRelationResp `json:"resourceRelations"`
	OriginResourceRelation ResourceRelationResp   `json:"originResourceRelation"`
	Categories             []CategoryResp         `json:"categories"`
	Serieses               []SeriesItemAdminResp  `json:"serieses"`
	IsFeaturedContent      bool                   `json:"isFeaturedContent"`
}
type ResourceRelationResp struct {
	LangCode    string `form:"langCode" json:"langCode" binding:"required"`
	Title       string `json:"title" binding:"required"`
	Description string `json:"description" binding:"required"`
	SubtitleUrl string `json:"subtitleUrl" binding:"required"`
}

type SeriesItemAdminResp struct {
	Id       string `json:"id"`
	Priority int    `json:"priority"`
	Cover    string `json:"cover"`
}
type SeriesDetailAdminResp struct {
	SeriesItemAdminResp
	SeriesRelations   []SeriesRelationResp `json:"seriesRelations"`
	CategoryIds       []string             `json:"categoryIds"`
	IsFeaturedContent bool                 `json:"isFeaturedContent"`
}

type SeriesRelationResp struct {
	LangCode    string `form:"langCode" json:"langCode" binding:"required"`
	Title       string `json:"title" binding:"required"`
	Description string `json:"description" binding:"required"`
	Statement   string `json:"statement"`
}
