package config

import (
	"path"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

var (
	logFile = "gin.log"
)

func NewLogConfig(conf *Config) error {
	// 打开文件
	logFileName := path.Join(conf.LogPath, logFile)
	// 使用滚动压缩方式记录日志
	rolling(logFileName)
	// 设置日志输出JSON格式
	// LogInstance.SetFormatter(&logrus.JSONFormatter{})
	logrus.SetFormatter(&logrus.TextFormatter{})
	// 设置日志记录级别
	logrus.SetLevel(logrus.DebugLevel)
	return nil
}

// 日志滚动设置
func rolling(logFile string) {
	// 设置输出
	logrus.SetOutput(&lumberjack.Logger{
		Filename:   logFile, //日志文件位置
		MaxSize:    100,     // 单文件最大容量,单位是MB
		MaxBackups: 3,       // 最大保留过期文件个数
		MaxAge:     30,      // 保留过期文件的最大时间间隔,单位是天
		Compress:   true,    // 是否需要压缩滚动日志, 使用的 gzip 压缩
	})
}
