package config

import (
	"github.com/go-pay/gopay/apple"
	"github.com/go-pay/xlog"
)

func NewPayConfig() error {
	// 初始化通联客户端
	// iss：issuer ID App 内购买中配置
	// bid：bundle ID 应用程序的唯一标识符。
	// kid：private key ID  App 内购买中配置
	// privateKey：私钥文件读取后的字符串内容
	// isProd：是否是正式环境
	_, err := apple.NewClient("c74ce851-693e-459e-bd32-f091acbe46d5", "com.forest.ls", "6DCP2348U5", `*****************************************************************************************************************************************************************************************************************************************************************`, false)
	if err != nil {
		xlog.Error(err)
		return err
	}
	return nil
}
