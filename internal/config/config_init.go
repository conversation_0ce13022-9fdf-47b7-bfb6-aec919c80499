package config

import (
	"flag"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gopkg.in/yaml.v2"
)

// 在部署release的时候会进行路径映射 - ../../configs/config-prod.yaml:/configs/config.yaml
// 所以不需要care这里的release路径
var configFile = flag.String("config", "../configs/config.yaml", "the config file")

func NewConfig() (*Config, error) {
	gin.DisableConsoleColor()
	conf := loadConfigFile(*configFile)
	return conf, nil
}
func loadConfigFile(filename string) *Config {
	sett := &Config{}
	if yamlFile, err := os.ReadFile(filename); err != nil {
		logrus.Error(err)
	} else if err = yaml.Unmarshal(yamlFile, sett); err != nil {
		logrus.Error(err)
	}
	return sett
}
