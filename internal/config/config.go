package config

import (
	"loop/pkg/i18n"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type RedisConfig struct {
	Addr     string `mapstructure:"addr"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

type ServerConfig struct {
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
}

func Setup() *Config {
	conf, err := NewConfig()
	if err != nil {
		logrus.Printf("NewConfig err: %v\n", err)
		panic(err)
	}
	if !gin.IsDebugging() {
		err = NewLogConfig(conf)
		if err != nil {
			logrus.Printf("NewLogConfig err: %v\n", err)
			panic(err)
		}
	}

	conf.Server.ReadTimeout = conf.Server.ReadTimeout * time.Second
	conf.Server.WriteTimeout = conf.Server.WriteTimeout * time.Second

	i18n.SetPath("../i18n")
	err = NewPayConfig()
	if err != nil {
		logrus.Printf("NewPayConfig err: %v\n", err)
		panic(err)
	}
	return conf
}
