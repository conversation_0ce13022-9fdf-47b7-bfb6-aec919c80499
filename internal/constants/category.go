package constants

import "loop/pkg/enum"

// CategoryTypeInfo 分类类型信息结构
type CategoryTypeInfo struct {
	ID          string
	Name        string
	Description string
	Priority    int64
	EnumValue   enum.CategoryType
}

// 预定义的分类类型信息
var CategoryTypes = []CategoryTypeInfo{
	{
		ID:          "1",
		Name:        enum.Level.GetName(),
		Description: enum.Level.GetDescription(),
		Priority:    enum.Level.GetPriority(),
		EnumValue:   enum.Level,
	},
	{
		ID:          "2",
		Name:        enum.Scene.GetName(),
		Description: enum.Scene.GetDescription(),
		Priority:    enum.Scene.GetPriority(),
		EnumValue:   enum.Scene,
	},
	{
		ID:          "3",
		Name:        enum.Topic.GetName(),
		Description: enum.Topic.GetDescription(),
		Priority:    enum.Topic.GetPriority(),
		EnumValue:   enum.Topic,
	},
}

// GetCategoryTypeByID 根据ID获取分类类型信息
func GetCategoryTypeByID(id string) *CategoryTypeInfo {
	for _, ct := range CategoryTypes {
		if ct.ID == id {
			return &ct
		}
	}
	return nil
}

// GetCategoryTypeByEnum 根据枚举值获取分类类型信息
func GetCategoryTypeByEnum(enumValue enum.CategoryType) *CategoryTypeInfo {
	for _, ct := range CategoryTypes {
		if ct.EnumValue == enumValue {
			return &ct
		}
	}
	return nil
}

// GetAllCategoryTypes 获取所有分类类型信息
func GetAllCategoryTypes() []CategoryTypeInfo {
	return CategoryTypes
}
