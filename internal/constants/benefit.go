package constants

// BenefitType 权益类型
type BenefitCode string

const (
	// 权益组编码
	BenefitGroupVideoSizeLimit        BenefitCode = "VIDEO_SIZE_LIMIT"        // M为单位
	BenefitGroupAiCallsLimit          BenefitCode = "AI_CALLS_LIMIT"          // 次为单位
	BenefitGroupSubtitleDialogueLimit BenefitCode = "SUBTITLE_DIALOGUE_LIMIT" // 字幕对话次数为单位

	// 视频大小限制组权益
	BenefitCodeVideoSizeLimitBasic BenefitCode = "UPLOAD_LIMIT_BASIC" // 普通会员视频限制
	BenefitCodeVideoSizeLimitPro   BenefitCode = "UPLOAD_LIMIT_PRO"   // Pro会员视频限制
	BenefitCodeVideoSizeLimitUltra BenefitCode = "UPLOAD_LIMIT_ULTRA" // Ultra会员视频限制

	// AI调用次数组权益
	BenefitCodeAICallsBasic BenefitCode = "AI_CALLS_BASIC" // 普通会员AI调用次数
	BenefitCodeAICallsPro   BenefitCode = "AI_CALLS_PRO"   // Pro会员AI调用次数
	BenefitCodeAICallsUltra BenefitCode = "AI_CALLS_ULTRA" // Ultra会员AI调用次数

	// 字幕对话次数组权益
	BenefitCodeSubtitleDialogueBasic BenefitCode = "SUBTITLE_DIALOGUE_BASIC" // 普通会员字幕对话次数
	BenefitCodeSubtitleDialoguePro   BenefitCode = "SUBTITLE_DIALOGUE_PRO"   // Pro会员字幕对话次数
	BenefitCodeSubtitleDialogueUltra BenefitCode = "SUBTITLE_DIALOGUE_ULTRA" // Ultra会员字幕对话次数
)
