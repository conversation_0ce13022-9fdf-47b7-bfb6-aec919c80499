package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/enum"
	"loop/pkg/lang"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/thoas/go-funk"
)

func NewResourceAdminRepo(
	model *model.ResourceAdminModel,
	resourceModel *model.ResourceModel,
	config *config.Config,
) *ResourceAdminRepo {
	return &ResourceAdminRepo{
		model:         model,
		resourceModel: resourceModel,
		config:        config,
	}
}

type ResourceAdminRepo struct {
	model         *model.ResourceAdminModel
	resourceModel *model.ResourceModel
	config        *config.Config
}

// 添加资源
func (s *ResourceAdminRepo) SaveOne(c *gin.Context, req request.ResourceAddReq) *web.JsonResult {
	if len(req.CategoryIds) != 0 {
		err := s.model.CheckCategoryIds(req.CategoryIds)
		if err != nil {
			return web.JsonInternalError(err)
		}
	}

	err := s.model.AddResource(req)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}
func (s *ResourceAdminRepo) DeleteResource(c *gin.Context, req request.IdReq) *web.JsonResult {
	err := s.model.DeleteResources([]string{req.Id})
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}
func (s *ResourceAdminRepo) DeleteMultiResource(c *gin.Context, req request.IdsReq) *web.JsonResult {
	err := s.model.DeleteResources(req.Id)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}

func (s *ResourceAdminRepo) GetResourceDetail(c *gin.Context, req request.IdReq) *web.JsonResult {
	resourceComplete, err := s.model.GetResourceDetail(req.Id)
	if err != nil {
		return web.JsonInternalError(err)
	}
	var resp response.ResourceDetailAdminResp
	copier.Copy(&resp, resourceComplete)
	featuredContentIds, err := s.resourceModel.GetFeatureContentIds()
	if err != nil {
		return web.JsonInternalError(err)
	}
	resp.IsFeaturedContent = funk.Contains(featuredContentIds, resp.Id)
	return web.JsonData(resp)
}

func (s *ResourceAdminRepo) GetResourceList(c *gin.Context, req request.ResourceListReq) *web.JsonResult {
	var resources []*model.Resource
	var count int64
	var err error
	if req.CategoryID == "" {
		count, err = s.model.GetOrderPage(&resources, enum.SortType(req.SortType).DescString(), req.CurrentPage, req.PageSize)
		if err != nil {
			return web.JsonInternalError(err)
		}
	} else {
		//从关联表中获取到CategoryID所对应的resourceIds
		categoryResources := []*model.CategoryResourceRelations{}
		err = s.model.GetList(&categoryResources, model.CategoryResourceRelations{CategoryId: req.CategoryID})
		if err != nil {
			return web.JsonInternalError(err)
		}
		resourceIds := funk.FlatMap(categoryResources, func(cr *model.CategoryResourceRelations) []string {
			return []string{cr.ResourceId}
		}).([]string)
		if len(resourceIds) != 0 {
			count, err = s.model.GetOrderQueryPage(&resources, enum.SortType(req.SortType).DescString(), req.CurrentPage, req.PageSize, "id IN ?", resourceIds)
			if err != nil {
				return web.JsonInternalError(err)
			}
		} else {
			count = 0
		}
	}

	featuredContentIds, err := s.resourceModel.GetFeatureContentIds()
	if err != nil {
		return web.JsonInternalError(err)
	}
	var resps []response.ResourceListItemAdminResp
	copier.Copy(&resps, resources)
	for i := range resps {
		rr, err := s.resourceModel.GetDefalutResourceRelationByLang(resps[i].Id, lang.GetPreferredLanguage(c).Code())
		if err != nil {
			return web.JsonInternalError(err)
		}
		resps[i].DefaultLangTitle = rr.Title
		resps[i].IsFeaturedContent = funk.Contains(featuredContentIds, resps[i].Id)
	}

	return web.JsonData(web.PageJsonResult{
		Total: count,
		Data:  resps,
	})
}
func (s *ResourceAdminRepo) SetFeaturedContent(c *gin.Context, req request.IdReq) *web.JsonResult {
	found, err := s.model.GetOne(&model.FeaturedContent{}, model.FeaturedContent{ContentType: int(enum.Resource), ContentID: req.Id})
	if err != nil {
		return web.JsonInternalError(err)
	}
	if found {
		err := s.model.Delete(&model.FeaturedContent{}, model.FeaturedContent{ContentType: int(enum.Resource), ContentID: req.Id}).Error
		if err != nil {
			return web.JsonInternalError(err)
		}
	} else {
		err = s.model.SaveOne(&model.FeaturedContent{ContentType: int(enum.Resource), ContentID: req.Id})
		if err != nil {
			return web.JsonInternalError(err)
		}
	}
	return web.JsonOK()
}

func (s *ResourceAdminRepo) SetPriority(c *gin.Context, req request.PriorityReq) *web.JsonResult {
	var result model.Resource
	found, err := s.model.GetOne(&result, model.Resource{Model: model.Model{Id: req.Id}})
	if err != nil || !found {
		return web.JsonInternalError(err)
	}
	result.Priority = req.Priority
	if err := s.model.Update(&result, &model.Resource{Model: model.Model{Id: req.Id}}); err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonOK()
}
