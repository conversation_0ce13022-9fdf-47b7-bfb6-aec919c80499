package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/jwtx"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

func NewSettingRepo(
	model *model.UserModel,
	lsResourceModel *model.ResourceModel,
	config *config.Config,
) *SettingRepo {
	return &SettingRepo{
		model:           model,
		lsResourceModel: lsResourceModel,
		config:          config,
	}
}

type SettingRepo struct {
	model           *model.UserModel
	lsResourceModel *model.ResourceModel
	config          *config.Config
}

func (s *SettingRepo) FetchConfig(c *gin.Context) *web.JsonResult {
	uid := jwtx.GetUid(c)
	playerConfig, err := s.model.GetPlayerConfig(uid)
	if err != nil {
		return web.JsonInternalError(err)
	}
	playerConfigResp := response.UserPlayerConfigResp{}
	if playerConfig == nil {
		playerConfig = s.createDefautPlayerConfig(uid)
	}
	copier.Copy(&playerConfigResp, &playerConfig)
	userConfigResp := response.UserConfigResp{}
	userConfigResp.UserPlayerConfig = &playerConfigResp

	return web.JsonData(userConfigResp)
}

func (s *SettingRepo) UpdateUserPlayerConfig(c *gin.Context, req request.UpdateUserPlayerConfigRequest) *web.JsonResult {
	uid := jwtx.GetUid(c)
	playerConfig, err := s.model.GetPlayerConfig(uid)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if playerConfig == nil {
		playerConfig = s.createDefautPlayerConfig(uid)
	}
	if playerConfig == nil {
		return web.JsonInternalError(err)
	}

	fields := make(map[string]interface{})
	if req.SubtitleFontSize != nil {
		fields["subtitle_font_size"] = *req.SubtitleFontSize
	}
	if req.SingleRepeatCount != nil {
		fields["single_repeat_count"] = *req.SingleRepeatCount
	}
	if req.ShowSubtitleNum != nil {
		fields["show_subtitle_num"] = *req.ShowSubtitleNum
	}
	if req.SubtitleTextBottomHeightRatio != nil {
		fields["subtitle_text_bottom_height_ratio"] = *req.SubtitleTextBottomHeightRatio
	}
	if req.ShowSubtitleWhenRecordEnd != nil {
		fields["show_subtitle_when_record_end"] = *req.ShowSubtitleWhenRecordEnd
	}
	if req.AutoPlayRecordWhenRecordEnd != nil {
		fields["auto_play_record_when_record_end"] = *req.AutoPlayRecordWhenRecordEnd
	}
	if req.AutoRecord != nil {
		fields["auto_record"] = *req.AutoRecord
	}
	if req.AutoStopRecord != nil {
		fields["auto_stop_record"] = *req.AutoStopRecord
	}
	if req.OpenSingleRepeat != nil {
		fields["open_single_repeat"] = *req.OpenSingleRepeat
	}
	if req.CoverSubtitle != nil {
		fields["cover_subtitle"] = *req.CoverSubtitle
	}
	if req.MenuSort != nil {
		fields["menu_sort"] = *req.MenuSort
	}
	if err := s.model.Update(fields, &model.UserPlayerConfig{Uid: uid}); err != nil {
		return web.JsonInternalError(err)
	}
	return s.FetchConfig(c)
}

func (s *SettingRepo) createDefautPlayerConfig(uid string) *model.UserPlayerConfig {
	playerConfig := model.UserPlayerConfig{
		SubtitleFontSize:              16,
		SubtitleTextBottomHeightRatio: 0.0,
		SingleRepeatCount:             1,
		ShowSubtitleNum:               1,
		ShowSubtitleWhenRecordEnd:     true,
		AutoPlayRecordWhenRecordEnd:   true,
		Uid:                           uid,
	}
	err := s.model.SaveOne(&playerConfig)
	if err != nil {
		return nil
	}
	return &playerConfig
}
