package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/response"
	"loop/pkg/lang"
	"loop/pkg/web"
)

func NewV1Repo(
	model *model.DbModel,
	config *config.Config,
) *V1Repo {
	return &V1Repo{
		model:  model,
		config: config,
	}
}

type V1Repo struct {
	model  *model.DbModel
	config *config.Config
}

func (s *V1Repo) GetLangs() *web.JsonResult {
	var nativeLangs []response.LangResp

	for _, langKey := range lang.OrderedLanguages {
		nativeLangs = append(nativeLangs, response.LangResp{
			Code: langKey.Code(),
			Name: langKey.Name(),
		})
	}
	var targetLangs []response.LangResp

	for _, langKey := range lang.OrderedLanguages {
		targetLangs = append(targetLangs, response.LangResp{
			Code: langKey.Code(),
			Name: langKey.Name(),
		})
	}
	return web.JsonData(response.LangsResp{
		NativeLanguages: nativeLangs,
		TargetLanguages: targetLangs,
	})
}
func (s *V1Repo) GetNativeLangs() *web.JsonResult {
	var langResps []response.LangResp

	for _, langKey := range lang.OrderedLanguages {
		langResps = append(langResps, response.LangResp{
			Code: langKey.Code(),
			Name: langKey.Name(),
		})
	}
	return web.JsonData(langResps)
}

func (s *V1Repo) GetTargetLangs() *web.JsonResult {
	var langResps []response.LangResp

	for _, langKey := range lang.OrderedLanguages {
		langResps = append(langResps, response.LangResp{
			Code: langKey.Code(),
			Name: langKey.Name(),
		})
	}
	return web.JsonData(langResps)
}
