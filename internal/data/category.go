package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
)

func NewCategoryRepo(
	model *model.CategoryModel,
	resModel *model.ResourceModel,
	resRepo *ResourceRepo,
	config *config.Config,
) *CategoryRepo {
	return &CategoryRepo{
		model:    model,
		resRepo:  resRepo,
		resModel: resModel,
		config:   config,
	}
}

type CategoryRepo struct {
	model    *model.CategoryModel
	resModel *model.ResourceModel
	resRepo  *ResourceRepo
	config   *config.Config
}

func (s *CategoryRepo) SaveOne(c *gin.Context, req request.ResCategoryReq) *web.JsonResult {
	query := model.Category{Name: req.Name}
	result := model.Category{}

	if found, _ := s.model.GetOne(&result, query); found {
		return web.JsonEntityNotFound(c)
	}
	return SaveOne[model.Category](s.model.DbModel, c, req)
}
