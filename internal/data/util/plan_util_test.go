package util

import (
	"loop/internal/model"
	"strings"
	"testing"
)

func TestBuildStagePlanPrompt(t *testing.T) {
	// 创建测试数据
	questionnaire := &model.UserQuestionnaire{
		CurrentLevel:      "A1",
		TargetLevel:       "B1",
		DailyStudyMinutes: 30,
		MotivationSource:  "工作需求",
		DesiredAbility:    "口语和听力",
	}

	stageDesc := "A1-1"
	resourceInfos := []map[string]any{
		{
			"id":       "resource1",
			"tags":     []string{"语法", "A1"},
			"duration": 300,
		},
		{
			"id":       "resource2",
			"tags":     []string{"听力", "A1"},
			"duration": 600,
		},
	}
	stageDuration := "60-90h"

	// 调用函数
	prompt := BuildStagePlanPrompt(questionnaire, stageDesc, resourceInfos, stageDuration)

	// 验证结果
	if prompt == "" {
		t.Error("生成的提示信息为空")
	}

	// 验证提示信息中是否包含关键信息
	expectedStrings := []string{
		"每天学习时间",
		stageDesc,
		"resource1",
		"resource2",
		"语法",
		"听力",
	}

	for _, expected := range expectedStrings {
		if !strings.Contains(prompt, expected) {
			t.Errorf("提示信息中缺少预期内容: %s", expected)
		}
	}
}

func TestExtractJSONFromContent(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "普通JSON",
			input:    `{"key": "value"}`,
			expected: `{"key": "value"}`,
		},
		{
			name:     "带json标记的代码块",
			input:    "```json\n{\"key\": \"value\"}\n```",
			expected: `{"key": "value"}`,
		},
		{
			name:     "带普通标记的代码块",
			input:    "```\n{\"key\": \"value\"}\n```",
			expected: `{"key": "value"}`,
		},
		{
			name:     "多个代码块",
			input:    "```json\n{\"key\": \"value1\"}\n```\n```json\n{\"key\": \"value2\"}\n```",
			expected: `{"key": "value1"}{"key": "value2"}`,
		},
		{
			name:     "代码块中间有文本",
			input:    "some text\n```json\n{\"key\": \"value\"}\n```\nmore text",
			expected: `some text{"key": "value"}more text`,
		},
		{
			name:     "嵌套代码块",
			input:    "```json\n{\"key\": \"```nested```\"}\n```",
			expected: `{"key": "nested"}`,
		},
		{
			name:     "多个空行",
			input:    "```json\n\n{\"key\": \"value\"}\n\n```",
			expected: `{"key": "value"}`,
		},
		{
			name:     "不规范的代码块",
			input:    "```json{\"key\": \"value\"}```",
			expected: `{"key": "value"}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ExtractJSONFromContent(tt.input)
			if err != nil {
				t.Errorf("ExtractJSONFromContent() error = %v", err)
				return
			}
			if result != tt.expected {
				t.Errorf("ExtractJSONFromContent() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestBuildStageResourcesPrompt(t *testing.T) {
	// 创建测试数据
	questionnaire := &model.UserQuestionnaire{
		CurrentLevel:      "A1",
		TargetLevel:       "B1",
		DailyStudyMinutes: 30,
		MotivationSource:  "工作需求",
		DesiredAbility:    "口语和听力",
	}

	// 使用实际资源数据
	resourceInfos := []map[string]any{
		{
			"id":       "1849101942031130624",
			"tags":     []string{"C1", "高级语法", "求职"},
			"duration": 500,
		},
		{
			"id":       "1854165993102798848",
			"tags":     []string{"动画片", "中级", "日常", "可爱"},
			"duration": 660,
		},
		{
			"id":       "1925084041682374656",
			"tags":     []string{"城市生活", "基础语法", "角色扮演", "初级（A0-A1）"},
			"duration": 300,
		},
		{
			"id":       "1925084237262770176",
			"tags":     []string{"奇幻词汇", "动词短语", "发音挑战", "A2-B1"},
			"duration": 300,
		},
		{
			"id":       "1925084237262770171",
			"tags":     []string{"疯狂动物城", "动画", "日常", "A2-B1"},
			"duration": 700,
		},
		{
			"id":       "1925084237262770172",
			"tags":     []string{"艾米丽在巴黎", "职场", "日常", "A2-B1"},
			"duration": 1200,
		},
		{
			"id":       "1925084237262770112",
			"tags":     []string{"演讲", "职场", "电影", "C1-C2"},
			"duration": 600,
		},
		{
			"id":       "1925084237262770122",
			"tags":     []string{"跨境电商英语", "外贸英语邮件", "英语谈判技巧", "B1-B2"},
			"duration": 900,
		},
		{
			"id":       "1925084237262770132",
			"tags":     []string{"电影台词模仿", "高分技巧", "TED演讲", "A1-B1"},
			"duration": 450,
		},
		// 新增资源
		{
			"id":       "1925084237262770142",
			"tags":     []string{"旅游英语", "日常对话", "文化体验", "A1-A2"},
			"duration": 400,
		},
		{
			"id":       "1925084237262770152",
			"tags":     []string{"商务会议", "职场英语", "谈判技巧", "B2-C1"},
			"duration": 800,
		},
		{
			"id":       "1925084237262770162",
			"tags":     []string{"英语写作", "学术论文", "邮件写作", "B1-C1"},
			"duration": 750,
		},
		{
			"id":       "1925084237262770173",
			"tags":     []string{"英语新闻", "时事热点", "听力训练", "B2-C2"},
			"duration": 550,
		},
		{
			"id":       "1925084237262770174",
			"tags":     []string{"英语面试", "求职技巧", "自我介绍", "B1-C1"},
			"duration": 480,
		},
		{
			"id":       "1925084237262770175",
			"tags":     []string{"英语歌曲", "流行音乐", "发音练习", "A2-B1"},
			"duration": 350,
		},
		{
			"id":       "1925084237262770176",
			"tags":     []string{"英语播客", "生活话题", "听力理解", "B1-B2"},
			"duration": 650,
		},
		{
			"id":       "1925084237262770177",
			"tags":     []string{"英语小说", "文学欣赏", "阅读理解", "C1-C2"},
			"duration": 1000,
		},
		{
			"id":       "1925084237262770178",
			"tags":     []string{"英语演讲", "公众演讲", "表达技巧", "B2-C1"},
			"duration": 720,
		},
		{
			"id":       "1925084237262770179",
			"tags":     []string{"英语俚语", "日常用语", "地道表达", "B1-C1"},
			"duration": 380,
		},
	}

	// 创建阶段时长映射
	stageDurations := map[string]string{
		"A1-1": "60-90h",
		"A1-2": "60-90h",
		"A2-1": "100-150h",
		"A2-2": "100-150h",
	}

	// 调用函数并打印结果
	prompt := BuildStageResourcesPrompt(questionnaire, resourceInfos, stageDurations)
	t.Logf("\n生成的提示信息:\n%s", prompt)
}
