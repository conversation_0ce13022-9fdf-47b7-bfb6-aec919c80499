package util

import (
	"context"
	"encoding/json"
	"errors"
	"fmt" // 导入包含PlanStageAIResponse的包
	"loop/internal/data/types"
	"loop/internal/model"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime"
	arkruntimemodel "github.com/volcengine/volcengine-go-sdk/service/arkruntime/model"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
)

// FilterEmptyTagsResources 过滤掉标签为空的资源
func FilterEmptyTagsResources(resources []*model.Resource) []*model.Resource {
	var validResources []*model.Resource
	for _, resource := range resources {
		if resource.Tags != "" {
			var tags []string
			if err := json.Unmarshal([]byte(resource.Tags), &tags); err != nil {
				logrus.WithError(err).Error("解析资源标签失败，跳过该资源 id:", resource.Id)
				continue
			}

			if len(tags) > 0 {
				validResources = append(validResources, resource)
			}
		}
	}

	logrus.Infof("有效资源数量: %d (总资源数量: %d)", len(validResources), len(resources))
	return validResources
}

// PrepareResourceInfos 准备资源信息，包括ID和标签
func PrepareResourceInfos(resources []*model.Resource) []map[string]any {
	resourceInfos := make([]map[string]any, 0, len(resources))
	for _, resource := range resources {
		// 解析资源标签
		var tags []string
		if resource.Tags != "" {
			if err := json.Unmarshal([]byte(resource.Tags), &tags); err != nil {
				logrus.WithError(err).Error("解析资源标签失败")
				tags = []string{} // 如果解析失败，使用空数组
			}
		} else {
			tags = []string{} // 如果标签为空，使用空数组
		}

		// 添加资源信息，包括时长
		resourceInfos = append(resourceInfos, map[string]any{
			"id":       resource.Id,
			"tags":     tags,
			"duration": resource.Duration, // 添加资源时长（秒）
		})
	}
	return resourceInfos
}

// CallAIService 调用AI服务的通用方法
func CallAIService(ctx context.Context, prompt string, apiKey string, model string) (string, error) {
	// 如果API密钥为空，使用默认密钥
	if apiKey == "" {
		apiKey = "76067b87-9bfd-4028-8eb0-f48c2a65c23f" // 使用默认API密钥
	}

	client := arkruntime.NewClientWithApiKey(apiKey)
	// 构建聊天完成请求，设置请求的模型和消息内容
	req := arkruntimemodel.CreateChatCompletionRequest{
		// 将推理接入点 <Model>替换为 Model ID
		Model: model,
		Messages: []*arkruntimemodel.ChatCompletionMessage{
			{
				// 消息的角色为用户
				Role: arkruntimemodel.ChatMessageRoleUser,
				Content: &arkruntimemodel.ChatCompletionMessageContent{
					StringValue: volcengine.String(prompt),
				},
			},
		},
	}

	// 发送聊天完成请求，并将结果存储在 resp 中
	logrus.Info("开始调用AI服务...")
	resp, err := client.CreateChatCompletion(ctx, req)
	if err != nil {
		// 若出现错误，打印错误信息并终止程序
		logrus.WithError(err).Error("调用AI服务失败")
		return "", err
	}

	// 检查是否有响应内容
	if len(resp.Choices) == 0 || resp.Choices[0].Message.Content == nil || *resp.Choices[0].Message.Content.StringValue == "" {
		logrus.Error("AI响应内容为空")
		return "", errors.New("AI响应内容为空")
	}

	// 获取完整内容
	content := *resp.Choices[0].Message.Content.StringValue

	// 打印AI的完整响应，便于调试
	logrus.Info("AI响应内容:")
	fmt.Println(content)

	return content, nil
}

// BuildFilterResourcesPrompt 构建筛选资源的提示信息
func BuildFilterResourcesPrompt(questionnaire *model.UserQuestionnaire, resourceInfos []map[string]any) string {
	// 将资源信息转换为JSON字符串
	resourcesJSON, _ := json.Marshal(resourceInfos)
	fmt.Println(string(resourcesJSON))

	// 筛选资源的提示信息
	filterPrompt := fmt.Sprintf(`你是英语学习资源筛选助手。根据用户情况和资源信息，筛选出最适合用户的资源。

用户情况:
- 当前级别: %s
- 目标级别: %s
- 动力来源: %s
- 想提高的能力: %s

资源信息(包含ID、标签):
###
%s
###

重要说明：
1. 资源信息是一个json，tags代表着每个资源的标签
2. 所有提供的资源都是专门为英语学习设计的，没有任何资源是"与学习无关"的
3. 资源标签中可能包含内容主题词(如"企业宣传"、"古装"、"情感高潮"等)，这些只是描述内容主题，不代表资源与英语学习无关
4. 每个资源都有其学习价值，只是适用的场景和级别可能不同

请根据用户的当前级别、目标级别和学习需求，从提供的资源中筛选出最适合的资源。
筛选标准(按优先级排序):
1. 资源内容应与用户想提高的能力相关，即使标签中没有直接提到这些能力
2. 资源难度应与用户当前级别相匹配或在当前级别到目标级别的范围内
3. 标签中包含级别信息(如A0、A1、B1等)的资源优先考虑
4. 最终的资源数量最多5个，最少2个

直接输出JSON格式的筛选结果，格式如下:
{
  "resourceIds": ["资源ID1", "资源ID2", "资源ID3", "资源ID4", "资源ID5"]
}

只返回JSON格式的结果，不要包含任何解释或其他文本。`,
		questionnaire.CurrentLevel,
		questionnaire.TargetLevel,
		questionnaire.MotivationSource,
		questionnaire.DesiredAbility,
		string(resourcesJSON))

	return filterPrompt
}

// generateSubStages 根据当前级别和目标级别生成子阶段列表
func generateSubStages(currentLevel, targetLevel string) []string {
	// CEFR级别映射
	levelMap := map[string]int{
		"A0": 0, "A1": 1, "A2": 2, "B1": 3, "B2": 4, "C1": 5, "C2": 6,
	}

	currentIdx, currentExists := levelMap[currentLevel]
	targetIdx, targetExists := levelMap[targetLevel]

	if !currentExists || !targetExists || currentIdx >= targetIdx {
		// 如果级别不存在或无效，返回默认的A0→B1子阶段
		return []string{"A0-1", "A0-2", "A1-1", "A1-2", "A2-1", "A2-2"}
	}

	var subStages []string

	// 生成从当前级别到目标级别的所有子阶段
	for i := currentIdx; i < targetIdx; i++ {
		levelName := ""
		switch i {
		case 0:
			levelName = "A0"
		case 1:
			levelName = "A1"
		case 2:
			levelName = "A2"
		case 3:
			levelName = "B1"
		case 4:
			levelName = "B2"
		case 5:
			levelName = "C1"
		case 6:
			levelName = "C2"
		}

		if levelName == "C2" {
			// C2只有一个阶段
			subStages = append(subStages, "C2")
		} else {
			// 其他级别都有两个子阶段
			subStages = append(subStages, levelName+"-1", levelName+"-2")
		}
	}

	return subStages
}

// GenerateStageDurations 根据用户的当前级别和目标级别生成阶段时长映射
func GenerateStageDurations(currentLevel, targetLevel string) map[string]string {
	// CEFR级别映射
	levelMap := map[string]int{
		"A0": 0, "A1": 1, "A2": 2, "B1": 3, "B2": 4, "C1": 5, "C2": 6,
	}

	// 完整的CEFR时长表
	fullTable := []struct {
		level string
		hours string
	}{
		{"A0-1", "30-50h"},
		{"A0-2", "30-50h"},
		{"A1-1", "60-90h"},
		{"A1-2", "60-90h"},
		{"A2-1", "100-150h"},
		{"A2-2", "100-150h"},
		{"B1-1", "175-250h"},
		{"B1-2", "175-250h"},
		{"B2-1", "300-400h"},
		{"B2-2", "300-400h"},
		{"C1-1", "400-500h"},
		{"C1-2", "400-500h"},
		{"C2", "500+h"},
	}

	currentIdx, currentExists := levelMap[currentLevel]
	targetIdx, targetExists := levelMap[targetLevel]

	if !currentExists || !targetExists || currentIdx >= targetIdx {
		// 如果级别不存在或无效，返回默认的A0→B1范围
		return map[string]string{
			"A0-1": "30-50h",
			"A0-2": "30-50h",
			"A1-1": "60-90h",
			"A1-2": "60-90h",
			"A2-1": "100-150h",
			"A2-2": "100-150h",
		}
	}

	stageDurations := make(map[string]string)

	// 生成从当前级别到目标级别的所有子阶段时长
	for i := currentIdx; i < targetIdx; i++ {
		levelName := ""
		switch i {
		case 0:
			levelName = "A0"
		case 1:
			levelName = "A1"
		case 2:
			levelName = "A2"
		case 3:
			levelName = "B1"
		case 4:
			levelName = "B2"
		case 5:
			levelName = "C1"
		case 6:
			levelName = "C2"
		}

		if levelName == "C2" {
			// C2只有一个阶段
			stageDurations["C2"] = "500+h"
		} else {
			// 其他级别都有两个子阶段，从fullTable中查找对应的时长
			for _, entry := range fullTable {
				if entry.level == levelName+"-1" {
					stageDurations[levelName+"-1"] = entry.hours
				}
				if entry.level == levelName+"-2" {
					stageDurations[levelName+"-2"] = entry.hours
				}
			}
		}
	}

	return stageDurations
}

// ConvertDurationToSeconds 将时长字符串转换为秒数
func ConvertDurationToSeconds(duration string) (int, int) {
	var minHours, maxHours int
	if strings.Contains(duration, "+") {
		// 处理 "500+h" 这种格式
		fmt.Sscanf(duration, "%d+h", &minHours)
		maxHours = minHours + 100 // 给一个合理的上限
	} else {
		// 处理 "30-50h" 这种格式
		fmt.Sscanf(duration, "%d-%dh", &minHours, &maxHours)
	}
	// 转换为秒
	return minHours * 3600, maxHours * 3600
}

// BuildStageResourcesPrompt 构建生成阶段资源分配的提示信息
func BuildStageResourcesPrompt(questionnaire *model.UserQuestionnaire, resourceInfos []map[string]any, stageDurations map[string]string) string {
	// 将资源信息转换为带序号的JSON字符串
	type NumberedResource struct {
		Number   int      `json:"number"`
		Tags     []string `json:"tags"`
		Duration int      `json:"duration"`
	}

	numberedResources := make([]NumberedResource, len(resourceInfos))
	for i, info := range resourceInfos {
		tags, _ := info["tags"].([]string)
		duration, _ := info["duration"].(int)
		numberedResources[i] = NumberedResource{
			Number:   i + 1, // 从1开始的序号
			Tags:     tags,
			Duration: duration,
		}
	}

	resourcesJSON, _ := json.Marshal(numberedResources)

	// 生成动态的子阶段列表
	subStages := generateSubStages(questionnaire.CurrentLevel, questionnaire.TargetLevel)

	// 构建阶段时长信息字符串（转换为秒）
	var stageDurationInfo strings.Builder
	for stage, duration := range stageDurations {
		minSeconds, maxSeconds := ConvertDurationToSeconds(duration)
		stageDurationInfo.WriteString(fmt.Sprintf("- %s: %d-%d秒\n", stage, minSeconds, maxSeconds))
	}

	prompt := fmt.Sprintf(`# 英语学习资源排序专家

## 任务目标
为英语学习者设计从 %s 到 %s 的阶段性学习资源排序方案。需要根据用户的学习需求和可用资源，为每个学习阶段对资源进行优先级排序。

## 用户信息
- 当前级别：%s
- 目标级别：%s
- 学习需求：%s（重点关注用户想要提高的具体能力）

## 阶段信息
学习路径阶段：%v
各阶段学习时长要求（单位：秒）：
%s

## 可用资源信息（带序号）
%s

## 重要说明
1. 每个资源都有一个序号（number字段），代表其在原始列表中的位置
2. 所有资源都是专门为英语学习设计的，没有任何资源是"与学习无关"的
3. 资源标签中可能包含内容主题词(如"企业宣传"、"古装"、"情感高潮"等)，这些只是描述内容主题，不代表资源与英语学习无关
4. 每个资源都有其学习价值，只是适用的场景和级别可能不同

## 排序要求
1. 为每个阶段对所有资源进行优先级排序
2. 排序标准(按优先级排序):
   - 资源内容应与用户想提高的能力相关，即使标签中没有直接提到这些能力
   - 资源难度应与用户当前级别相匹配或在当前级别到目标级别的范围内
   - 标签中包含级别信息(如A0、A1、B1等)的资源优先考虑
3. 每个阶段都必须包含所有资源的排序，排序靠前的表示更适合该阶段

## 输出格式
{
  "stages": [
    {
      "stageDesc": "阶段名称(如%s)",
      "objective": "该阶段的具体学习目标",
      "resourceNumbers": [1, 2, 3, ...] // 按优先级排序的资源序号列表
    }
  ]
}

## 重要说明
1. 直接输出JSON格式，不要包含其他文本
2. resourceNumbers数组必须包含所有资源的序号，不能遗漏或重复
3. 序号顺序代表资源在该阶段的优先级，序号越靠前优先级越高`,
		questionnaire.CurrentLevel,
		questionnaire.TargetLevel,
		questionnaire.CurrentLevel,
		questionnaire.TargetLevel,
		questionnaire.DesiredAbility,
		subStages,
		stageDurationInfo.String(),
		string(resourcesJSON),
		subStages[0])

	return prompt
}

// BuildStagePlanPrompt 构建生成单个阶段详细计划的提示信息
func BuildStagePlanPrompt(questionnaire *model.UserQuestionnaire, stageDesc string, resourceInfos []map[string]any, stageDuration string) string {
	// 将资源信息转换为JSON字符串
	resourcesJSON, _ := json.Marshal(resourceInfos)

	// 计算预计周数
	estimatedWeeks := CalculateEstimatedWeeks(stageDuration, questionnaire.DailyStudyMinutes)

	// 计算每天学习秒数
	dailyStudySeconds := questionnaire.DailyStudyMinutes * 60
	// 计算每天学习时间的灵活范围（±15%）
	minDailySeconds := int(float64(dailyStudySeconds) * 0.85)
	maxDailySeconds := int(float64(dailyStudySeconds) * 1.15)

	prompt := fmt.Sprintf(`你是英语学习计划分配助手。请根据以下信息，为指定阶段生成周学习计划。

用户信息：
- 每天学习时间：%d秒（可浮动范围：%d-%d秒）
- 阶段名称：%s
- 学习周数：%d周

可用资源信息（包含ID、标签和时长，单位秒）：
%s

任务要求：
1. 资源分配策略：
   - 确保每周都使用所有可用资源，保持学习的全面性
   - 根据资源标签特点（如语法、词汇、听力等），设计合理的学习顺序
   - 每个资源在%d周内的总使用次数不超过100次
   - 避免连续使用同一个资源
   - 每天使用多个资源，总时间在%d-%d秒之间

2. 分配建议：
   - 先分析每个资源的特点（标签、时长、学习目标）
   - 根据资源特点确定每周的使用频率
   - 考虑学习效果，合理安排资源使用顺序
   - 确保每周的学习时间合理分布
   - 可以根据资源特点适当调整使用次数

3. 输出格式：
{
  "stageDesc": "%s",
  "weeks": [
    {
      "weekNumber": 1,
      "resources": [
        {"resourceId": "资源ID", "lsCount": 该周使用的次数}
      ]
    }
  ]
}

注意：
1. 直接输出JSON，不要包含其他文本
2. 重点关注资源的学习效果和合理分配
3. 确保每周都使用所有资源，保持学习的全面性
4. 根据资源特点灵活调整使用次数，但总次数不超过100次`,
		dailyStudySeconds,
		minDailySeconds,
		maxDailySeconds,
		stageDesc,
		estimatedWeeks,
		string(resourcesJSON),
		estimatedWeeks,
		minDailySeconds,
		maxDailySeconds,
		stageDesc)

	return prompt
}

// CalculateEstimatedWeeks 根据阶段时长和每天学习时间计算预计周数
func CalculateEstimatedWeeks(stageDuration string, dailyStudyMinutes int) int {
	var minHours, maxHours int
	if strings.Contains(stageDuration, "+") {
		// 处理 "500+h" 这种格式
		fmt.Sscanf(stageDuration, "%d+h", &minHours)
		maxHours = minHours + 100 // 给一个合理的上限
	} else {
		// 处理 "30-50h" 这种格式
		fmt.Sscanf(stageDuration, "%d-%dh", &minHours, &maxHours)
	}

	// 使用平均时长计算周数
	avgHours := (minHours + maxHours) / 2
	if avgHours > 0 && dailyStudyMinutes > 0 {
		// 计算需要的周数：总小时数 / (每天学习小时数 * 7天)
		dailyHours := float64(dailyStudyMinutes) / 60.0
		estimatedWeeks := int((float64(avgHours) / (dailyHours * 7)) + 0.5) // 四舍五入
		if estimatedWeeks < 1 {
			return 1 // 至少1周
		}
		return estimatedWeeks
	}
	return 8 // 默认8周
}

// CalculatePlanStagesTime 计算计划各阶段的时间
// 根据每个阶段的周数，计算开始和结束时间，确保阶段之间时间连续
func CalculatePlanStagesTime(stages []types.PlanStageAIResponse) []types.PlanStageAIResponse {
	if len(stages) == 0 {
		return stages
	}

	// 使用当前时间作为第一个阶段的开始时间
	baseStartDate := time.Now()
	stageStartDates := make(map[int]time.Time)

	// 遍历所有阶段，计算时间
	for i, stage := range stages {
		// 计算该阶段的开始时间
		stageStartDate := baseStartDate
		if i > 0 {
			// 如果不是第一个阶段，使用前一个阶段的结束时间作为开始时间
			if prevStage, exists := stageStartDates[i-1]; exists {
				// 获取前一个阶段的周数
				prevWeeksCount := 10 // 默认值
				if i > 0 && len(stages) > 0 {
					prevWeeksCount = len(stages[i-1].Weeks)
					if prevWeeksCount == 0 {
						prevWeeksCount = 10
					}
				}
				// 计算前一个阶段的结束时间（开始时间 + 周数 * 7天）
				prevEndDate := prevStage.AddDate(0, 0, prevWeeksCount*7)
				// 当前阶段的开始时间是前一个阶段结束时间后一天
				stageStartDate = prevEndDate.AddDate(0, 0, 1)
			}
		}
		stageStartDates[i] = stageStartDate

		// 计算该阶段的持续时间（周数）
		weeksCount := len(stage.Weeks)
		if weeksCount == 0 {
			// 如果没有周数据，使用默认值（2.5个月，约10周）
			weeksCount = 10
		}

		// 计算结束时间（开始时间 + 周数 * 7天）
		endDate := stageStartDate.AddDate(0, 0, weeksCount*7)

		// 更新阶段的时间
		stages[i].StartDate = stageStartDate.Format("2006-01-02 15:04:05")
		stages[i].EndDate = endDate.Format("2006-01-02 15:04:05")
	}

	return stages
}

// ExtractJSONFromContent 从内容中提取JSON字符串
// 处理可能包含markdown代码块的JSON内容
func ExtractJSONFromContent(content string) (string, error) {
	// 移除所有可能的markdown代码块标记
	content = strings.TrimSpace(content)

	// 移除所有 ```json 和 ``` 标记
	content = strings.ReplaceAll(content, "```json", "")
	content = strings.ReplaceAll(content, "```", "")

	// 移除可能的多余空行
	lines := strings.Split(content, "\n")
	var nonEmptyLines []string
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			nonEmptyLines = append(nonEmptyLines, line)
		}
	}
	content = strings.Join(nonEmptyLines, "\n")

	return strings.TrimSpace(content), nil
}
