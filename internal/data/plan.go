package data

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"loop/internal/client"
	"loop/internal/config"
	"loop/internal/data/types"
	"loop/internal/data/util"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/dbx"
	"loop/pkg/jwtx"
	"loop/pkg/timex"
	"loop/pkg/web"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/sirupsen/logrus"
	"go4.org/syncutil/singleflight"
)

func NewPlanRepo(
	model *model.PlanModel,
	resourceModel *model.ResourceModel,
	config *config.Config,
	client *client.Client,
) *PlanRepo {
	return &PlanRepo{
		model:         model,
		resourceModel: resourceModel,
		config:        config,
		client:        client,
	}
}

type PlanRepo struct {
	model         *model.PlanModel
	resourceModel *model.ResourceModel
	config        *config.Config
	client        *client.Client
}

// GetQuestionnaire 获取用户的问卷信息
func (r *PlanRepo) GetQuestionnaire(c *gin.Context) *web.JsonResult {
	uid := jwtx.GetUid(c)

	questionnaire, err := r.model.GetUserQuestionnaire(uid)
	if err != nil {
		logrus.WithError(err).Error("Failed to get user questionnaire")
		return web.JsonInternalError(err)
	}

	if questionnaire == nil {
		return web.JsonEntityNotFound(c)
	}

	resp := &response.UserQuestionnaireResp{}
	copier.Copy(resp, questionnaire)
	resp.CreatedAt = timex.FormatRFC3339(questionnaire.CreatedAt)
	resp.UpdatedAt = timex.FormatRFC3339(questionnaire.UpdatedAt)

	return web.JsonData(resp)
}

// GeneratePlan 为用户生成学习计划
// 添加 singleflight 组
var generatePlanGroup singleflight.Group

// 全局状态映射，用于跟踪计划生成状态
var planGenerationStatusMap = sync.Map{}

// 检查是否有正在进行的计划生成
func (r *PlanRepo) isGeneratingPlan(uid string) bool {
	_, ok := planGenerationStatusMap.Load(uid)
	return ok
}

// 标记计划生成开始
func (r *PlanRepo) markPlanGenerationStarted(uid string) {
	planGenerationStatusMap.Store(uid, true)
}

// 标记计划生成结束
func (r *PlanRepo) markPlanGenerationCompleted(uid string) {
	planGenerationStatusMap.Delete(uid)
}

func (r *PlanRepo) GeneratePlan(c *gin.Context, req request.GeneratePlanReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	// 检查用户是否已有活跃计划
	existingPlan, err := r.model.GetActiveLearningPlan(uid)
	if err != nil {
		logrus.WithError(err).Error("获取用户活跃计划失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	// 如果用户已有活跃计划且不强制重新生成，则返回错误
	if existingPlan != nil && !req.ForceRegenerate {
		return web.JsonParamErr("用户已有活跃计划，如需重新生成请设置forceRegenerate=true")
	}

	// 检查是否已经有正在进行的计划生成
	if r.isGeneratingPlan(uid) {
		// 如果已经有正在进行的计划生成，返回状态信息
		return web.JsonData(map[string]any{
			"message": "计划生成正在进行中，请稍后查询",
			"status":  "processing",
		})
	}

	// 如果存在活跃计划，立即将其状态设置为无效
	if existingPlan != nil {
		// 使用事务将现有计划标记为无效
		err = r.model.Tx(func(txDb *dbx.DBExtension) error {
			return txDb.Model(&model.LearningPlan{}).Where(model.LearningPlan{Uid: uid, Status: 1}).Update("status", 0).Error
		})

		if err != nil {
			logrus.WithError(err).Error("使现有计划失效失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		logrus.Infof("用户 %s 的现有计划已设置为无效", uid)
	}

	// 创建临时问卷对象用于AI服务调用和保存
	tempQuestionnaire := &model.UserQuestionnaire{
		Uid:               uid,
		CurrentLevel:      req.CurrentLevel,
		TargetLevel:       req.TargetLevel,
		DailyStudyMinutes: req.DailyStudyMinutes,
		MotivationSource:  req.MotivationSource,
		DesiredAbility:    req.DesiredAbility,
	}

	// 标记计划生成开始
	r.markPlanGenerationStarted(uid)

	// 获取目标语言代码，因为在goroutine中无法访问gin上下文
	// targetLangCode := jwtx.GetTargetLangCode(c)

	// 在后台goroutine中执行计划生成
	go func() {
		// 使用 singleflight 确保同一用户的并发请求只执行一次
		key := fmt.Sprintf("generate_plan_%s", uid)

		// 使用 singleflight 执行生成计划的逻辑
		result, err := generatePlanGroup.Do(key, func() (any, error) {
			logrus.Infof("开始为用户 %s 生成学习计划", uid)

			// 获取AI使用的资源
			// 首先获取与目标语言相关的资源关系
			var resourceRelations []*model.ResourceRelation
			if err := r.resourceModel.GetList(&resourceRelations, ""); err != nil {
				// if err := r.resourceModel.GetList(&resourceRelations, model.ResourceRelation{LangCode: targetLangCode}); err != nil {
				logrus.WithError(err).Error("获取资源关系失败 uid:", uid)
				return nil, err
			}

			// 提取资源ID
			var resourceIds []string
			for _, relation := range resourceRelations {
				resourceIds = append(resourceIds, relation.ResourceId)
			}

			// 如果没有找到资源关系，返回空列表
			if len(resourceIds) == 0 {
				return &types.PlanAIResponse{}, nil
			}

			// 获取资源
			resources, err := r.resourceModel.GetByIds(resourceIds)
			if err != nil {
				logrus.WithError(err).Error("获取资源失败 uid:", uid)
				return nil, err
			}

			// 创建一个新的上下文，因为原始的gin上下文可能已经关闭
			ctx := context.Background()

			// 过滤掉tags为空的资源
			validResources := util.FilterEmptyTagsResources(resources)

			// 先调用AI服务筛选资源
			logrus.Info("开始筛选资源...")
			// filteredResources, err := r.callFilterResourcesAIService(ctx, tempQuestionnaire, validResources)
			// if err != nil {
			// 	logrus.WithError(err).Error("筛选资源失败 uid:", uid)
			// 	return nil, err
			// }

			// 准备资源信息
			// resourceInfos := util.PrepareResourceInfos(filteredResources)
			resourceInfos := util.PrepareResourceInfos(validResources)

			// 调用分阶段生成计划的函数
			aiResponse, err := r.callGenerateStagedPlanAIService(ctx, tempQuestionnaire, resourceInfos)
			if err != nil {
				logrus.WithError(err).Error("生成分阶段计划失败 uid:", uid)
				return nil, err
			}

			return aiResponse, nil

		})

		// 无论成功还是失败，最后都要清理状态
		defer r.markPlanGenerationCompleted(uid)

		if err != nil {
			logrus.WithError(err).Error("生成计划失败 uid:", uid)
			return
		}

		// 类型断言，将 result 转换为 *types.PlanAIResponse
		aiResponse, ok := result.(*types.PlanAIResponse)
		if !ok {
			err := errors.New("类型断言失败，无法将结果转换为 PlanAIResponse")
			logrus.Error(err)
			return
		}
		// 打印AI响应内容
		jsonBytes, err := json.MarshalIndent(aiResponse, "", "  ")
		if err != nil {
			logrus.WithError(err).Error("序列化AI响应失败")
			return
		}
		logrus.Infof("AI生成的计划响应: %s", string(jsonBytes))
		if !ok {
			err := errors.New("类型断言失败，无法将结果转换为 PlanAIResponse")
			logrus.Error(err)
			return
		}

		// 计算结束时间
		var endDate time.Time

		// 如果有阶段，使用最后一个阶段的结束时间
		if len(aiResponse.Stages) > 0 {
			lastStage := aiResponse.Stages[len(aiResponse.Stages)-1]
			if lastStage.EndDate != "" {
				parsedEndDate, err := timex.ParseStandard(lastStage.EndDate)
				if err == nil {
					endDate = parsedEndDate
				}
			}
		}

		// 使用事务处理保存问卷和创建计划
		err = r.model.Tx(func(txDb *dbx.DBExtension) error {
			// 1. 获取用户问卷（如果存在）
			existingQuestionnaire, err := r.model.GetUserQuestionnaire(uid)
			if err != nil {
				return err
			}

			var questionnaireId string
			if existingQuestionnaire == nil {
				// 2. 创建新问卷
				newQuestionnaire := &model.UserQuestionnaire{
					Uid:               uid,
					MotivationSource:  tempQuestionnaire.MotivationSource,
					DesiredAbility:    tempQuestionnaire.DesiredAbility,
					CurrentLevel:      req.CurrentLevel,
					TargetLevel:       req.TargetLevel,
					DailyStudyMinutes: req.DailyStudyMinutes,
				}

				// 使用事务保存问卷
				if err := txDb.Create(newQuestionnaire).Error; err != nil {
					logrus.WithError(err).Error("保存用户问卷失败 uid:", uid)
					return err
				}
				questionnaireId = newQuestionnaire.Id
			} else {
				questionnaireId = existingQuestionnaire.Id
			}

			// 3.2 创建新计划
			now := timex.Now()
			plan := &model.LearningPlan{
				Uid:             uid,
				StartLevel:      req.CurrentLevel,
				TargetLevel:     req.TargetLevel,
				StartDate:       now,
				EndDate:         endDate,
				Status:          1,
				QuestionnaireId: questionnaireId,
			}

			if err := txDb.Create(plan).Error; err != nil {
				return err
			}

			// 3.3 创建计划阶段
			for i, stageResponse := range aiResponse.Stages {
				// 解析阶段开始和结束日期
				stageStartDate := now
				if stageResponse.StartDate != "" {
					parsedStartDate, err := timex.ParseStandard(stageResponse.StartDate)
					if err == nil {
						stageStartDate = parsedStartDate
					}
				}

				var stageEndDate time.Time
				if stageResponse.EndDate != "" {
					parsedEndDate, err := timex.ParseStandard(stageResponse.EndDate)
					if err == nil {
						stageEndDate = parsedEndDate
					}
				}

				// 收集阶段的资源ID和LS次数
				var resourceIds []string
				var lsCounts []int
				var weeksJSON string

				// 如果有周数据，从周数据中收集资源并保存周数据
				if len(stageResponse.Weeks) > 0 && len(stageResponse.Weeks[0].Resources) > 0 {
					// 兼容旧格式：如果没有周数据但有资源数据，直接使用资源数据
					for _, resourceResponse := range stageResponse.Weeks[0].Resources {
						resourceIds = append(resourceIds, resourceResponse.ResourceId)
						lsCounts = append(lsCounts, resourceResponse.LsCount)
					}

					// 创建一个默认的周数据，将所有资源放在第一周
					defaultWeek := types.PlanWeekAIResponse{
						WeekNumber: 1,
						Resources:  stageResponse.Weeks[0].Resources,
					}

					// 将默认周数据序列化为JSON字符串
					weeksJSONBytes, err := json.Marshal([]types.PlanWeekAIResponse{defaultWeek})
					if err != nil {
						logrus.WithError(err).Error("序列化默认周数据失败")
						return err
					}
					weeksJSON = string(weeksJSONBytes)
				} else if len(stageResponse.Weeks) > 0 {
					// 兼容旧格式：如果没有周数据但有资源数据，直接使用资源数据
					for _, resourceResponse := range stageResponse.Weeks[0].Resources {
						resourceIds = append(resourceIds, resourceResponse.ResourceId)
						lsCounts = append(lsCounts, resourceResponse.LsCount)
					}

					// 创建一个默认的周数据，将所有资源放在第一周
					defaultWeek := types.PlanWeekAIResponse{
						WeekNumber: 1,
						Resources:  stageResponse.Weeks[0].Resources,
					}

					// 将默认周数据序列化为JSON字符串
					weeksJSONBytes, err := json.Marshal([]types.PlanWeekAIResponse{defaultWeek})
					if err != nil {
						logrus.WithError(err).Error("序列化默认周数据失败")
						return err
					}
					weeksJSON = string(weeksJSONBytes)
				}

				// 将资源ID列表和LS次数列表转换为JSON字符串
				resourceIdsJSON, err := json.Marshal(resourceIds)
				if err != nil {
					logrus.WithError(err).Error("序列化资源ID列表失败")
					return err
				}

				lsCountsJSON, err := json.Marshal(lsCounts)
				if err != nil {
					logrus.WithError(err).Error("序列化LS次数列表失败")
					return err
				}

				stage := &model.LearningPlanStage{
					PlanId:       plan.Id,
					StageDesc:    stageResponse.StageDesc, // 使用AI返回的阶段描述
					Objective:    stageResponse.Objective,
					SortOrder:    i + 1,
					StartDate:    stageStartDate,
					EndDate:      stageEndDate,
					ResourceIds:  string(resourceIdsJSON), // 保存资源ID列表
					LsCountsJSON: string(lsCountsJSON),    // 保存LS次数列表
					WeeksJSON:    weeksJSON,               // 保存周数据
				}

				if err := txDb.Create(stage).Error; err != nil {
					return err
				}
			}

			return nil
		})

		if err != nil {
			logrus.WithError(err).Error("创建计划失败 uid:", uid)
			return
		}

		logrus.Infof("用户 %s 的学习计划生成完成", uid)
	}()

	// 立即返回，告知用户计划生成已开始
	message := "计划生成已开始，请稍后查询"
	if existingPlan != nil {
		message = "现有计划已失效，新计划生成已开始，请稍后查询"
	}

	return web.JsonData(map[string]any{
		"message": message,
		"status":  "processing",
	})
}

// GetPlan 获取用户当前活跃的学习计划
func (r *PlanRepo) GetPlan(c *gin.Context, req request.GetPlanReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	var plan *model.LearningPlan
	var err error

	// 1. 获取计划
	if req.PlanId != "" {
		// 如果提供了计划ID，则获取指定的计划
		plan = &model.LearningPlan{Model: model.Model{Id: req.PlanId}}
		found, err := r.model.GetOne(plan, *plan)
		if err != nil {
			logrus.WithError(err).Error("获取指定计划失败 uid:", uid)
			return web.JsonInternalError(err)
		}
		if !found {
			return web.JsonErrorCodeMsg(404, "未找到指定的计划")
		}
		// 验证计划是否属于当前用户
		if plan.Uid != uid {
			return web.JsonErrorCodeMsg(403, "无权访问该计划")
		}
	} else {
		// 否则获取用户当前活跃的计划
		plan, err = r.model.GetActiveLearningPlan(uid)
		if err != nil {
			logrus.WithError(err).Error("获取活跃计划失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		if plan == nil {
			return web.JsonEmptyData()
		}
	}

	// 2. 获取计划阶段
	stages, err := r.model.GetPlanStages(plan.Id)
	if err != nil {
		logrus.WithError(err).Error("获取计划阶段失败 uid:", uid)
		return web.JsonInternalError(err)
	}
	// 3. 构建响应
	resp := &response.LearningPlanResp{
		Id:          plan.Id,
		StartLevel:  plan.StartLevel,
		TargetLevel: plan.TargetLevel,
		StartDate:   timex.FormatStandard(plan.StartDate),
		EndDate:     timex.FormatStandard(plan.EndDate),
		Status:      plan.Status,
	}
	// 4. 添加阶段到响应
	for _, stage := range stages {
		stageResp := response.PlanStageResp{
			Id:        stage.Id,
			StageDesc: stage.StageDesc, // 添加阶段描述
			Objective: stage.Objective,
			StartDate: timex.FormatStandard(stage.StartDate),
			EndDate:   timex.FormatStandard(stage.EndDate),
		}

		// 5. 解析阶段的资源ID列表和LS次数列表
		var resourceIds []string
		var lsCounts []int

		if stage.ResourceIds != "" {
			if err := json.Unmarshal([]byte(stage.ResourceIds), &resourceIds); err != nil {
				logrus.WithError(err).Error("解析资源ID列表失败 uid:", uid)
				return web.JsonInternalError(err)
			}
		}

		if stage.LsCountsJSON != "" {
			if err := json.Unmarshal([]byte(stage.LsCountsJSON), &lsCounts); err != nil {
				logrus.WithError(err).Error("解析LS次数列表失败 uid:", uid)
				return web.JsonInternalError(err)
			}
		}

		// 创建资源ID到LS次数的映射
		resourceLsCountMap := make(map[string]int)
		for i, resourceId := range resourceIds {
			if i < len(lsCounts) {
				resourceLsCountMap[resourceId] = lsCounts[i]
			}
		}

		// 6. 一次性获取所有资源和资源关系
		resources, relations, err := r.resourceModel.GetResourcesAndRelations(resourceIds, jwtx.GetTargetLangCode(c))
		if err != nil {
			logrus.WithError(err).Error("批量获取资源和关系失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		// 创建映射（一次性创建所有需要的映射）
		resourceMap := make(map[string]*model.Resource)
		relationMap := make(map[string]*model.ResourceRelation)

		for _, resource := range resources {
			resourceMap[resource.Id] = resource
		}

		for _, relation := range relations {
			relationMap[relation.ResourceId] = relation
		}

		// 不再需要构建资源响应列表，直接使用周数据

		// 8. 获取阶段的周数据
		// 从数据库中获取存储的周数据
		if stage.WeeksJSON != "" {
			// 解析周数据
			var weeks []types.PlanWeekAIResponse
			if err := json.Unmarshal([]byte(stage.WeeksJSON), &weeks); err != nil {
				logrus.WithError(err).Error("解析周数据失败 uid:", uid)
				return web.JsonInternalError(err)
			}

			// 构建周响应
			stageResp.Weeks = make([]response.PlanWeekResp, 0, len(weeks))

			for _, week := range weeks {
				weekResp := response.PlanWeekResp{
					WeekNumber: week.WeekNumber,
					Resources:  make([]response.PlanWeekResourceResp, 0, len(week.Resources)),
				}

				for _, resource := range week.Resources {
					weekResourceResp := response.PlanWeekResourceResp{
						ResourceId: resource.ResourceId,
						LsCount:    resource.LsCount,
					}

					// 从映射中获取资源信息
					if resource, ok := resourceMap[resource.ResourceId]; ok {
						weekResourceResp.ResourceCover = resource.Cover
					}

					if relation, ok := relationMap[resource.ResourceId]; ok {
						weekResourceResp.ResourceName = relation.Title
					}

					weekResp.Resources = append(weekResp.Resources, weekResourceResp)
				}

				// 只添加有资源的周
				if len(weekResp.Resources) > 0 {
					stageResp.Weeks = append(stageResp.Weeks, weekResp)
				}
			}
		} else if len(resourceIds) > 0 {
			// 兼容旧数据：如果没有存储周数据但有资源ID，则创建一个默认的周数据
			logrus.Info("未找到周数据，使用资源ID创建默认周数据")

			// 创建一个默认的周，将所有资源放在第一周
			weekResp := response.PlanWeekResp{
				WeekNumber: 1,
				Resources:  make([]response.PlanWeekResourceResp, 0, len(resourceIds)),
			}

			for i, resourceId := range resourceIds {
				lsCount := 0
				if i < len(lsCounts) {
					lsCount = lsCounts[i]
				}

				weekResourceResp := response.PlanWeekResourceResp{
					ResourceId: resourceId,
					LsCount:    lsCount,
				}

				// 从映射中获取资源信息
				if resource, ok := resourceMap[resourceId]; ok {
					weekResourceResp.ResourceCover = resource.Cover
				}

				if relation, ok := relationMap[resourceId]; ok {
					weekResourceResp.ResourceName = relation.Title
				}

				weekResp.Resources = append(weekResp.Resources, weekResourceResp)
			}

			stageResp.Weeks = []response.PlanWeekResp{weekResp}
		}

		resp.Stages = append(resp.Stages, stageResp)
	}

	return web.JsonData(resp)
}

// InvalidatePlan 使用户的学习计划失效
func (r *PlanRepo) InvalidatePlan(c *gin.Context, req request.InvalidatePlanReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	if req.PlanId != "" {
		// 如果提供了计划ID，则只使该计划失效
		plan := &model.LearningPlan{Model: model.Model{Id: req.PlanId}}
		found, err := r.model.GetOne(plan, *plan)
		if err != nil {
			logrus.WithError(err).Error("获取指定计划失败")
			return web.JsonInternalError(err)
		}
		if !found {
			return web.JsonErrorCodeMsg(404, "未找到指定的计划")
		}
		// 验证计划是否属于当前用户
		if plan.Uid != uid {
			return web.JsonErrorCodeMsg(403, "无权操作该计划")
		}
		// 使计划失效
		plan.Status = 0
		if err := r.model.Update(plan, "id = ?", plan.Id); err != nil {
			logrus.WithError(err).Error("使计划失效失败 uid:", uid)
			return web.JsonInternalError(err)
		}
	} else {
		// 否则使用户所有活跃计划失效
		if err := r.model.InvalidateUserPlans(uid); err != nil {
			logrus.WithError(err).Error("使用户计划失效失败 uid:", uid)
			return web.JsonInternalError(err)
		}
	}

	return web.JsonOK()
}

// getCEFRLevel 获取CEFR级别的数值，用于排序
func getCEFRLevel(stageDesc string) int {
	// 定义CEFR级别的映射
	levelMap := map[string]int{
		"A0-1": 1, "A0-2": 2,
		"A1-1": 3, "A1-2": 4,
		"A2-1": 5, "A2-2": 6,
		"B1-1": 7, "B1-2": 8,
		"B2-1": 9, "B2-2": 10,
		"C1-1": 11, "C1-2": 12,
		"C2": 13,
	}

	// 查找匹配的级别
	for level, value := range levelMap {
		if strings.Contains(stageDesc, level) {
			return value
		}
	}

	// 如果没有匹配的级别，返回0
	return 0
}

// 计算每周的学习时长（秒）
func calculateWeeklyStudyTime(dailyStudyMinutes int) int {
	return dailyStudyMinutes * 60 * 7 // 每天学习分钟数 * 60秒 * 7天
}

// generateStageResources 生成阶段资源排序
func (r *PlanRepo) generateStageResources(ctx context.Context, questionnaire *model.UserQuestionnaire, resourceInfos []map[string]any) (*types.StageResourceListResponse, error) {
	// 生成阶段时长映射
	stageDurations := util.GenerateStageDurations(questionnaire.CurrentLevel, questionnaire.TargetLevel)

	// 构建阶段资源排序的提示信息
	prompt := util.BuildStageResourcesPrompt(questionnaire, resourceInfos, stageDurations)
	logrus.Infof("阶段资源排序 AI Prompt: %s", prompt)

	// 从环境变量或配置中获取API密钥
	apiKey := os.Getenv("ARK_API_KEY")

	// 调用AI服务
	content, err := util.CallAIService(ctx, prompt, apiKey, "deepseek-v3-250324")
	if err != nil {
		logrus.WithError(err).Error("调用AI服务生成阶段资源排序失败")
		return nil, err
	}

	// 预处理内容，提取JSON
	jsonContent, err := util.ExtractJSONFromContent(content)
	if err != nil {
		logrus.WithError(err).Error("提取JSON内容失败")
		return nil, err
	}

	// 解析AI返回的JSON字符串为StageResourcesSortResponse结构
	var sortResponse types.StageResourcesSortResponse
	if err := json.Unmarshal([]byte(jsonContent), &sortResponse); err != nil {
		logrus.WithError(err).Error("解析AI生成的阶段资源排序失败")
		return nil, err
	}

	// 验证响应数据的有效性
	if len(sortResponse.Stages) == 0 {
		logrus.Error("AI生成的阶段资源排序为空")
		return nil, errors.New("AI生成的阶段资源排序为空")
	}

	// 创建资源ID到序号的映射
	resourceIdToNumber := make(map[string]int)
	for i, info := range resourceInfos {
		if id, ok := info["id"].(string); ok {
			resourceIdToNumber[id] = i + 1 // 从1开始的序号
		}
	}

	// 创建序号到资源ID的映射
	numberToResourceId := make(map[int]string)
	for id, number := range resourceIdToNumber {
		numberToResourceId[number] = id
	}

	// 转换为StageResourceListResponse格式
	response := &types.StageResourceListResponse{
		Stages: make([]types.StageResourceList, len(sortResponse.Stages)),
	}

	for i, stage := range sortResponse.Stages {
		// 创建阶段响应
		stageResponse := types.StageResourceList{
			BaseStageResponse: stage.BaseStageResponse,
			ResourceIds:       make([]string, 0, len(stage.ResourceNumbers)),
		}

		// 将资源序号转换为资源ID
		for _, number := range stage.ResourceNumbers {
			if resourceId, ok := numberToResourceId[number]; ok {
				stageResponse.ResourceIds = append(stageResponse.ResourceIds, resourceId)
			}
		}

		response.Stages[i] = stageResponse
	}

	return response, nil
}

// callGenerateStagedPlanAIService 调用AI服务分阶段生成计划
func (r *PlanRepo) callGenerateStagedPlanAIService(ctx context.Context, questionnaire *model.UserQuestionnaire, resourceInfos []map[string]any) (*types.PlanAIResponse, error) {
	// 1. 首先调用AI服务生成阶段资源列表
	logrus.Info("开始生成阶段资源列表...")
	stageResources, err := r.generateStageResources(ctx, questionnaire, resourceInfos)
	if err != nil {
		logrus.WithError(err).Error("生成阶段资源列表失败")
		return nil, err
	}

	// 2. 创建资源使用状态映射
	resourceUsageMap := make(map[string]*types.ResourceUsage)
	for _, info := range resourceInfos {
		if id, ok := info["id"].(string); ok {
			if duration, ok := info["duration"].(int); ok {
				resourceUsageMap[id] = &types.ResourceUsage{
					ResourceId: id,
					Duration:   duration,
				}
			}
		}
	}

	// 3. 计算每周学习时长
	weeklyStudyTime := calculateWeeklyStudyTime(questionnaire.DailyStudyMinutes)
	logrus.Infof("每周学习时长: %d秒", weeklyStudyTime)

	// 生成阶段时长映射
	stageDurations := util.GenerateStageDurations(questionnaire.CurrentLevel, questionnaire.TargetLevel)

	// 4. 生成阶段计划
	finalResponse := &types.PlanAIResponse{
		Stages: make([]types.PlanStageAIResponse, len(stageResources.Stages)),
	}

	// 5. 处理每个阶段
	for i, stage := range stageResources.Stages {
		// 创建阶段响应
		stageResponse := types.PlanStageAIResponse{
			BaseStageResponse: stage.BaseStageResponse,
			Weeks:             make([]types.PlanWeekAIResponse, 0),
		}

		// 获取当前阶段的时长（秒）
		stageDurationStr, ok := stageDurations[stage.StageDesc]
		if !ok {
			logrus.Warnf("未找到阶段 %s 的时长信息，使用默认值", stage.StageDesc)
			stageDurationStr = "60-90h" // 使用默认值
		}

		// 将时长字符串转换为秒数
		minSeconds, maxSeconds := util.ConvertDurationToSeconds(stageDurationStr)
		// 使用平均时长作为阶段时长
		stageDuration := (minSeconds + maxSeconds) / 2
		logrus.Infof("阶段 %s 目标时长: %d-%d秒 (平均: %d秒)", stage.StageDesc, minSeconds, maxSeconds, stageDuration)

		// 计算阶段需要的总周数（向上取整）
		totalWeeks := (stageDuration + weeklyStudyTime - 1) / weeklyStudyTime
		if totalWeeks < 1 {
			totalWeeks = 1 // 确保至少有一周
		}
		logrus.Infof("阶段 %s 需要 %d 周完成", stage.StageDesc, totalWeeks)

		// 计算每周应该完成的学习时长
		weeklyTargetDuration := stageDuration / totalWeeks
		if weeklyTargetDuration < 1 {
			weeklyTargetDuration = 1 // 确保每周至少有一个单位的学习时长
		}

		// 初始化所有周
		for weekNum := 1; weekNum <= totalWeeks; weekNum++ {
			stageResponse.Weeks = append(stageResponse.Weeks, types.PlanWeekAIResponse{
				WeekNumber: weekNum,
				Resources:  make([]types.PlanResourceAIResponse, 0),
			})
		}

		// 计算每个资源应该分配到的周数
		resourceCount := len(stage.ResourceIds)
		resourcesPerWeek := (resourceCount + totalWeeks - 1) / totalWeeks // 向上取整

		// 当前处理的周索引和资源计数
		currentWeekIndex := 0
		currentWeekResourceCount := 0
		currentWeekDuration := 0

		// 处理每个资源
		for _, resourceId := range stage.ResourceIds {
			// 获取资源信息
			resourceUsage, ok := resourceUsageMap[resourceId]
			if !ok {
				continue
			}

			// 如果当前周的资源数量达到目标，或者当前周时长接近目标，移动到下一周
			if (currentWeekResourceCount >= resourcesPerWeek ||
				currentWeekDuration >= weeklyTargetDuration) &&
				currentWeekIndex < totalWeeks-1 {
				currentWeekIndex++
				currentWeekResourceCount = 0
				currentWeekDuration = 0
			}

			// 计算在当前周可以学习的次数
			remainingWeekTime := weeklyStudyTime - currentWeekDuration
			lsCount := 1 // 默认至少学习一次

			if resourceUsage.Duration > 0 {
				// 计算在当前周剩余时间内可以学习的次数
				possibleCount := remainingWeekTime / resourceUsage.Duration
				if possibleCount > 0 {
					lsCount = possibleCount
				}
			}

			// 如果当前周剩余时间不足，且还有下一周，则减少学习次数
			if currentWeekDuration+(resourceUsage.Duration*lsCount) > weeklyStudyTime && currentWeekIndex < totalWeeks-1 {
				lsCount = (weeklyStudyTime - currentWeekDuration) / resourceUsage.Duration
				if lsCount < 1 {
					lsCount = 1
				}
			}

			// 添加资源到当前周
			stageResponse.Weeks[currentWeekIndex].Resources = append(
				stageResponse.Weeks[currentWeekIndex].Resources,
				types.PlanResourceAIResponse{
					ResourceId: resourceId,
					LsCount:    lsCount,
				},
			)

			// 更新当前周已用时间和资源计数
			currentWeekDuration += resourceUsage.Duration * lsCount
			currentWeekResourceCount++
		}

		// 移除空的周
		validWeeks := make([]types.PlanWeekAIResponse, 0, len(stageResponse.Weeks))
		for _, week := range stageResponse.Weeks {
			if len(week.Resources) > 0 {
				validWeeks = append(validWeeks, week)
			}
		}
		stageResponse.Weeks = validWeeks

		// 如果没有分配任何资源到周，添加一个空的第一周
		if len(stageResponse.Weeks) == 0 {
			stageResponse.Weeks = append(stageResponse.Weeks, types.PlanWeekAIResponse{
				WeekNumber: 1,
				Resources:  make([]types.PlanResourceAIResponse, 0),
			})
		}

		finalResponse.Stages[i] = stageResponse
	}

	// 6. 计算每个阶段的开始和结束时间
	finalResponse.Stages = util.CalculatePlanStagesTime(finalResponse.Stages)

	return finalResponse, nil
}

// callFilterResourcesAIService 调用AI服务筛选资源
func (r *PlanRepo) callFilterResourcesAIService(ctx context.Context, questionnaire *model.UserQuestionnaire, resources []*model.Resource) ([]*model.Resource, error) {
	// 获取配置的每批最大资源数量
	maxResourcesPerBatch := r.config.AI.MaxResourcesPerBatch
	if maxResourcesPerBatch <= 0 {
		// 如果配置无效，使用默认值50
		maxResourcesPerBatch = 50
	}

	totalResources := len(resources)
	logrus.Infof("开始筛选资源，总资源数量: %d，每批处理数量: %d", totalResources, maxResourcesPerBatch)

	// 如果资源数量小于等于每批最大数量，直接处理
	if totalResources <= maxResourcesPerBatch {
		return r.filterResourcesBatch(ctx, questionnaire, resources)
	}

	// 计算需要处理的批次数
	batchCount := (totalResources + maxResourcesPerBatch - 1) / maxResourcesPerBatch
	logrus.Infof("将分成 %d 个批次并发处理", batchCount)

	// 创建通道用于接收每个批次的处理结果
	type batchResult struct {
		resources []*model.Resource
		err       error
		batchNum  int
	}
	resultChan := make(chan batchResult, batchCount)

	// 创建一个WaitGroup来等待所有goroutine完成
	var wg sync.WaitGroup

	// 并发处理每个批次
	for i := 0; i < batchCount; i++ {
		wg.Add(1)
		go func(batchIndex int) {
			defer wg.Done()

			// 计算当前批次的起始和结束索引
			start := batchIndex * maxResourcesPerBatch
			end := min((batchIndex+1)*maxResourcesPerBatch, totalResources)

			// 获取当前批次的资源
			batchResources := resources[start:end]
			logrus.Infof("开始处理第 %d/%d 批资源，数量: %d", batchIndex+1, batchCount, len(batchResources))

			// 处理当前批次
			filteredResources, err := r.filterResourcesBatch(ctx, questionnaire, batchResources)

			// 将结果发送到通道
			resultChan <- batchResult{
				resources: filteredResources,
				err:       err,
				batchNum:  batchIndex + 1,
			}

			if err != nil {
				logrus.WithError(err).Errorf("处理第 %d 批资源失败", batchIndex+1)
			} else {
				logrus.Infof("第 %d 批资源处理完成，筛选出 %d 个资源", batchIndex+1, len(filteredResources))
			}
		}(i)
	}

	// 启动一个goroutine来关闭结果通道
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集所有批次的结果
	var allFilteredResources []*model.Resource
	var allResourceIds []string
	successCount := 0

	// 使用互斥锁保护共享资源
	var mu sync.Mutex

	for result := range resultChan {
		if result.err != nil {
			// 如果处理失败，记录错误但继续处理其他批次的结果
			continue
		}

		mu.Lock()
		successCount++
		// 收集当前批次筛选出的资源
		for _, resource := range result.resources {
			// 避免重复添加
			if !contains(allResourceIds, resource.Id) {
				allResourceIds = append(allResourceIds, resource.Id)
				allFilteredResources = append(allFilteredResources, resource)
			}
		}
		mu.Unlock()
	}

	// 如果所有批次都处理失败，返回错误
	if successCount == 0 {
		logrus.Error("所有批次处理均失败")
		return nil, errors.New("所有批次处理均失败")
	}

	// 如果所有批次处理后没有筛选出任何资源，返回错误
	if len(allFilteredResources) == 0 {
		logrus.Error("所有批次处理后，AI筛选的资源为空")
		return nil, errors.New("AI筛选的资源为空")
	}

	// 记录成功的响应
	logrus.Infof("所有批次处理完成，成功处理 %d/%d 批次，筛选出 %d 个资源",
		successCount, batchCount, len(allFilteredResources))

	return allFilteredResources, nil
}

// filterResourcesBatch 处理单批资源
func (r *PlanRepo) filterResourcesBatch(ctx context.Context, questionnaire *model.UserQuestionnaire, resources []*model.Resource) ([]*model.Resource, error) {
	// 准备资源信息，包括ID和标签
	resourceInfos := util.PrepareResourceInfos(resources)

	// 构建筛选资源的提示信息
	prompt := util.BuildFilterResourcesPrompt(questionnaire, resourceInfos)
	logrus.Infof("筛选资源批次 AI Prompt: %s", prompt)

	// 从环境变量或配置中获取API密钥
	apiKey := os.Getenv("ARK_API_KEY")

	// 调用AI服务
	content, err := util.CallAIService(ctx, prompt, apiKey, "deepseek-v3-250324")
	if err != nil {
		logrus.WithError(err).Error("调用AI服务筛选资源失败")
		return nil, err
	}

	// 解析AI返回的JSON字符串为资源ID列表
	type FilteredResourcesResponse struct {
		ResourceIds []string `json:"resourceIds"`
	}

	var filteredResourcesResponse FilteredResourcesResponse
	if err := json.Unmarshal([]byte(content), &filteredResourcesResponse); err != nil {
		logrus.WithError(err).Error("解析AI筛选的资源响应失败")
		return nil, err
	}

	// 验证响应数据的有效性
	if len(filteredResourcesResponse.ResourceIds) == 0 {
		logrus.Error("AI筛选的资源ID为空")
		return nil, errors.New("AI筛选的资源ID为空")
	}

	// 根据筛选后的资源ID获取资源
	var filteredResources []*model.Resource
	resourceMap := make(map[string]*model.Resource)

	// 创建资源ID到资源的映射
	for _, resource := range resources {
		resourceMap[resource.Id] = resource
	}

	// 根据筛选后的资源ID获取资源
	for _, resourceId := range filteredResourcesResponse.ResourceIds {
		if resource, ok := resourceMap[resourceId]; ok {
			filteredResources = append(filteredResources, resource)
		}
	}

	// 记录成功的响应
	logrus.Infof("批次筛选后的资源数量: %d", len(filteredResources))

	return filteredResources, nil
}

// contains 检查字符串切片是否包含指定字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
