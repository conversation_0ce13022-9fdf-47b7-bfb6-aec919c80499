package data

import (
	"loop/internal/client"
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/enum"
	"loop/pkg/i18n"
	"loop/pkg/jwtx"
	"loop/pkg/lang"
	"loop/pkg/oss"
	"loop/pkg/util"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/sirupsen/logrus"
	"github.com/thoas/go-funk"
)

func NewVideoRepo(
	model *model.VideoModel,
	dataCenterModel *model.DataCenterModel,
	lsResourceModel *model.ResourceModel,
	localNotesModel *model.NoteModel,
	collectModel *model.CollectModel,
	userModel *model.UserModel,
	config *config.Config,
	client *client.Client,
) *VideoRepo {
	return &VideoRepo{
		model:           model,
		userModel:       userModel,
		dataCenterModel: dataCenterModel,
		resourceModel:   lsResourceModel,
		notesModel:      localNotesModel,
		collectModel:    collectModel,
		config:          config,
		client:          client,
	}
}

type VideoRepo struct {
	model           *model.VideoModel
	userModel       *model.UserModel
	dataCenterModel *model.DataCenterModel
	notesModel      *model.NoteModel
	resourceModel   *model.ResourceModel
	collectModel    *model.CollectModel
	config          *config.Config
	client          *client.Client
}

func (s *VideoRepo) UpdateVideoDetail(c *gin.Context, req request.VideoDetailUpdateReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	if req.ResourceType == int(enum.LocalResource) {
		userCustomResource, err := s.model.GetUserLocalResourceById(req.ResourceId)
		if err != nil {
			return web.JsonInternalError(err)
		}
		if userCustomResource == nil {
			return web.JsonParamErr(i18n.T(c, i18n.ErrVideoLocalResourceNotFound))
		}
		userCustomResource.Position = req.Position
		if err := s.model.Update(userCustomResource, " id = ?", userCustomResource.Id); err != nil {
			return web.JsonInternalError(err)
		}
	} else {
		resourceUser := model.UserRemoteResourceRelations{}
		found, err := s.model.GetOne(&resourceUser, model.UserRemoteResourceRelations{Uid: uid, ResourceId: req.ResourceId})
		if err != nil {
			return web.JsonInternalError(err)
		}
		if !found {
			return web.JsonParamErr(i18n.T(c, i18n.ErrEntityNotFound))
		}
		resourceUser.Position = req.Position
		if err := s.model.Update(resourceUser, " id = ?", resourceUser.Id); err != nil {
			return web.JsonInternalError(err)
		}
	}
	return web.JsonOK()
}

func (s *VideoRepo) UpdateCutsomSubtitle(c *gin.Context, req request.SubtitleUpdateReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	var userLocalResource model.UserLocalResource
	if req.ResourceType == int(enum.LocalResource) {
		userLocalResource, err := s.model.GetUserLocalResourceById(req.ResourceId)
		if err != nil {
			return web.JsonInternalError(err)
		}
		if userLocalResource == nil {
			return web.JsonParamErr(i18n.T(c, i18n.ErrVideoLocalResourceNotFound))
		}
	}

	userCustomSubtitle, err := s.model.GetOrCreateUserSubtitleRelations(uid, req.ResourceId, req.ResourceType, req.SubtitleUrl)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if userCustomSubtitle == nil {
		return web.JsonParamErr(i18n.T(c, i18n.ErrVideoUserCustomSubtitleCreateFailed))
	}
	if req.ForceSkips {
		userCustomSubtitle.Skips = req.Skips
	}
	userCustomSubtitle.SubtitleUrl = req.SubtitleUrl
	if err := s.model.Update(&userCustomSubtitle, "id = ?", userCustomSubtitle.Id); err != nil {
		return web.JsonInternalError(err)
	}
	if req.ResourceType == int(enum.LocalResource) {
		return s.returnLocalVideoDetailResp(c, uid, &userLocalResource)
	}
	return s.GetVideoDetail(c, request.VideoDetailReq{ResourceId: req.ResourceId, ResourceType: req.ResourceType})
}

func (s *VideoRepo) GetVideoDetail(c *gin.Context, req request.VideoDetailReq) *web.JsonResult {
	if req.ResourceType == int(enum.LocalResource) {
		return s.getVideoLocalDetail(c, req)
	}
	return s.getVideoRemoteDetail(c, req)
}
func (s *VideoRepo) getVideoLocalDetail(c *gin.Context, req request.VideoDetailReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	// 获取平台类型
	platform := jwtx.GetPlatform(c)
	//根据文件名判断是否存在
	userCustomResource, err := s.model.GetUserLocalResourceByFileName(uid, req.FileName)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if userCustomResource != nil {
		// 如果是 iOS 平台，移除包含 "var/mobile/Containers" 的路径
		if platform == util.PlatformIOS {
			if len(userCustomResource.LocalVideoPaths) != 1 || userCustomResource.LocalVideoPaths[0] != req.LocalVideoPath {
				var filteredPaths []string
				for _, path := range userCustomResource.LocalVideoPaths {
					if !funk.Contains(path, "var/mobile/Containers") {
						filteredPaths = append(filteredPaths, path)
					}
				}
				userCustomResource.LocalVideoPaths = filteredPaths
			}
		}

		if !funk.Contains(userCustomResource.LocalVideoPaths, req.LocalVideoPath) {
			var localVideoPaths = append(userCustomResource.LocalVideoPaths, req.LocalVideoPath)
			userCustomResource.LocalVideoPaths = localVideoPaths
			if err := s.model.Update(userCustomResource, " id = ?", userCustomResource.Id); err != nil {
				return web.JsonInternalError(err)
			}
		}
		err = s.userModel.AddOrUpdateWatchHistory(uid, request.WatchHistoryAddReq{
			ResourceId:   userCustomResource.Id,
			ResourceType: int(enum.LocalResource),
			Position:     userCustomResource.Position,
		})
		if err != nil {
			return web.JsonInternalError(err)
		}
		return s.returnLocalVideoDetailResp(c, uid, userCustomResource)
	}
	logrus.Info("User ", uid, "  has not created a userCustomResource yet. Prepare to create one")
	// 创建一条本地资源记录  第一次不会有subtitleurl  需要app后续进行上传
	var saver model.UserLocalResource
	copier.Copy(&saver, &req)
	saver.Uid = uid
	saver.FileName = req.FileName
	var localVideoPaths []string
	localVideoPaths = append(localVideoPaths, req.LocalVideoPath)
	saver.LocalVideoPaths = localVideoPaths

	if err := s.model.Save(&saver).Error; err != nil {
		return web.JsonInternalError(err)
	}
	dataEpisode, err := s.dataCenterModel.GetDataEpisodeAndCreate(uid, saver.Id, int(enum.LocalResource))
	if err != nil {
		return web.JsonInternalError(err)
	}
	resp := response.VideoDetailResp{
		ResourceId:     saver.Id,
		Position:       0,
		CurrentLsTimes: dataEpisode.CurrentLsTimes,
		ResourceType:   int(enum.LocalResource),
	}
	err = s.userModel.AddOrUpdateWatchHistory(uid, request.WatchHistoryAddReq{
		ResourceId:   saver.Id,
		ResourceType: int(enum.LocalResource),
		Position:     0,
	})
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(&resp)
}

// TODO 有一个bug 如果我更换了字幕  用户关联的那个表还是原来的字幕信息 判断是否使用两个字幕就有问题
func (s *VideoRepo) getVideoRemoteDetail(c *gin.Context, req request.VideoDetailReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	userRemoteResourceRelations, resource, err := s.model.GetOrCreateRemoteResourceRelations(uid, req)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if userRemoteResourceRelations == nil || resource == nil {
		return web.JsonEntityNotFound(c)
	}
	result, err := s.getVideoDetailResp(c, uid, userRemoteResourceRelations.ResourceId, int(enum.RemoteResouce), userRemoteResourceRelations.Position, resource.VideoURL)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if result != nil {
		return web.JsonData(result)
	}
	return web.JsonOK()
}

func (s *VideoRepo) returnLocalVideoDetailResp(c *gin.Context, uid string, userCustomResource *model.UserLocalResource) *web.JsonResult {
	result, err := s.getVideoDetailResp(c, uid, userCustomResource.Id, int(enum.LocalResource), userCustomResource.Position, "")
	if err != nil {
		return web.JsonInternalError(err)
	}
	if result != nil {
		return web.JsonData(result)
	}
	return web.JsonOK()
}

func (s *VideoRepo) getVideoDetailResp(c *gin.Context, uid string, resourceId string, resourceType int, position int64, playUrl string) (*response.VideoDetailResp, error) {
	userSubtitleRelations, err := s.model.GetUserSubtitleRelations(uid, resourceId, resourceType)
	if err != nil {
		return nil, err
	}
	userLocalNotesList, err := s.notesModel.GetNoteList(uid, resourceId, resourceType)
	if err != nil {
		return nil, err
	}
	var noteResp []response.NotesResp
	for _, note := range userLocalNotesList {
		var resp response.NotesResp
		copier.Copy(&resp, &note)
		noteResp = append(noteResp, resp)
	}
	dataEpisode, err := s.dataCenterModel.GetDataEpisodeAndCreate(uid, resourceId, resourceType)
	if err != nil {
		return nil, err
	}
	nativeLangSubtitleUrl := ""
	originSubtitleUrl := ""
	// UserSubtitleRelations 中的 SubtitleUrl 对应上面的SubtitleUrl
	// OriginSubtitleUrl 代表视频本身的字幕 一定会有
	// 因为 UserSubtitleRelations中的 SubtitleUrl默认是 OriginSubtitleUrl
	// 所以需要判断 SubtitleUrl如果和 OriginSubtitleUrl 地址一样 说明用户没有改过字幕 useDoubleSubtitleUrl 为true
	if resourceType == int(enum.RemoteResouce) {
		originResourceRelation, nativeLangResourceRelation, err := s.resourceModel.GetOriginAndNativeLangResourceRelation(resourceId, lang.GetPreferredLanguage(c).Code())
		if err != nil {
			return nil, err
		}
		if originResourceRelation != nil {
			originSubtitleUrl = originResourceRelation.SubtitleUrl
		}
		if nativeLangResourceRelation != nil {
			nativeLangSubtitleUrl = nativeLangResourceRelation.SubtitleUrl
		}
	}
	logrus.Info("getVideoDetailResp Uid=", uid, " resourceId=", resourceId, " resourceType=", resourceType, " userSubtitleRelations isNull=", userSubtitleRelations == nil)
	if userSubtitleRelations != nil {
		return &response.VideoDetailResp{
			ResourceId:            resourceId,
			SubtitleUrl:           userSubtitleRelations.SubtitleUrl,
			NativeLangSubtitleUrl: nativeLangSubtitleUrl,
			OriginSubtitleUrl:     originSubtitleUrl,
			Skips:                 userSubtitleRelations.Skips,
			Notes:                 noteResp,
			Position:              position,
			SentenceCollects:      userSubtitleRelations.Collects,
			CurrentLsTimes:        dataEpisode.CurrentLsTimes,
			ResourceType:          resourceType,
			PlayUrl:               oss.GetOssSignedURL(s.client, playUrl),
		}, nil
	}
	return &response.VideoDetailResp{
		ResourceId:     resourceId,
		Position:       position,
		CurrentLsTimes: dataEpisode.CurrentLsTimes,
		ResourceType:   resourceType,
		PlayUrl:        oss.GetOssSignedURL(s.client, playUrl),
	}, nil
}
func (s *VideoRepo) ChangeResourceName(c *gin.Context, req request.ResourceNameModReq) *web.JsonResult {
	if req.ResourceType == int(enum.LocalResource) {
		userCustomResource, err := s.model.GetUserLocalResourceById(req.ResourceId)
		if err != nil {
			return web.JsonInternalError(err)
		}
		if userCustomResource == nil {
			return web.JsonEntityNotFound(c)
		}
		userCustomResource.FileName = req.Name
		if err := s.model.Update(userCustomResource, " id = ?", userCustomResource.Id); err != nil {
			return web.JsonInternalError(err)
		}
		return web.JsonOK()
	} else {
		return web.JsonOK()
	}
}
