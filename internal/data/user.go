package data

import (
	"context"
	"loop/internal/client"
	"loop/internal/config"
	"loop/internal/constants"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/apple"
	"loop/pkg/enum"
	"loop/pkg/i18n"
	"loop/pkg/jwtx"
	"loop/pkg/lang"
	"loop/pkg/web"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/thoas/go-funk"
	"golang.org/x/crypto/bcrypt"
)

func NewUserRepo(
	model *model.UserModel,
	lsResourceModel *model.ResourceModel,
	videoModel *model.VideoModel,
	config *config.Config,
	benefitModel *model.BenefitModel,
	client *client.Client,
) *UserRepo {
	return &UserRepo{
		model:         model,
		resourceModel: lsResourceModel,
		videoModel:    videoModel,
		config:        config,
		benefitModel:  benefitModel,
		client:        client,
	}
}

type UserRepo struct {
	model         *model.UserModel
	resourceModel *model.ResourceModel
	videoModel    *model.VideoModel
	config        *config.Config
	benefitModel  *model.BenefitModel
	client        *client.Client
}

// Login 用户登录函数
func (s *UserRepo) Login(c *gin.Context, req request.UserLoginReq) *web.JsonResult {

	user, err := s.model.FindByUsername(req.Username)
	if err != nil {
		return web.JsonErrorCodeMsg(http.StatusUnauthorized, err.Error())
	}
	if user == nil {
		return web.JsonEntityNotFound(c)
	}

	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordDigest), []byte(req.Password))
	if err != nil {
		return web.JsonErrorCodeMsg(http.StatusUnauthorized, err.Error())
	}
	token, err := jwtx.GenerateToken(user.Id, req.Username, user.Status, s.config.Jwt.SignKey, int64(s.config.Jwt.ExpireSeconds))
	if err != nil {
		return web.JsonErrorCodeMsg(http.StatusUnauthorized, err.Error())
	}
	userResp := response.LoginUserInfoResp{
		User: &response.UserInfoResp{},
	}
	userResp.Token = token
	s.client.RedisClient.Set(context.Background(), constants.UserTokenPrefix+user.Id, token, time.Duration(s.config.Jwt.ExpireSeconds)*time.Second)
	copier.Copy(&userResp.User, &user)
	return web.JsonData(userResp)
}
func (s *UserRepo) Register(c *gin.Context, req request.UserRegisterReq) *web.JsonResult {
	user := model.User{
		Nickname: req.Nickname,
		Username: req.Username,
	}

	query := model.User{Nickname: req.Nickname}
	result := model.User{}

	if found, _ := s.model.GetOne(&result, query); found {
		return web.JsonParamErr(i18n.T(c, i18n.ErrNickNameUsed))
	}
	query = model.User{Username: req.Username}
	if found, _ := s.model.GetOne(&result, query); found {
		return web.JsonParamErr(i18n.T(c, i18n.ErrUserNameUsed))
	}

	bytes, err := bcrypt.GenerateFromPassword([]byte(req.Password), constants.PassWordCost)
	if err != nil {
		return web.JsonInternalError(err)
	}
	user.PasswordDigest = string(bytes)

	if err := s.model.SaveOne(&user); err != nil {
		return web.JsonInternalError(err)
	}
	userResp := response.UserInfoResp{}
	copier.Copy(&userResp, &user)
	return web.JsonData(userResp)
}

func (s *UserRepo) LoginByApple(c *gin.Context, req request.AppleLoginReq) *web.JsonResult {
	userIdentifier, err := apple.VerifyApple(req.IdentityToken)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if userIdentifier != req.UserIdentifier {
		return web.JsonParamErr("userIdentifier not the same one")
	}

	appleSignUser, err := s.model.FindByAppleSign(req.UserIdentifier)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if appleSignUser == nil {
		userCount := int64(0)
		s.model.Count(&userCount, model.User{})
		name := "苹果用户" + strconv.FormatInt(userCount+1, 10)
		user := model.User{
			Nickname:            name,
			Username:            name,
			AppleUserIdentifier: req.UserIdentifier,
		}

		u, err := s.model.RegisterUser(user, lang.GetPreferredLanguage(c).Code())
		if err != nil {
			return web.JsonInternalError(err)
		}
		appleSignUser = u
	}
	userResp := response.LoginUserInfoResp{
		User: &response.UserInfoResp{},
	}
	token, err := jwtx.GenerateToken(appleSignUser.Id, appleSignUser.Username, appleSignUser.Status, s.config.Jwt.SignKey, int64(s.config.Jwt.ExpireSeconds))
	if err != nil {
		return web.JsonErrorCodeMsg(http.StatusUnauthorized, err.Error())
	}
	userResp.Token = token
	s.client.RedisClient.Set(context.Background(), constants.UserTokenPrefix+appleSignUser.Id, token, time.Duration(s.config.Jwt.ExpireSeconds)*time.Second)
	copier.Copy(&userResp.User, &appleSignUser)
	return web.JsonData(userResp)
}

func (s *UserRepo) GetInfo(uid string) *web.JsonResult {
	user := model.User{}
	if found, err := s.model.GetOne(&user, "id =?", uid); !found {
		if err != nil {
			return web.JsonInternalError(err)
		}
	}

	userResp := response.UserInfoResp{}
	copier.Copy(&userResp, &user)
	return web.JsonData(userResp)
}
func (s *UserRepo) CurrentUser(c *gin.Context) (*response.UserInfoResp, error) {
	uid := jwtx.GetUid(c)
	user := model.User{}
	if found, err := s.model.GetOne(&user, "id =?", uid); !found {
		if err != nil {
			return nil, err
		}
	}
	userResp := response.UserInfoResp{}
	copier.Copy(&userResp, &user)
	return &userResp, nil
}

func (s *UserRepo) SetInfo(c *gin.Context, req request.UpdateUserInfoReq) *web.JsonResult {

	uid := jwtx.GetUid(c)
	udateAttrs := model.User{
		Nickname: req.Nickname,
		Username: req.Username,
		Status:   req.Status,
	}
	if err := s.model.Update(&udateAttrs, "id = ?", uid); err != nil {
		return web.JsonInternalError(err)
	}
	return s.GetInfo(uid)
}

func (s *UserRepo) GetWatchHistory(c *gin.Context, req request.ListReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	result := []*model.WatchHistory{}
	resps := []*response.WatchHistoryResp{}
	err := s.model.GetPageRangeList(&result, "updated_at DESC", req.CurrentPage, req.PageSize, "uid = ?", uid)
	if err != nil {
		return web.JsonInternalError(err)
	}
	copier.Copy(&resps, &result)
	//根据查询的列表 查出不同资源的ids
	customResourceIds := funk.FlatMap(result, func(cr *model.WatchHistory) []string {
		if cr.ResourceType == int(enum.LocalResource) {
			return []string{cr.ResourceId}
		}
		return []string{}
	}).([]string)
	remoteResourceIds := funk.FlatMap(result, func(cr *model.WatchHistory) []string {
		if cr.ResourceType == int(enum.RemoteResouce) {
			return []string{cr.ResourceId}
		}
		return []string{}
	}).([]string)
	userCustomResources, err := s.videoModel.GetUserLocalResourceByIds(customResourceIds)
	if err != nil {
		return web.JsonInternalError(err)
	}
	resources, err := s.resourceModel.GetByIds(remoteResourceIds)
	if err != nil {
		return web.JsonInternalError(err)
	}
	for i, resp := range resps {
		if resp.ResourceType == int(enum.LocalResource) {
			found := funk.Find(userCustomResources, func(item *model.UserLocalResource) bool {
				return item.Id == resp.ResourceId
			})
			if found != nil {
				userCustomResource := found.(*model.UserLocalResource)
				resps[i].Resource = &response.WatchHistoryResourceResp{
					Name:            userCustomResource.FileName,
					Position:        userCustomResource.Position,
					LocalVideoPaths: userCustomResource.LocalVideoPaths,
				}
			}

		} else {
			found := funk.Find(resources, func(item *model.Resource) bool {
				return item.Id == resp.ResourceId
			})
			if found != nil {
				resource := found.(*model.Resource)
				resourceResp := response.WatchHistoryResourceResp{}

				copier.Copy(&resourceResp, &resource)
				//在查询到的资源标题根据语言信息找一个默认的语言
				foundTitle, err := s.resourceModel.GetDefalutResourceRelationByLang(resource.Id, lang.GetPreferredLanguage(c).Code())
				if err != nil {
					return web.JsonInternalError(err)
				}
				if foundTitle != nil {
					resourceResp.Name = foundTitle.Title
				}

				resps[i].Resource = &resourceResp
			}

		}
		resps[i].LastTime = result[i].Model.UpdatedAt.String()
	}

	return web.JsonData(resps)
}
func (s *UserRepo) DeleteWatchHistory(c *gin.Context, req request.WatchHistoryDeleteReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	err := s.model.DeleteWatchHistory(uid, req.ResourceId, req.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}

// DeleteAccount 删除用户账户
func (r *UserRepo) DeleteAccount(c *gin.Context) *web.JsonResult {
	uid := jwtx.GetUid(c)
	user, err := r.model.GetUserByUId(c, uid)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if user == nil {
		return web.JsonEntityNotFound(c)
	}

	// 执行注销操作
	err = r.model.DeleteAccount(uid)
	if err != nil {
		return web.JsonInternalError(err)
	}

	// 删除Redis中的token
	r.client.RedisClient.Del(context.Background(), constants.UserTokenPrefix+uid)

	return web.JsonOK()
}

// GetUserBenefits 获取用户权益列表
func (r *UserRepo) GetUserBenefits(c *gin.Context) *web.JsonResult {
	uid := jwtx.GetUid(c)
	benefits, err := r.benefitModel.GetUserBenefitsByUid(uid)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(benefits)
}

// GetBenefitsByGroup 获取用户指定组的权益列表
func (r *UserRepo) GetBenefitsByGroup(c *gin.Context, req request.GetUserBenefitsByGroupReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	benefits, err := r.benefitModel.GetUserBenefitsByGroup(uid, req.BenefitGroupCode)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(benefits)
}

// UpdateUserVideoLimit 更新用户视频存储空间使用量
func (r *UserRepo) UpdateUserVideoLimit(c *gin.Context, req request.UpdateUserVideoLimitReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	err := r.benefitModel.ConsumeBenefit(uid, string(constants.BenefitGroupVideoSizeLimit), req.Size)
	if err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonOK()
}

// UpdateUserAICallLimit 更新AI调用次数限制
func (r *UserRepo) UpdateUserAICallLimit(c *gin.Context, req request.UpdateUserAICallLimitReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	// 消耗AI调用次数权益
	err := r.benefitModel.ConsumeBenefit(uid, string(constants.BenefitGroupAiCallsLimit), req.Count)
	if err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonOK()
}
