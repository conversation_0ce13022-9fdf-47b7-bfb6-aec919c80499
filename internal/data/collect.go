package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/pkg/i18n"
	"loop/pkg/jwtx"
	"loop/pkg/types"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
)

func NewCollectRepo(
	model *model.CollectModel,
	videoModel *model.VideoModel,
	config *config.Config,
) *CollectRepo {
	return &CollectRepo{
		model:      model,
		videoModel: videoModel,
		config:     config,
	}
}

type CollectRepo struct {
	model      *model.CollectModel
	videoModel *model.VideoModel
	config     *config.Config
}

func (s *CollectRepo) AddNoteCollect(c *gin.Context, req request.IdReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	saver := model.NoteCollectRelations{
		Uid:         uid,
		LocalNoteId: req.Id,
	}
	if req.Id == "" {
		return web.JsonOK()
	}
	result, err := s.model.GetUserLocalNoteCollect(uid, req.Id)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if result != nil {
		return web.JsonOK()
	} else {
		if err := s.model.Save(&saver).Error; err != nil {
			return web.JsonInternalError(err)
		}
	}
	return web.JsonOK()
}
func (s *CollectRepo) DeleteLocalNoteCollect(c *gin.Context, req request.IdReq) *web.JsonResult {
	return DeleteById[model.NoteCollectRelations](s.model.DbModel, req.Id)
}

// -1代表异常情况 0代表没有收藏 1代表已收藏
func (s *CollectRepo) GetLocalNoteCollectStatus(uid string, userLocalNoteId string) (int, error) {
	result, err := s.model.GetUserLocalNoteCollect(uid, userLocalNoteId)
	if err != nil {
		return -1, nil
	}
	if result != nil {
		return 1, nil
	}
	return 0, nil
}

func (s *CollectRepo) AddSentenceCollect(c *gin.Context, req request.SentenceCollectAddReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	userLocalSubtitle, err := s.videoModel.GetUserSubtitleRelations(uid, req.ResourceId, req.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if userLocalSubtitle == nil {
		return web.JsonParamErr(i18n.T(c, i18n.ErrEntityNotFound))
	}

	for _, newInterval := range req.Times {
		exists := false
		for _, existingInterval := range userLocalSubtitle.Collects {
			if existingInterval.Start == newInterval.Start && existingInterval.End == newInterval.End {
				exists = true
				break
			}
		}
		if !exists {
			userLocalSubtitle.Collects = append(userLocalSubtitle.Collects, newInterval)
		}
	}

	if err := s.model.Save(&userLocalSubtitle).Error; err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}

func (s *CollectRepo) RemoveSentenceCollect(c *gin.Context, req request.SentenceCollectAddReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	userLocalSubtitle, err := s.videoModel.GetUserSubtitleRelations(uid, req.ResourceId, req.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if userLocalSubtitle == nil {
		return web.JsonParamErr(i18n.T(c, i18n.ErrEntityNotFound))
	}

	// 遍历要删除的时间区间
	for _, removeInterval := range req.Times {
		// 创建一个新切片来保存未被删除的时间区间
		updatedCollects := types.VideoTimeIntervalArray{}

		// 遍历现有的 Collects，检查是否与要删除的区间重叠
		for _, existingInterval := range userLocalSubtitle.Collects {
			if !(existingInterval.Start == removeInterval.Start && existingInterval.End == removeInterval.End) {
				// 不重叠，保留该时间区间
				updatedCollects = append(updatedCollects, existingInterval)
			}
		}

		// 更新 Collects
		userLocalSubtitle.Collects = updatedCollects
	}

	// 保存更新后的 userLocalSubtitle
	if err := s.model.Save(&userLocalSubtitle).Error; err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}
