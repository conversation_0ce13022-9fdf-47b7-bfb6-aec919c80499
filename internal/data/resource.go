package data

import (
	"loop/internal/client"
	"loop/internal/config"
	"loop/internal/constants"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/enum"
	"loop/pkg/jwtx"
	"loop/pkg/oss"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

func NewResourceRepo(
	model *model.ResourceModel,
	seriesModel *model.SeriesModel,
	config *config.Config,
	client *client.Client,
) *ResourceRepo {
	return &ResourceRepo{
		model:       model,
		seriesModel: seriesModel,
		config:      config,
		client:      client,
	}
}

type ResourceRepo struct {
	model       *model.ResourceModel
	seriesModel *model.SeriesModel
	config      *config.Config
	client      *client.Client
}

func (s *ResourceRepo) GetResourceHome(c *gin.Context, req request.EmptyReq) *web.JsonResult {
	targetLang := jwtx.GetTargetLangCode(c)
	serieses, resources, err := s.model.GetFeatureContent(targetLang)
	if err != nil {
		return web.JsonInternalError(err)
	}

	// 构建精选内容
	featureContent := []response.ResourceHomeFeatureContentResp{}
	for _, series := range serieses {
		featureContent = append(featureContent, response.ResourceHomeFeatureContentResp{
			Id:          series.Id,
			Cover:       series.Cover,
			ContentType: int(enum.Series),
			Name:        series.Title,
		})
	}
	for _, resource := range resources {
		featureContent = append(featureContent, response.ResourceHomeFeatureContentResp{
			Id:          resource.Id,
			Cover:       resource.Cover,
			ContentType: int(enum.Resource),
			VideoURL:    oss.GetOssSignedURL(s.client, resource.VideoURL),
		})
	}

	// 获取分类类型及其下属分类
	categoryTypeList := s.buildCategoryTypeList()

	resourceHomeResp := response.ResourceHomeResp{
		FeatureContent:   featureContent,
		CategoryTypeList: categoryTypeList,
	}

	return web.JsonData(resourceHomeResp)
}

// buildCategoryTypeList 构建分类类型列表
func (s *ResourceRepo) buildCategoryTypeList() []response.ResourceHomeCategoryTypeResp {
	// 获取所有分类类型（使用常量）
	categoryTypes := constants.GetAllCategoryTypes()

	categoryTypeList := []response.ResourceHomeCategoryTypeResp{}

	for _, categoryType := range categoryTypes {
		// 获取该类型下的所有分类
		var categories []*model.Category
		err := s.model.Where("category_type_id = ?", categoryType.ID).
			Order("priority ASC").
			Find(&categories).Error
		if err != nil {
			continue
		}

		// 转换为响应格式
		categoryResps := []response.CategoryResp{}
		for _, category := range categories {
			categoryResp := response.CategoryResp{}
			copier.Copy(&categoryResp, category)
			categoryResps = append(categoryResps, categoryResp)
		}

		// 只有当下属分类不为空时才添加到列表中
		if len(categoryResps) > 0 {
			categoryTypeList = append(categoryTypeList, response.ResourceHomeCategoryTypeResp{
				Name:         categoryType.Name,
				CategoryResp: categoryResps,
			})
		}
	}

	return categoryTypeList
}

func (s *ResourceRepo) GetResourceHomeByCategory(c *gin.Context, req request.ResourceHomeResourceReq) *web.JsonResult {
	targetLangCode := jwtx.GetTargetLangCode(c)
	serieses, resources, err := s.model.GetResouceSeriesByCategory(targetLangCode, req.CategoryIds)
	if err != nil {
		return web.JsonInternalError(err)
	}
	items := []response.ResourceHomeItemResp{}
	for _, series := range serieses {
		items = append(items, response.ResourceHomeItemResp{
			Id:          series.Id,
			Cover:       series.Cover,
			ContentType: int(enum.Series),
			Name:        series.Title,
		})
	}
	for _, resource := range resources {
		tags, err := s.model.GetResourceTags(resource.Id)
		if err != nil {
			tags = []string{}
		}
		items = append(items, response.ResourceHomeItemResp{
			Id:          resource.Id,
			Cover:       resource.Cover,
			ContentType: resource.ContentType,
			Name:        resource.Title,
			VideoURL:    oss.GetOssSignedURL(s.client, resource.VideoURL),
			Tags:        tags,
			Duration:    resource.Duration,
			Author:      resource.Author,
		})
	}
	return web.JsonData(items)
}

// GetResourceTags 获取资源的标签
func (s *ResourceRepo) GetResourceTags(c *gin.Context, req request.ResourceTagsReq) *web.JsonResult {
	tags, err := s.model.GetResourceTags(req.ResourceId)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(tags)
}

// SetResourceTags 设置资源的标签
func (s *ResourceRepo) SetResourceTags(c *gin.Context, req request.SetResourceTagsReq) *web.JsonResult {
	if err := s.model.SetResourceTags(req.ResourceId, req.Tags); err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}

// AddResourceTag 添加资源标签
func (s *ResourceRepo) AddResourceTag(c *gin.Context, req request.ResourceTagReq) *web.JsonResult {
	if err := s.model.AddResourceTag(req.ResourceId, req.Tag); err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}

// RemoveResourceTag 移除资源标签
func (s *ResourceRepo) RemoveResourceTag(c *gin.Context, req request.ResourceTagReq) *web.JsonResult {
	if err := s.model.RemoveResourceTag(req.ResourceId, req.Tag); err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}

// GetResourcesByTags 根据标签获取资源
func (s *ResourceRepo) GetResourcesByTags(c *gin.Context, req request.ResourcesByTagsReq) *web.JsonResult {
	targetLangCode := jwtx.GetTargetLangCode(c)
	if req.LangCode != "" {
		targetLangCode = req.LangCode
	}

	resources, err := s.model.GetResourcesByTags(req.Tags, targetLangCode)
	if err != nil {
		return web.JsonInternalError(err)
	}

	items := []response.ResourceHomeItemResp{}
	for _, resource := range resources {
		resourceRelations, err := s.model.GetOriginResourceRelation(resource.Id)
		if err != nil {
			return web.JsonInternalError(err)
		}

		// 获取资源标签
		tags, err := s.model.GetResourceTags(resource.Id)
		if err != nil {
			// 标签获取失败不影响整体结果，只记录日志
			tags = []string{}
		}

		items = append(items, response.ResourceHomeItemResp{
			Id:          resource.Id,
			Cover:       resource.Cover,
			ContentType: int(enum.Resource),
			Name:        resourceRelations.Title,
			VideoURL:    oss.GetOssSignedURL(s.client, resource.VideoURL),
			Tags:        tags,
		})
	}

	return web.JsonData(items)
}

// GetAllResources 获取所有资源
func (s *ResourceRepo) GetAllResources(c *gin.Context, req request.GetAllResourcesReq) *web.JsonResult {
	// 从请求头获取语言代码
	langCode := jwtx.GetTargetLangCode(c)

	// 如果请求参数中提供了langCode，则覆盖请求头中的值
	if req.LangCode != "" {
		langCode = req.LangCode
	}

	// 设置默认分页参数
	page := req.CurrentPage
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20 // 默认每页20条
	}

	// 调用模型层获取资源
	resources, total, err := s.model.GetAllResources(langCode, page, pageSize)
	if err != nil {
		return web.JsonInternalError(err)
	}

	// 构建响应
	items := []response.ResourceHomeItemResp{}
	for _, resource := range resources {
		// 获取资源关系（标题等信息）
		resourceRelations, err := s.model.GetOriginResourceRelation(resource.Id)
		if err != nil {
			return web.JsonInternalError(err)
		}

		// 获取资源标签
		tags, err := s.model.GetResourceTags(resource.Id)
		if err != nil {
			// 标签获取失败不影响整体结果，只记录日志
			tags = []string{}
		}

		// 构建资源项
		items = append(items, response.ResourceHomeItemResp{
			Id:          resource.Id,
			Cover:       resource.Cover,
			ContentType: int(enum.Resource),
			Name:        resourceRelations.Title,
			VideoURL:    oss.GetOssSignedURL(s.client, resource.VideoURL),
			Tags:        tags,
			Duration:    resource.Duration,
			Author:      resource.Author,
		})
	}

	// 构建分页响应
	result := response.PagedResourcesResp{
		Items:      items,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: (total + int64(pageSize) - 1) / int64(pageSize),
	}

	return web.JsonData(result)
}
