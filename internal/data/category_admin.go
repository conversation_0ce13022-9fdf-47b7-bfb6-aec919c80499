package data

import (
	"loop/internal/config"
	"loop/internal/constants"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/enum"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

func NewCategoryAdminRepo(
	model *model.CategoryAdminModel,
	resRepo *ResourceRepo,
	config *config.Config,
) *CategoryAdminRepo {
	return &CategoryAdminRepo{
		model:   model,
		resRepo: resRepo,
		config:  config,
	}
}

type CategoryAdminRepo struct {
	model   *model.CategoryAdminModel
	resRepo *ResourceRepo
	config  *config.Config
}

func (s *CategoryAdminRepo) SaveOne(c *gin.Context, req request.ResCategoryReq) *web.JsonResult {
	// 检查分类类型是否存在（使用常量）
	categoryType := constants.GetCategoryTypeByID(req.CategoryTypeId)
	if categoryType == nil {
		return web.JsonParamErr("分类类型不存在")
	}

	// 检查同类型下是否有重名分类
	query := model.Category{Name: req.Name, CategoryTypeId: req.CategoryTypeId}
	result := model.Category{}

	if found, _ := s.model.GetOne(&result, query); found {
		return web.JsonEntityNotFound(c)
	}
	return SaveOne[model.Category](s.model.DbModel, c, req)
}

func (s *CategoryAdminRepo) GetCategoryList(c *gin.Context, req request.ListReq) *web.JsonResult {
	var categories []*model.Category
	count, err := s.model.GetOrderPage(&categories, enum.SortType(req.SortType).DescString(), req.CurrentPage, req.PageSize)
	if err != nil {
		return web.JsonInternalError(err)
	}
	resps := []response.CategoryResp{}
	copier.Copy(&resps, &categories)
	return web.JsonData(web.PageJsonResult{
		Data:  resps,
		Total: count,
	})
}
func (s *CategoryAdminRepo) SetPriority(c *gin.Context, req request.PriorityReq) *web.JsonResult {
	var result model.Category
	found, err := s.model.GetOne(&result, model.Category{Model: model.Model{Id: req.Id}})
	if err != nil || !found {
		return web.JsonInternalError(err)
	}
	result.Priority = req.Priority
	if err := s.model.Update(&result, &model.Category{Model: model.Model{Id: req.Id}}); err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonOK()
}
