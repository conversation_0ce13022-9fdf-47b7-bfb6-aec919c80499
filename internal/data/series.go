package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/lang"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/thoas/go-funk"
)

func NewSeriesRepo(
	model *model.SeriesModel,
	resourceModel *model.ResourceModel,
	config *config.Config,
) *SeriesRepo {
	return &SeriesRepo{
		model:         model,
		resourceModel: resourceModel,
		config:        config,
	}
}

type SeriesRepo struct {
	model         *model.SeriesModel
	resourceModel *model.ResourceModel
	config        *config.Config
}

func (s *SeriesRepo) GetSeriesDetail(c *gin.Context, req request.IdReq) *web.JsonResult {
	var seriesResources []*model.SeriesResourceRelations
	err := s.model.GetList(&seriesResources, model.SeriesResourceRelations{SeriesId: req.Id})
	if err != nil {
		return web.JsonInternalError(err)
	}
	var series model.Series
	found, err := s.model.GetOne(&series, "id = ?", req.Id)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if !found {
		return web.JsonEmptyData()
	}
	resourceIds := funk.Map(seriesResources, func(cr *model.SeriesResourceRelations) string {
		return cr.ResourceId
	}).([]string)
	var resources []*model.Resource
	err = s.model.GetList(&resources, "id IN ?", resourceIds)
	if err != nil {
		return web.JsonInternalError(err)
	}
	var resps []response.ResourceResp
	copier.Copy(&resps, resources)
	for i := range resps {
		rr, err := s.resourceModel.GetDefalutResourceRelationByLang(resps[i].Id, lang.GetPreferredLanguage(c).Code())
		if err != nil {
			return web.JsonInternalError(err)
		}
		resps[i].DefaultLangTitle = rr.Title
	}

	// 获取SeriesRelation数据，根据用户语言偏好
	var seriesRelation model.SeriesRelation
	userLangCode := lang.GetPreferredLanguage(c).Code()
	found, err = s.model.GetOne(&seriesRelation, "series_id = ? AND lang_code = ?", req.Id, userLangCode)
	if err != nil {
		return web.JsonInternalError(err)
	}

	seriesDetailResp := response.SeriesDetailResp{}
	copier.Copy(&seriesDetailResp, series)

	// 设置多语言信息
	if found {
		seriesDetailResp.Title = seriesRelation.Title
		seriesDetailResp.Description = seriesRelation.Description
	}

	seriesDetailResp.Resources = resps
	return web.JsonData(seriesDetailResp)
}
