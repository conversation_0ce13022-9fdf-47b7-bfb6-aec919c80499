package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
)

func NewCategoryTypeAdminRepo(
	model *model.CategoryTypeModel,
	config *config.Config,
) *CategoryTypeAdminRepo {
	return &CategoryTypeAdminRepo{
		model:  model,
		config: config,
	}
}

type CategoryTypeAdminRepo struct {
	model  *model.CategoryTypeModel
	config *config.Config
}

func (s *CategoryTypeAdminRepo) SaveOne(c *gin.Context, req request.ResCategoryTypeReq) *web.JsonResult {
	// 检查名称是否重复
	exists, err := s.model.CheckNameExists(req.Name, "")
	if err != nil {
		return web.JsonInternalError(err)
	}
	if exists {
		return web.JsonParamErr("分类类型名称已存在")
	}

	return SaveOne[model.CategoryType](s.model.DbModel, c, req)
}

func (s *CategoryTypeAdminRepo) UpdateOne(c *gin.Context, req request.ResCategoryTypeReq) *web.JsonResult {
	if req.Id == "" {
		return web.JsonParamErr("ID不能为空")
	}

	// 检查名称是否重复（排除当前记录）
	exists, err := s.model.CheckNameExists(req.Name, req.Id)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if exists {
		return web.JsonParamErr("分类类型名称已存在")
	}

	return UpdateOne[model.CategoryType](s.model.DbModel, req)
}

func (s *CategoryTypeAdminRepo) GetList(c *gin.Context, req request.ListReq) *web.JsonResult {
	return GetList[model.CategoryType](s.model.DbModel, req.PageSize, req.CurrentPage)
}

func (s *CategoryTypeAdminRepo) GetAll(c *gin.Context) *web.JsonResult {
	categoryTypes, err := s.model.GetAll()
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(categoryTypes)
}

func (s *CategoryTypeAdminRepo) Delete(c *gin.Context, req request.IdReq) *web.JsonResult {
	if req.Id == "" {
		return web.JsonParamErr("ID不能为空")
	}

	// 检查是否有关联的分类
	var count int64
	err := s.model.Model(&model.Category{}).Where("category_type_id = ?", req.Id).Count(&count).Error
	if err != nil {
		return web.JsonInternalError(err)
	}
	if count > 0 {
		return web.JsonParamErr("该分类类型下还有分类，无法删除")
	}

	return DeleteOne[model.CategoryType](s.model.DbModel, req.Id)
}

func (s *CategoryTypeAdminRepo) DeleteMulti(c *gin.Context, req request.IdsReq) *web.JsonResult {
	// 检查每个ID是否有关联的分类
	for _, idStr := range req.Id {
		if idStr == "" {
			return web.JsonParamErr("ID不能为空")
		}

		var count int64
		err := s.model.Model(&model.Category{}).Where("category_type_id = ?", idStr).Count(&count).Error
		if err != nil {
			return web.JsonInternalError(err)
		}
		if count > 0 {
			return web.JsonParamErr("分类类型ID " + idStr + " 下还有分类，无法删除")
		}
	}

	return DeleteMulti[model.CategoryType](s.model.DbModel, req.Id)
}

func (s *CategoryTypeAdminRepo) SetPriority(c *gin.Context, req request.PriorityReq) *web.JsonResult {
	return SetPriority[model.CategoryType](s.model.DbModel, req)
}
