package client

import (
	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
)

var ProvideSet = wire.NewSet(
	NewClientModel, NewRedisClient, NewOssClient,
)

type Client struct {
	RedisClient *redis.Client
	OssClient   *oss.Client
}

func NewClientModel(redis *redis.Client, oss *oss.Client) *Client {
	return &Client{
		RedisClient: redis,
		OssClient:   oss,
	}
}
