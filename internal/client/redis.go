package client

import (
	"context"
	"fmt"
	"loop/internal/config"
	"net"
	"time"

	"github.com/redis/go-redis/v9"
)

// NewRedisClient 创建Redis客户端（带连接验证）
func NewRedisClient(cfg *config.Config) (*redis.Client, error) {
	client := redis.NewClient(&redis.Options{
		Addr:         cfg.RedisConfig.Host,                                      // Redis地址
		Password:     cfg.RedisConfig.Password,                                  // Redis密码
		DB:           0,                                                         // 默认使用DB 0
		PoolTimeout:  time.Duration(cfg.RedisConfig.PoolTimeout) * time.Second,  // 连接池超时
		DialTimeout:  time.Duration(cfg.RedisConfig.DialTimeout) * time.Second,  // 连接超时
		ReadTimeout:  time.Duration(cfg.RedisConfig.ReadTimeout) * time.Second,  // 读取超时
		WriteTimeout: time.Duration(cfg.RedisConfig.WriteTimeout) * time.Second, // 写入超时
		PoolSize:     cfg.RedisConfig.MaxActive,                                 // 连接池大小
		MinIdleConns: cfg.RedisConfig.MaxIdle,                                   // 最小空闲连接数
		MaxIdleConns: cfg.RedisConfig.MaxIdle,                                   // 最大空闲连接数
	})
	client.AddHook(&redisHook{})
	// 验证连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 使用Ping命令测试连接
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("redis连接失败: %v", err)
	}

	fmt.Println("redis连接成功")

	return client, nil
}

// 日志钩子用于调试
type redisHook struct{}

func (redisHook) DialHook(next redis.DialHook) redis.DialHook {
	return func(ctx context.Context, network, addr string) (net.Conn, error) {
		fmt.Printf("尝试连接到: %s (网络协议: %s)\n", addr, network)
		start := time.Now()
		conn, err := next(ctx, network, addr)
		duration := time.Since(start)
		if err != nil {
			fmt.Printf("连接失败: %v (耗时: %v)\n", err, duration)
		} else {
			fmt.Printf("连接成功 (耗时: %v)\n", duration)
		}
		return conn, err
	}
}
func (redisHook) ProcessHook(next redis.ProcessHook) redis.ProcessHook {
	return func(ctx context.Context, cmd redis.Cmder) error {
		fmt.Printf("执行命令: %s\n", cmd.Name())
		return next(ctx, cmd)
	}
}
func (redisHook) ProcessPipelineHook(next redis.ProcessPipelineHook) redis.ProcessPipelineHook {
	return func(ctx context.Context, cmds []redis.Cmder) error {
		fmt.Printf("执行批量命令: %v\n", cmds)
		return next(ctx, cmds)
	}
}
