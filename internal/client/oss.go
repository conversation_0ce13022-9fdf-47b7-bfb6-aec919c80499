package client

import (
	"loop/internal/config"

	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss/credentials"
)

func NewOssClient(cfg *config.Config) (*oss.Client, error) {
	c := oss.LoadDefaultConfig().
		WithCredentialsProvider(credentials.NewEnvironmentVariableCredentialsProvider()).
		WithRegion(cfg.Setting.AliyunOss.Region)
	client := oss.NewClient(c)

	return client, nil
}
