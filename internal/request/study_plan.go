package request

// CreateStudyPlanReq 创建学习计划请求
type CreateStudyPlanReq struct {
	UserId      int64  `json:"userId" binding:"required"`
	StartLevel  int    `json:"startLevel" binding:"required"`
	TargetLevel int    `json:"targetLevel" binding:"required"`
	LangCode    string `json:"langCode" binding:"required"`
	OldPlanId   int64  `json:"oldPlanId"` // 旧计划ID，用于作废旧计划

	// 问卷信息
	Questionnaire struct {
		MotivationSource string   `json:"motivationSource" binding:"required"`     // 动力来源：career-升职或外派机会，school-申请理想学校，travel-日常旅行，community-融入国际社群，growth-自我成长，other-其他
		ImproveSkills    []string `json:"improveSkills" binding:"required"`        // 想提高的能力：listening-听，speaking-说，reading-读，writing-写
		CurrentLevel     int      `json:"currentLevel" binding:"required"`         // 当前级别
		DailyStudyTime   int      `json:"dailyStudyTime" binding:"required,min=1"` // 每日学习时间(分钟)
	} `json:"questionnaire" binding:"required"`
}

// UpdateResourceProgressReq 更新资源进度请求
type UpdateResourceProgressReq struct {
	ResourceId int64 `json:"resourceId" binding:"required"`
	Count      int   `json:"count" binding:"required,min=1"`
}

// GetPlanDetailsReq 获取计划详情请求
type GetPlanDetailsReq struct {
	PlanId int64 `form:"planId" json:"planId" binding:"required"`
}
