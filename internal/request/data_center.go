package request

type DataEpisodeEachReq struct {
	//本地这里代表的就是 LocalResource 的id
	ResourceId string `form:"resourceId" json:"resourceId" binding:"required"`
	// 1代表远程的资源 2代表本地资源
	ResourceType   int                   `form:"resourceType" json:"resourceType" binding:"required"`
	LearnDuration  int64                 `form:"learnDuration" json:"learnDuration" binding:"required"`
	StartTime      int64                 `form:"startTime" json:"startTime" binding:"required"`
	EndTime        int64                 `form:"endTime" json:"endTime" binding:"required"`
	CurrentLsTimes int64                 `form:"currentLsTimes" json:"currentLsTimes" binding:"required"`
	SentenceList   []DataEpisodeSentence `form:"sentenceData" json:"sentenceData"`
}

// 用户每个视频 不同句子的学习情况
// - 单句维度
// - 每一句通过 LS 的有多少遍（通过=录音分值>自定义分值）
// - 自定义通过分值值设置
type DataEpisodeSentence struct {
	SubtitleId      string `gorm:"size:128;not null" json:"subtitleId"`
	TargetPassTimes int64  `gorm:"not null;comment:通过目标分值的次数" json:"targetPassTimes"`
	LearnDuration   int64  `gorm:"not null;comment:每一句的学习时长" json:"learnDuration"`
	LearnTimes      int64  `gorm:"not null;comment:每一句的学习次数" json:"learnTimes"`
	SubtitleIndex   int64  `gorm:"not null;unique;comment:对应字幕的index" json:"subtitleIndex"`
}
type DataEpisodeReq struct {
	//本地这里代表的就是 LocalResource 的id
	ResourceId string `form:"resourceId" json:"resourceId" binding:"required"`
	// 1代表远程的资源 2代表本地资源
	ResourceType   int   `form:"resourceType" json:"resourceType" binding:"required"`
	CurrentLsTimes int64 `form:"currentLsTimes" json:"currentLsTimes" binding:"required"`
}
type DataEpisodeTypeReq struct {
	// 1日 2周 3月 4年
	Type      int   `form:"type" json:"type" binding:"required"`
	StartTime int64 `form:"startTime" json:"startTime"`
	EndTime   int64 `form:"endTime" json:"endTime"`
}
type EpisodeLsDataReq struct {
	//本地这里代表的就是 LocalResource 的id
	ResourceId string `form:"resourceId" json:"resourceId" binding:"required"`
	// 1代表远程的资源 2代表本地资源
	ResourceType int `form:"resourceType" json:"resourceType" binding:"required"`
}
