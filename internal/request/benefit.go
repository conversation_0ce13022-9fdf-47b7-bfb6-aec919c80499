package request

// CreateBenefitReq 创建权益请求
type CreateBenefitReq struct {
	Name           string `form:"name" json:"name" binding:"required"`
	Code           string `form:"code" json:"code" binding:"required"`
	Level          int    `form:"level" json:"level" binding:"required"`
	CycleType      int    `form:"cycleType" json:"cycleType" binding:"required,oneof=1 2 3 4 5 6"` // 1-日，2-周，3-月，4-季，5-年，6-无周期
	CycleCount     int    `form:"cycleCount" json:"cycleCount" binding:"required"`
	BenefitCount   int    `form:"benefitCount" json:"benefitCount" binding:"required"`
	Sort           int    `form:"sort" json:"sort" binding:"required"`
	Status         int    `form:"status" json:"status" binding:"required,oneof=0 1"`
	BenefitGroupID uint   `form:"benefitGroupId" json:"benefitGroupId" `
	Description    string `form:"description" json:"description"`
}

// UpdateBenefitReq 更新权益请求
type UpdateBenefitReq struct {
	Id             uint   `form:"id" json:"id" binding:"required"`
	Name           string `form:"name" json:"name"`
	Code           string `form:"code" json:"code"`
	Level          int    `form:"level" json:"level"`
	CycleType      int    `form:"cycleType" json:"cycleType" binding:"omitempty,oneof=1 2 3 4 5 6"` // 1-日，2-周，3-月，4-季，5-年，6-无周期
	CycleCount     int    `form:"cycleCount" json:"cycleCount"`
	BenefitCount   int    `form:"benefitCount" json:"benefitCount"`
	Sort           int    `form:"sort" json:"sort"`
	Status         int    `form:"status" json:"status" binding:"omitempty,oneof=0 1"`
	BenefitGroupID uint   `form:"benefitGroupId" json:"benefitGroupId"`
	Description    string `form:"description" json:"description"`
}

// BenefitPageReq 权益分页查询请求
type BenefitPageReq struct {
	CurrentPage      int    `form:"currentPage" json:"currentPage" binding:"required"`
	PageSize         int    `form:"pageSize" json:"pageSize" binding:"required"`
	Name             string `form:"name" json:"name"`
	BenefitGroupCode string `form:"benefitGroupCode" json:"benefitGroupCode"`
	Status           int    `form:"status" json:"status"`
}

// CreateVipLevelReq 创建VIP等级请求
type CreateVipLevelReq struct {
	Name        string  `form:"name" json:"name" binding:"required"`
	Level       int     `form:"level" json:"level" binding:"required,gt=0"`
	Description string  `form:"description" json:"description"`
	Price       float64 `form:"price" json:"price" binding:"required,gte=0"`
	Status      int     `form:"status" json:"status" binding:"required,oneof=0 1"`
}

// UpdateVipLevelReq 更新VIP等级请求
type UpdateVipLevelReq struct {
	Id          uint    `form:"id" json:"id" binding:"required"`
	Name        string  `form:"name" json:"name" binding:"required"`
	Level       int     `form:"level" json:"level" binding:"required,gt=0"`
	Description string  `form:"description" json:"description"`
	Price       float64 `form:"price" json:"price" binding:"required,gte=0"`
	Status      int     `form:"status" json:"status" binding:"required,oneof=0 1"`
}

// VipLevelPageReq VIP等级分页查询请求
type VipLevelPageReq struct {
	CurrentPage int    `form:"currentPage" json:"currentPage" binding:"required"`
	PageSize    int    `form:"pageSize" json:"pageSize" binding:"required"`
	Name        string `form:"name" json:"name"`
	Status      int    `form:"status" json:"status"`
}

// UpdateVipLevelBenefitReq 更新VIP等级权益请求
type UpdateVipLevelBenefitReq struct {
	VipLevelId int64   `form:"vipLevelId" json:"vipLevelId" binding:"required"`
	BenefitIds []int64 `form:"benefitIds" json:"benefitIds" binding:"required,dive,gt=0"`
}

// UpdateBenefitRuleReq 更新权益规则请求
type UpdateBenefitRuleReq struct {
	BenefitId int64 `form:"benefitId" json:"benefitId" binding:"required"`
}

// CreateBenefitGroupReq 创建权益组请求
type CreateBenefitGroupReq struct {
	Name string `form:"name" json:"name" binding:"required"`
	Code string `form:"code" json:"code" binding:"required"`
}

type GetBenefitsByGroupCodeReq struct {
	Code string `form:"code" json:"code" binding:"required"`
}

// UpdateBenefitGroupReq 更新权益组请求
type UpdateBenefitGroupReq struct {
	Id   uint   `form:"id" json:"id" binding:"required"`
	Name string `form:"name" json:"name" binding:"required"`
	Code string `form:"code" json:"code" binding:"required"`
}

// BenefitGroupPageReq 权益组分页查询请求
type BenefitGroupPageReq struct {
	CurrentPage int `form:"currentPage" json:"currentPage" binding:"required"`
	PageSize    int `form:"pageSize" json:"pageSize" binding:"required"`
}

// UpdateBenefitGroupStatusReq 更新权益组状态请求
type UpdateBenefitGroupStatusReq struct {
	Id     uint `form:"id" json:"id" binding:"required"`
	Status int  `form:"status" json:"status" binding:"required"`
}

// GetUserBenefitsByGroupReq 获取用户指定组权益请求
type GetUserBenefitsByGroupReq struct {
	BenefitGroupCode string `form:"benefitGroupCode" json:"benefitGroupCode" binding:"required"`
}
