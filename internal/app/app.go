package app

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// App is an application components lifecycle manager.
type App struct {
	hs  *http.Server
	db  *gorm.DB
	rdb *redis.Client
}

// New create an application lifecycle manager.
func New(hs *http.Server, db *gorm.DB, rdb *redis.Client) *App {
	return &App{
		hs:  hs,
		db:  db,
		rdb: rdb,
	}
}

// Run executes all OnStart hooks registered with the application's Lifecycle.
func (a *App) Run() error {
	// 创建一个通道来接收操作系统的信号
	quit := make(chan os.Signal, 1)
	// 监听 SIGINT 和 SIGTERM 信号
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// 在一个新的 goroutine 中启动服务器
	go func() {
		if err := a.hs.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			panic(err)
		}
	}()

	// 等待退出信号
	<-quit

	// 收到退出信号后，调用 Stop 方法优雅地关闭服务
	if err := a.Stop(); err != nil {
		return err
	}

	return nil
}

// Stop gracefully stops the application.
func (a *App) Stop() error {
	// 关闭 HTTP 服务器
	if err := a.hs.Shutdown(context.Background()); err != nil {
		return err
	}

	// 关闭 Redis 连接
	if err := a.rdb.Close(); err != nil {
		return err
	}

	// 关闭数据库连接
	sqlDB, err := a.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}
