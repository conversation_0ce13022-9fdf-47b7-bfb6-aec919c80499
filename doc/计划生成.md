下面是一个完整的工作流程示例，展示如何结合向量数据库和大模型生成计划：

用户提交需求：用户填写英语水平、学习目标、偏好等信息
需求向量化：将用户需求转换为向量
资源检索：基于用户需求向量，从向量数据库中检索 Top 50 相关资源
生成阶段框架：向大模型发送用户需求和精选资源的轻量级元数据，请求生成阶段框架
阶段资源细化：针对每个阶段，再次检索更具体的相关资源
详细计划生成：为每个阶段生成详细的资源使用计划
计划整合与去重：整合所有阶段计划，确保资源使用的连贯性和多样性



GeneratePlan需要增加功能，在callAIService的时候，就需要调用AI服务了，
通过AI来，先资源给ai，ai根据匹配来筛选出匹配到的资源，需要写扩展，后续如果资源多，可以分批次，第一批给多少个资源去匹配，第二批给多少个资源去匹配

因为我一次性传太多资源给AI效果并不好，所以callAIService这部分可能需要从命名，可能需要修改为类似callGenPlanAIService，然后新增callfilterResourceService类似的方法，用来筛选前5个资源，将获取到的resources，然后丢给AI，让AI根据用户信息和问卷信息来返回我5个资源信息，返回的是一个json，json里是资源列表，每个资源仅包含资源id信息

然后根据这个筛选后的资源再去调用原来的callAIService去生成计划、