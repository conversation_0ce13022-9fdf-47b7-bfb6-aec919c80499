user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /run/nginx.pid;

include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    log_format main
        '$remote_addr - $remote_user [$time_local] "$request" '
        '$status $body_bytes_sent "$http_referer" '
        '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 4096;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 全局SSL配置（建议使用泛域名证书）
    ssl_certificate /var/local/seedtu.pem;
    ssl_certificate_key /var/local/seedtu.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 24h;

    # HTTP强制跳转HTTPS（全域名）
    server {
        listen 80;
        server_name seedtu.com www.seedtu.com *************; 

        location / {
            return 301 https://$host$request_uri;
        }
    }

    # API服务（后端接口）
    server {
        listen 443 ssl http2;
        server_name test.seedtu.com;

        ssl_certificate /var/local/testseedtu.pem;
        ssl_certificate_key /var/local/testseedtu.key;

        location / {
            proxy_pass http://localhost:3000; # 指向API服务地址
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 长连接优化
            proxy_set_header Connection "";
            proxy_connect_timeout 75s;
            proxy_read_timeout 300s;
        }
    }
}