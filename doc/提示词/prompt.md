请基于以下需求使用Golang和GORM实现英语学习目标计划功能：

首先有一些问卷信息，这些问卷信息都是从前端传递过来的，主要包含几个问卷信息

1. 你最大的动力来源是什么
2. 你最想提高什么能力
3. 你的当前级别
4. 你要提高到什么级别，级别这个部分我后面会说
5. 你平常希望每天抽出多少时间学习

这些问卷信息都需要存在数据库，由于我已经有一个user表了，所以这些还是不要存在user表里，而是单独一个关联表来存储，每个用户只有一条数据

然后就是级别，这个级别的逻辑是:
有几个大阶段：A0 A1 A2 B1 B2 C1 C2
然后，A0到A1还有两个小阶段，是A0-1  A0-2,同理 A1到A2还有两个小阶段，是A1-1  A1-2

当接受到问卷信息后，需要给用户生成一份目标计划，首先，会根据AI来生成一份计划，然后AI这部分你可以先不用管，预留一个方法来处理就好，就是调用一个第三方的服务然后返回给我一个json，然后根据这个json来生成计划，
我会把资源信息丢给AI，资源信息就是在我的resource部分有资源表，AI做的事情就是，我提供资源信息、用户的问卷信息、他当前的级别、想要提到的级别、每天抽出多少时间学习这些信息都给AI，然后AI给我的json里包括这样的内容

```json
[
  {
    "stageId": "A0-1",
    "period": "今天-1月2日",
    "objective": "建立学习习惯",
    "task": "7天完成五分钟视频的21遍LS学习",
    "resources": [
      {
        "resourceId": "EM2301",
        "schedule": [
          {"dayType": "everyday", "timeSlots": ["09:41-09:45", "19:00-19:05"]},
          {"dayType": "weekdays", "timeSlots": ["18:30-18:35"]}
        ],
        "learningPattern": "h-min",
        "sessionCode": "LS-01"
      }
    ]
  },
  {
    "stageId": "A0-2",
    "period": "今天-1月2日",
    "objective": "建立学习习惯",
    "task": "7天完成五分钟视频的21遍LS学习",
    "resources": [
      {
        "resourceId": "EM2302",
        "schedule": [
          {"dayType": "allDays", "timeSlots": ["碎片时段"]},
          {"dayType": "weekends", "timeSlots": ["09:00-09:10", "21:00-21:10"]}
        ],
        "learningPattern": "ls",
        "sessionCode": "LS-02"
      }
    ]
  }
]
```

然后我需要将这个json的信息转换成数据库的数据，比如resourceId就去资源表去查询，然后插入到具体的表中，这样就形成了一个计划

计划同一时间只能有一个计划正在执行，计划不能被删除只能作废，  重新生产计划就相当于给之前正在进行中的计划作废

注意，这属于新的模块，就叫plan模块，在internal中的各个文件夹下都需要创建对应的文件，并且在server的route中创建对应的路由接口,我的三层结构，model层在/internal/model中，data层在/internal/data/中，api层在/internal/api中，路由的配置在/server/router.go中，在编写新的文件的时候需要和之前的处理方式保持一致，不能够出现外键，需要使用关联表的方式，然后所有和sql相关的不要使用字符串，除非无法使用模型类来处理，否则默认都有模型类来处理sql相关。

