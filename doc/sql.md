阿里云数据库 新密码：Yongjun0118\*

## 安装 YAPI

https://github.com/fjc0k/docker-YApi

### 服务器上，应用部署到 docker 后无法连接 mysql

连接数据库使用的地址是 127.0.0.1，但是需要使用服务器提供的**主私网 IP**

可以使用 `ip addr show docker0`找到对应的 ip 地址

```jsx
[root@iZb app]# ip addr show docker0
5: docker0: <NO-CARRIER,BROADCAST,MULTICAST,UP> mtu 1500 qdisc noqueue state DOWN group default
    link/ether 02:42:cd:42:e0:57 brd ff:ff:ff:ff:ff:ff
    inet **********/16 brd ************** scope global docker0
       valid_lft forever preferred_lft forever
    inet6 fe80::42:cdff:fe42:e057/64 scope link
       valid_lft forever preferred_lft forever
```

然后将连接 mysql 的修改为

之后会提示

```jsx
failed to initialize database, got error Error 1130: Host '**********' is not allowed to connect to this MySQL server
```

说明没有权限

从 MySQL 8.0 开始，`GRANT` 语句不再接受 `IDENTIFIED BY` 部分。你需要先创建用户，然后分别授予权限。

以下是对应的命令：

1. **创建用户**：

   ```sql
   CREATE USER 'username'@'**********' IDENTIFIED BY 'password';

   ```

   在这个命令中，`username` 是你的应用的 MySQL 用户名，`**********` 是你的 Docker 容器的 IP 地址，`password` 是用户的密码。

2. **授予用户权限**：

   ```sql
   GRANT ALL PRIVILEGES ON dbname.* TO 'username'@'**********';
   GRANT ALL PRIVILEGES ON loop.* TO 'root'@'**********';
   ```

   在这个命令中，`dbname` 是你的数据库名，`username` 是你的应用的 MySQL 用户名，`**********` 是你的 Docker 容器的 IP 地址。

3. **刷新权限**：

   ```sql
   FLUSH PRIVILEGES;
   ```

   这个命令会使得你的更改立即生效。

### 自己电脑连接服务器数据库

查看自己的公网 ip
curl ifconfig.me

```sql
-- 连接sql
mysql -u root -p

SELECT User, Host FROM mysql.user;

-- 删除用户
DROP USER 'username'@'hostname';
```

确保你有一个可以远程访问的 MySQL 用户。如果没有，可以创建一个。假设你的用户名是 username，密码是 password，并且你希望用户从任意主机连接（%）：

```sql
CREATE USER 'myj'@'%' IDENTIFIED BY 'Yongjun0118*';
GRANT ALL PRIVILEGES ON *.* TO 'myj'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;
```
