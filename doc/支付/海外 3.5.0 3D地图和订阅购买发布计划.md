# 海外 3.5.0 3D地图和订阅购买发布计划

#### 项目发布负责人

服务端： $\color{#0089FF}{@吕刘杰(小布)}$  $\color{#0089FF}{@俞鸿泰(鸿泰)}$ 

IOS： $\color{#0089FF}{@王凯}$ 

安卓： $\color{#0089FF}{@缪永俊(阿俊)}$ 

H5： $\color{#0089FF}{@曾志银}$ 

#### 发布内容

1、3D地图

2、苹果支付和pingpong支付的订阅购买

3、会员和权益系统

#### 项目发布时间：10-30  10:00

[ ] **发布前准备** $\color{#0089FF}{@张君宁(君宝)}$  $\color{#0089FF}{@王凯}$ 

*   [x] 多语言翻译
    

*   [x] 商品定价等信息确认：美服：月卡连续$7.99、年卡连续$79.99；日服：月卡连续 ¥1200、年卡连续¥12000
    

*   [ ] 三方支付的正式环境申请准备（可以让相关技术协助，iOS需要额外准备证书和私钥文件）
    

*   [ ] 3D地图相关准备（账号申请，决定需要增加3D的地图，地图的3D封面）
    

*   [ ] 测试确定线上测试只服用的
    

#### 发布顺序

*   [ ] 0、在预发环境，测试回归除正式支付以外的所有功能（测试需要冒烟测试老app接新后端代码不出问题） $\color{#0089FF}{@黄坷佳(三千)}$  $\color{#0089FF}{@彭益顺(益达)}$ 
    
*   [x] 1、产品确定日服商品价格和美服商品价格（各自服单独定价，不使用苹果支付的自动转换） $\color{#0089FF}{@张君宁(君宝)}$ 
    
*   [ ] 2、pingpong修改支付配置，直接在预发环境测试正式支付功能 $\color{#0089FF}{@俞鸿泰}$ 
    
*   [ ] 3、iOS开发前往苹果管理页面创建生产环境将要使用的日服的月卡订阅和年卡订阅，美服的月卡订阅和年卡订阅 $\color{#0089FF}{@王凯}$ 
    
*   [ ] 4、后端发布代码到正式环境： $\color{#0089FF}{@俞鸿泰}$  $\color{#0089FF}{@吕刘杰(小布)}$ 
    

*   [ ] 修改表结构，执行 DDL，增加 nacos 配置
    
*   [ ] 执行DML，初始化默认权益，权益组，会员, 同时将默认权益的 level 设置成 0。这步务必在“初始化存量用户默认权益”之前做
    
*   [ ] 发布 admin、course、user
    
*   [ ] 初始化存量用户用户的默认权益
    

*   [ ] 5、H5发布后端管理页面，并配制菜单权限 $\color{#0089FF}{@曾志银}$ 
    

*   [ ] 6、产品按如下顺序添加本次的订阅、会员、权益数据 $\color{#0089FF}{@张君宁(君宝)}$ 
    

*   [ ] 创建权益
    
*   [ ] 修改会员
    
*   [ ] 在日服、美服分别创建普通用户可见的月卡订阅和年卡订阅并上线，创建白名单（测试）用户可见的测试商品但是不上线，由开发修改测试商品的isHide字段和上线状态：美服：月卡连续$7.99、年卡连续$79.99；日服：月卡连续 ¥1200、年卡连续¥12000
    

*   [ ] 7、iOS提包最新app（不上架），在最新的正式包上测试正式支付功能 $\color{#0089FF}{@王凯}$ 
    

*   [ ] 8、修改3D地图相关配置（封面） $\color{#0089FF}{@张君宁(君宝)}$ 
    

*   [ ] 9、测试在正式环境回归，通过后正式上架安卓和iOS的app包 $\color{#0089FF}{@黄坷佳(三千)}$  $\color{#0089FF}{@彭益顺(益达)}$ 
    

[ ] **DDL** $\color{#0089FF}{@吕刘杰 (小布)}$ 

*   [ ] 日服
    
*   [ ] 美服
    

merit库

**订阅-会员-权益表**结构

```text/x-sql
# 权益表
CREATE TABLE `benefit` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `name` varchar(64)     NOT NULL COMMENT '权益名',
  `i18n_code` varchar(64)     NOT NULL COMMENT '权益国际化code',
  `level` int DEFAULT '10' COMMENT '权益等级',
  `cycle_type` int NOT NULL COMMENT '权益周期类型：1-月，2-季，3-年，4-日，5-周',
  `cycle_count` int NOT NULL COMMENT '权益周期数量',
  `benefit_count` int NOT NULL COMMENT '权益数量',
  `sort` int NOT NULL DEFAULT '1' COMMENT '排序，越大越靠前',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态：0-禁用，1-启用',
  `benefit_group_name` varchar(64)     NOT NULL COMMENT '组名',
  `benefit_group_code` varchar(64)     NOT NULL COMMENT '组编号',
  `benefit_group_id` bigint NOT NULL COMMENT '权益分组id',
  `online_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0-未上过线 1-上过线',
  `create_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  `update_id` bigint DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_benefitgroupcode` (`benefit_group_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='权益表';

# 权益分组
CREATE TABLE `benefit_group` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `name` varchar(64) DEFAULT NULL COMMENT '组名',
  `code` varchar(64) DEFAULT NULL COMMENT '组编号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='权益组表';

# 会员
CREATE TABLE `vip` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `name` varchar(64) DEFAULT NULL COMMENT '名称',
  `level` int DEFAULT '10' COMMENT '会员等级',
  `status` int DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  `update_id` bigint DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会员表';

# 会员权益表
CREATE TABLE `vip_benefit` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `vip_id` bigint NOT NULL COMMENT '会员主键',
  `benefit_id` bigint NOT NULL COMMENT '权益主键',
  `benefit_group_id` bigint NOT NULL COMMENT '权益组主键',
  `benefit_group_code` varchar(64)     NOT NULL COMMENT '权益组编号',
  `create_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `udx_vipid_benefitid` (`vip_id`,`benefit_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会员权益表';

# 商品
CREATE TABLE `trade_product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `name` varchar(64)      NOT NULL COMMENT '名称',
  `i18n_code` varchar(64)      NOT NULL COMMENT '国际码',
  `type` int NOT NULL DEFAULT '1' COMMENT '商品类型：1-会员',
  `online_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0-未上过线 1-上过线',
  `is_subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否为订阅商品：0-否，1-是',
  `price` decimal(10,2) NOT NULL COMMENT '实际价格',
  `origin_price` decimal(10,2) NOT NULL COMMENT '原价价格',
  `currency` varchar(16)      NOT NULL COMMENT '币种',
  `sort` int NOT NULL DEFAULT '1' COMMENT '排序，越大越靠前',
  `terminal` int NOT NULL DEFAULT '1' COMMENT '终端：1-Android，2-IOS',
  `out_product_code` varchar(64)      DEFAULT NULL COMMENT '第三方支付平台商品编码',
  `is_hide` tinyint NOT NULL DEFAULT '0' COMMENT '是否仅白名单用户可见：0-否，1-是',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态：0-禁用，1-启用',
  `vip_id` bigint NOT NULL COMMENT '会员id',
  `vip_level` int NOT NULL COMMENT '会员等级',
  `vip_cycle_type` int NOT NULL COMMENT '会员周期类型：1-月，2-季，3-年，4-日，5-周',
  `vip_cycle_count` int NOT NULL COMMENT '会员周期数量',
  `create_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_timestamp` bigint NOT NULL COMMENT '创建时间戳',
  `update_id` bigint NOT NULL COMMENT '修改人ID',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_timestamp` bigint NOT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';


# 用户权益
CREATE TABLE `user_benefit` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `benefit_group_code` varchar(64)     NOT NULL COMMENT '权益组编号',
  `benefit_id` bigint NOT NULL DEFAULT '0' COMMENT '权益id',
  `benefit_name` varchar(64)     NOT NULL COMMENT '权益名',
  `benefit_i18n_code` varchar(64)     NOT NULL COMMENT '权益国际化code',
  `remain` int NOT NULL COMMENT '权益剩余数量',
  `total` int NOT NULL COMMENT '权益总数量',
  `last_refresh_time` datetime NOT NULL COMMENT '最后刷新时间',
  `last_refresh_timestamp` bigint NOT NULL COMMENT '最后刷新时间戳',
  `next_refresh_time` datetime NOT NULL COMMENT '下次刷新时间',
  `next_refresh_timestamp` bigint NOT NULL COMMENT '下次刷新时间戳',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态：0-失效，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_userid_benefitid_groupcode` (`user_id`,`benefit_id`,`benefit_group_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户权益表';

# 用户权益流水
CREATE TABLE `user_benefit_flow` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `biz_type` int NOT NULL DEFAULT '1' COMMENT '产生流水的业务',
  `biz_id` varchar(64)     DEFAULT NULL COMMENT '业务来源id，如订单id、兑换code',
  `operation` int NOT NULL DEFAULT '1' COMMENT '操作：1-新增，2-减少，3-刷新，4-关闭',
  `user_benefit_id` bigint NOT NULL DEFAULT '0' COMMENT '用户在用权益id',
  `benefit_group_code` varchar(64)     NOT NULL COMMENT '权益组编号',
  `benefit_id` bigint NOT NULL DEFAULT '0' COMMENT '权益id',
  `benefit_name` varchar(64)     NOT NULL COMMENT '权益名',
  `count` int NOT NULL COMMENT '变动数量',
  `remain` int NOT NULL COMMENT '变动后剩余数量',
  `terminal` tinyint NOT NULL DEFAULT '0' COMMENT '终端：0-未知，1-Android，2-IOS',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  `update_id` bigint DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_userid` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户权益流水表';


# 用户会员
CREATE TABLE `user_vip` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `is_vip` int NOT NULL DEFAULT '0' COMMENT '状态：0-非会员，1-是会员',
  `vip_id` bigint NOT NULL COMMENT '会员id',
  `vip_level` int NOT NULL COMMENT '会员等级',
  `expire_date` date NOT NULL COMMENT '到期日期',
  `expire_timestamp` bigint NOT NULL COMMENT '到期日期时间戳',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_timestamp` bigint NOT NULL COMMENT '创建时间戳',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `udx_userid` (`user_id`) COMMENT '用户唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户会员表';

# 用户会员流水
CREATE TABLE `user_vip_flow` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `vip_id` bigint NOT NULL,
  `vip_level` int NOT NULL,
  `biz_type` int NOT NULL DEFAULT '1' COMMENT '产生流水的类型：1-购买，202-会员到期',
  `biz_id` varchar(64) DEFAULT NULL COMMENT '业务来源id，如订单id、兑换code',
  `operation` int NOT NULL DEFAULT '1' COMMENT '操作：1-开通，2-关闭，3-升级',
  `operation_time` datetime DEFAULT NULL COMMENT '开通/关闭时间',
  `operation_timestamp` bigint DEFAULT NULL COMMENT '开通/关闭时间戳',
  `days` int NOT NULL DEFAULT '0' COMMENT '开通天数',
  `expire_date` date DEFAULT NULL COMMENT '本次开通后的过期日期',
  `expire_timestamp` bigint DEFAULT NULL COMMENT '本次开通后的过期时间戳',
  `terminal` tinyint DEFAULT '0' COMMENT '终端：0-未知，1-Android，2-IOS',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_userid_opertationtime` (`user_id`,`operation_time` DESC) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户会员流水表';


# 用户订阅
CREATE TABLE `user_subscription` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_name` varchar(64)     NOT NULL COMMENT '名称',
  `payment_provider` int NOT NULL COMMENT '三方支付：3-苹果内购，10-paypal',
  `out_subscription_id` varchar(64)     NOT NULL COMMENT '第三方支付平台订阅id',
  `first_order_no` varchar(64)     NOT NULL COMMENT '订阅时的第一次支付订单号',
  `first_cycle_amount` decimal(10,2) NOT NULL COMMENT '订阅第一个周期金额',
  `next_cycle_amount` decimal(10,2) NOT NULL COMMENT '订阅下一个周期金额',
  `currency` varchar(16)     NOT NULL COMMENT '币种',
  `next_paid_date` date NOT NULL COMMENT '下一次支付日期',
  `next_paid_timestamp` bigint NOT NULL COMMENT '下一次支付日期时间戳',
  `sign_timestamp` bigint NOT NULL COMMENT '签约时间',
  `cancel_timestamp` bigint DEFAULT NULL COMMENT '解约时间',
  `status` int NOT NULL DEFAULT '1' COMMENT '订阅状态：1-签约，0-解约',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_timestamp` bigint NOT NULL COMMENT '创建时间戳',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  `terminal` int NOT NULL COMMENT '终端 1-安卓 2-ios',
  `renew_fail_days` int NOT NULL DEFAULT '0' COMMENT '续约失败天数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_nextpaiddate_paymentprovider_status` (`next_paid_date`,`payment_provider`,`status`),
  KEY `idx_userid` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户订阅表';

# 用户订阅流水
CREATE TABLE `user_subscription_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `product_id` bigint DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(64) DEFAULT NULL COMMENT '名称',
  `payment_provider` int DEFAULT NULL COMMENT '三方支付：3-苹果内购，10-paypal',
  `user_subscription_id` bigint NOT NULL DEFAULT '0' COMMENT '订阅id',
  `operation` int DEFAULT '1' COMMENT '订阅操作：1-签约，2-续约，3-解约',
  `next_paid_date` varchar(16) DEFAULT NULL COMMENT '下一次支付日期',
  `next_paid_timestamp` bigint DEFAULT NULL COMMENT '下一次支付日期时间戳',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_usersubscriptionid` (`user_subscription_id`),
  KEY `idx_userid` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户订阅日志表';


# 订单表
CREATE TABLE `trade_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `order_no` varchar(64)      NOT NULL COMMENT '订单编号',
  `out_trade_no` varchar(64)      DEFAULT NULL COMMENT '外部交易号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_name` varchar(64)      NOT NULL COMMENT '名称',
  `product_type` tinyint NOT NULL DEFAULT '1' COMMENT '商品类型：1-会员',
  `user_subscription_id` bigint DEFAULT NULL COMMENT '用户订阅ID',
  `currency` varchar(16)      NOT NULL COMMENT '币种',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `payment_provider` int DEFAULT NULL COMMENT '三方支付：3-苹果内购，10-paypal，11-pingpong',
  `paid_time` datetime DEFAULT NULL COMMENT '支付时间',
  `paid_timestamp` bigint DEFAULT NULL COMMENT '支付时间戳',
  `status` int NOT NULL DEFAULT '1' COMMENT '订单状态：0-交易关闭，1-待支付，2-交易完成，3-交易失败',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_timestamp` bigint NOT NULL COMMENT '创建时间戳',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  `ext_info` json DEFAULT NULL COMMENT '额外字段，主要放支付信息',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_order_no` (`order_no`),
  KEY `idx_userid` (`user_id`),
  KEY `idx_createtimestamp_paymentprovider` (`create_timestamp`,`payment_provider`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';
```

**3D地图、训练报告** 新增字段

```text/x-sql
# 3d地图新增3d静态封面、3d动态地图封面、是否支持3d字段
alter table sport_race_map 
add column support3d tinyint DEFAULT 0 comment '是否支持3d地图：0-否，1-是' after status ,
add column static_cover3d  varchar(255) DEFAULT NULL COMMENT '3d静态封面' after support3d,
add column dynamic_cover3d varchar(255) DEFAULT NULL COMMENT '3d动态封面' after static_cover3d;

# 训练类型增加子类型
alter table sport_result
add column sub_train_type int DEFAULT NULL COMMENT '训练子类型 不同的训练类型有不同的子训练含义' after train_type ;
```

[ ] **DML** $\color{#0089FF}{@吕刘杰 (小布)}$ 

*   [ ] 日服
    
*   [ ] 美服
    

1、所有老用户增加默认权益（benefit\_group\_code 和 benefit\_id 需要依据线上的来）

```text/x-sql
# 权益组数据初始化
INSERT INTO `benefit_group` (`id`, `name`, `code`, `create_time`, `create_timestamp`) VALUES (1, '3d地图权益', '3dm001', '2024-10-28 11:20:44', NULL);

# 页面新建一条默认权益，去数据库里把权益等级设置为0
  update benefit set level=0 where id 1;

# 用户权益初始化
insert into user_benefit(user_id,benefit_group_code,benefit_id,benefit_name,benefit_i18n_code,remain,total,last_refresh_time,last_refresh_timestamp,next_refresh_time,next_refresh_timestamp,status)
 (select id,'3dm001',1,'每月2次权益','3d_map_2_month',2,2,now(),UNIX_TIMESTAMP(NOW()) * 1000,'2024-11-30',1732896000000,1 from user_info);

# vip初始化
INSERT INTO `vip` (`id`, `name`, `level`, `status`, `create_id`, `create_time`, `create_timestamp`, `update_id`, `update_time`, `update_timestamp`) VALUES (1, '月卡会员', 10, 0, 0, now(), UNIX_TIMESTAMP(NOW()) * 1000,0, now(), UNIX_TIMESTAMP(NOW()) * 1000);

INSERT INTO `vip` (`id`, `name`, `level`, `status`, `create_id`, `create_time`, `create_timestamp`, `update_id`, `update_time`, `update_timestamp`) VALUES (2, '年卡会员', 20, 0, 0, now(), UNIX_TIMESTAMP(NOW()) * 1000,0, now(), UNIX_TIMESTAMP(NOW()) * 1000);




```

[ ] **Nacos配置**

yudong-user (线上配置需要变动)

```text/x-sql
payment:
    pingpong:
      host: https://sandbox-acquirer-payment.pingpongx.com/v4
      salt: 2A634238036D3A72395C1E67
      clientId: 2024100909522210314
      accId: 2024100909522210314557
      notifyUrl: https://preapi-usa.merach.com/user/trade/notify/ping-pong
      payResultUrl: https://stagingconsole.merach.com/webview/1.0.0/payment-result/index.html
    apple-pay:
      environment: Sandbox
      bundleId: com.mrk.MeritInternation
      appAppleId: 1603931285
      issuerId: f6bdb8ee-e29c-4a27-8137-577c0ddf9857
      keyId: 7RZ59GN92L
      rootCA: AppleRootCA-G3.cer
      p8file: SubscriptionKey_7RZ59GN92L.p8
```

[ ] 代码发布 $\color{#0089FF}{@陈浩 (天泽)}$  $\color{#0089FF}{@曾志银(长风)}$ 

|  是否发布  |  项目名称  |  开发人员  |  版本号  |  CodeReview  |  发布内容  |  备注  |
| --- | --- | --- | --- | --- | --- | --- |
|  *   [ ] 日服      *   [ ] 美服       |  yudng-admin  |  小布  |   |   |   |  prod  |
|  *   [ ] 日服      *   [ ] 美服       |  yudong-course  |  鸿泰  |   |   |  |  prod  |
|  *   [ ] 日服      *   [ ] 美服       |  yudong-user  |  小布  |   |   |  |  prod  |
|  *   [ ] 日服      *   [ ] 美服       |  yudong-admin-web  |  志银  |   |   |   |  管理后台  |
|  *   [ ] 日服      *   [ ] 美服       |  meritinternation-h5  |  志银  |   |   |   |  h5  |

[ ] **权限配置@志银**

*   [ ] 订阅商品
    
*   [ ] 会员
    
*   [ ] 权益
    

[ ] **初始数据创建** $\color{#0089FF}{@张君宁(君宝)}$ 

*   [ ] 多语言更新
    

*   [ ] 技术通过sql语句准备默认权益、权益组、会员 $\color{#0089FF}{@俞鸿泰(鸿泰)}$ 
    

*   [ ] 权益
    

*   [ ] 会员
    

*   [ ] 订阅商品
    

[ ] **线上回归** $\color{#0089FF}{@黄坷佳 (三千)}$  $\color{#0089FF}{@彭益顺 (益达)}$ 

[ ] APP提审 $\color{#0089FF}{@王凯}$  $\color{#0089FF}{@缪永俊(阿俊)}$