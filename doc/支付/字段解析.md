### notificationType+subtype

回调接口数据里面的通知类型, 原来 v1 版本只有 notificationType 来识别，在 storekit2 中 notificationType+subtype 配合细化通知类型。

```
SUBSCRIBED  如果subtype是INITIAL_BUY（首次购买），则用户首次通过“家人共享”购买或接收了对订阅的访问权限。如果是RESUBSCRIBE（重新购买/重新购买同一个组内的plan），则用户通过家庭共享重新订阅或接收了对同一订阅或同一订阅组内的另一个订阅的访问权限。

CONSUMPTION_REQUEST 一种通知类型，表明客户发起了应用内消费品(大师课/肤质检测次数购买)购买的退款请求

DID_RENEW 一种通知类型，与其一起subtype指示订阅已成功续订。如果subtype是BILLING_RECOVERY，则之前续订失败的过期订阅已成功续订。如果子状态为空（续订），则活动订阅已成功自动续订新的交易周期。为客户提供对订阅内容或服务的访问权限。

OFFER_REDEEMED 一种通知类型，与其 一起subtype指示用户兑换了促销优惠或优惠代码。如果subtype是INITIAL_BUY，则用户兑换了首次购买的优惠。如果是RESUBSCRIBE，则用户兑换了重新订阅非活动订阅的优惠。如果是UPGRADE，则用户兑换了升级其有效订阅的优惠，该优惠立即生效。如果是DOWNGRADE，则用户兑换了降级其有效订阅的优惠，该优惠将在下一个续订日期生效。

REFUND 一种通知类型，指示 AppStore已成功对消费品应用内购买、非消费品应用内购买、自动续订订阅或非续订订阅的交易进行退款。

DID_CHANGE_RENEWAL_STATUS 一种通知类型，与其一起subtype指示用户对订阅续订状态进行了更改。如果subtype=AUTO_RENEW_ENABLED，则用户重新启用订阅自动续订。如果是AUTO_RENEW_DISABLED，则用户禁用了订阅自动续费，或者用户申请退款后App Store禁用了订阅自动续费。

DID_CHANGE_RENEWAL_PREF 一种通知类型，与其一起subtype指示用户对其订阅计划进行了更改。如果subtype是UPGRADE，则用户升级了他们的订阅。升级立即生效，开始新的计费周期，用户将收到上一周期未使用部分的按比例退款。如果subtype是DOWNGRADE，则用户降级了他们的订阅。降级将在下一个续订日期生效，并且不会影响当前有效的计划。如果subtype为空，则用户将其续订首选项更改回当前订阅，从而有效地取消降级。

4. DID_FAIL_TO_RENEW 一种通知类型，与其一起subtype指示订阅由于计费问题而未能续订。订阅进入计费重试期。如果subtype是GRACE_PERIOD，则在宽限期内继续提供服务。如果为空，则说明订阅不在宽限期内，您可以停止提供订阅服务。

6. EXPIRED 如果subtype是VOLUNTARY，则订阅在用户禁用订阅续订后过期。如果subtype是BILLING_RETRY，则订阅已过期，因为计费重试期已结束，但没有成功的计费事务。如果是PRICE_INCREASE，则订阅已过期，因为用户不同意需要用户同意的价格上涨。如果是PRODUCT_NOT_FOR_SALE，则订阅已过期，因为在订阅尝试续订时该产品不可购买。

7. GRACE_PERIOD_EXPIRED 一种通知类型，指示计费宽限期已结束而无需续订订阅，因此您可以关闭对服务或内容的访问。通知用户他们的账单信息可能存在问题。

10. PRICE_INCREASE 一种通知类型，与其一起subtype表示系统已通知用户自动续订订阅价格上涨。
如果涨价需要用户同意，是subtype指PENDING用户没有对涨价做出回应，或者ACCEPTED用户已经同意涨价。
如果涨价不需要用户同意，那subtype就是ACCEPTED。

12. REFUND_DECLINED 一种通知类型，指示 AppStore 拒绝了应用开发者使用以下任一方法发起的退款请求

13. REFUND_REVERSED 一种通知类型，表明 App Store 由于客户提出的争议而撤销了之前授予的退款。如果您的应用因相关退款而撤销了内容或服务，则需要恢复它们。
此通知类型可适用于任何应用内购买类型：消耗型、非消耗型、非续订订阅和自动续订订阅。对于自动续订订阅，当 App Store 撤销退款时，续订日期保持不变。

14. RENEWAL_EXTENDED 一种通知类型，指示 App Store 延长了特定订阅的订阅续订日期。您可以通过调用App Store Server API中的延长订阅续订日期或为所有活跃订阅者延长订阅续订日期来请求订阅续订日期延期。

15. RENEWAL_EXTENSION 一种通知类型，与其一起subtype表示 AppStore 正在尝试通过调用为所有活跃订阅者延长订阅续订日期 来延长您请求的订阅续订日期。如果subtype是SUMMARY，则 AppStore 已完成为所有符合条件的订阅者延长续订日期。

16. REVOKE指示用户有权通过“家人共享”进行应用内购买的通知类型不再可通过共享进行。当购买者对其购买禁用“家庭共享”、购买者（或家庭成员）离开家庭群组或购买者收到退款时，AppStore 会发送此通知。您的应用程序也会收到呼叫。家庭共享适用于非消耗性应用内购买和自动续订订阅。有关家庭共享的更多信息，请参阅在应用程序中支持家庭共享。
```

signedRenewalInfo
```json
{
  "originalTransactionId": "xxx", //原始交易id
  "autoRenewProductId": "com.xxx.xx.xx", //在下一个计费周期续订的产品的产品标识符
  "productId": "com.xx.xxx.xx.xx", //应用内购买的产品标识符。
  "autoRenewStatus": 1, //自动续订订阅的续订状态
  "signedDate": *************, //App Store 签署 JSON Web 签名 (JWS) 数据的 UNIX 时间（以毫秒为单位）
  "environment": "Sandbox", //环境
  "recentSubscriptionStartDate": *************, //最近自动续订订阅开始日期
  "renewalDate": *************, //下次续约日期
  "offerType": 1 //代表促销优惠类型的值。
}
```

signedTransactionInfo
```json
{
    "appAccountToken":"userId", //自定义uuid 和回调关联起来
    "transactionId": "xxx", //交易的唯一标识符。
    "originalTransactionId": "xxx", //purchas的原始交易标识符
    "webOrderLineItemId": "xxx", // 跨设备的订阅购买事件的唯一标识符
    "bundleId": "com.xx.xxx", //bundleId
    "productId": "com.xx.xx.xx.xxx", //sku
    "subscriptionGroupIdentifier": "xxx", //订阅所属的订阅组的标识符
    "purchaseDate": *************, //购买时间戳
    "originalPurchaseDate": *************, //原始购买时间戳
    "expiresDate": *************, //订阅过期或续订的 UNIX 时间（以毫秒为单位）
    "quantity": 1, //用户购买的消耗品的数量。
    "type": "Auto-Renewable Subscription", //应用内购买的产品类型
    "inAppOwnershipType": "PURCHASED",
    "signedDate": *************, //签名时间
    "environment": "Sandbox", //环境
    "transactionReason": "PURCHASE",
    "storefront": "USA",
    "storefrontId": "143441",
    "price": 9990, //价格 $9.99
    "currency": "USD", //货币 (US dollar)
    "offerType":1, //代表促销优惠类型的值。
    "offerDiscountType":, //订阅优惠使用的付款模式，例如免费试用、现收现付或预付
}
```
