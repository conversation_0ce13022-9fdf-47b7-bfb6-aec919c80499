# 海外-V3.5.0 3D地图和订阅技术方案

# 1、概述

### 1.1、术语

（目的：将新出现的业务专业术语，在这里做解释，统一认知, 工具：表格等）

### 1.2、需求背景

（目的：将场景描述清楚，知道要解决什么问题）

### 1.3、本期目标

（目的：说明清楚本期要做的产品边界、要拿到的结果）

# 2、业务实现设计

整体“支付-会员-权益”的模块设计

![支付-会员-权益业务结构.drawio.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/ac3eb625-a955-4706-ba75-ea9cbb6f4af1.png)

## 2.1、后台权益管理

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/7004fa04-143d-41dd-bd31-944c6f50659d.png)![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/ca466664-60bd-4351-b091-8d217363566e.png)

### 2.1.1、修改点

1、权益组（产品定义上的权益类型）通过insert脚本直接创建

2、新增创建权益接口

3、新增修改权益接口（仅限上下线和排序字段的修改）

4、新增权益分页查询接口

**5、本次迭代不涉及编辑日志相关逻辑**

### 2.1.2、数据库

权益和权益组

```text/x-sql
CREATE TABLE `benefit_group` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `name` varchar(64) DEFAULT NULL COMMENT '组名',
  `code` varchar(64) DEFAULT NULL COMMENT '组编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='权益组表';

CREATE TABLE `benefit` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `name` varchar(64) DEFAULT NULL COMMENT '权益名',
  `i18n_code` varchar(64) DEFAULT NULL COMMENT '权益国际化code',
  `level` int DEFAULT '10' COMMENT '权益等级',
  `cycle_type` int NOT NULL COMMENT '权益周期类型：1-月，2-季，3-年，4-日，5-周',
  `cycle_count` int NOT NULL COMMENT '权益周期数量',
  `benefit_count` int NOT NULL COMMENT '权益数量',
  `sort` int DEFAULT '1' COMMENT '排序，越大越靠前',
  `status` int DEFAULT '0' COMMENT '状态：0-禁用，1-启用',
  `benefit_group_name` varchar(64) DEFAULT NULL COMMENT '组名',
  `benefit_group_code` varchar(64) DEFAULT NULL COMMENT '组编号',
  `benefit_group_id` bigint DEFAULT NULL COMMENT '组id'
  `create_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  `update_id` bigint DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='权益表';
```

用户权益和用户权益流水

```text/x-sql
CREATE TABLE `user_benefit` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `benefit_group_code` varchar(64) DEFAULT NULL COMMENT '权益组编号',
  `benefit_id` bigint NOT NULL DEFAULT '0' COMMENT '权益id',
  `benefit_name` varchar(64) DEFAULT NULL COMMENT '权益名',
  `benefit_i18n_code` varchar(64) DEFAULT NULL COMMENT '权益国际化code',
  `remain` int NOT NULL COMMENT '权益剩余数量',
  `total` int NOT NULL COMMENT '权益总数量',
  `last_refresh_time` datetime DEFAULT NULL COMMENT '最后一次次刷新时间',
  `last_refresh_timestamp` bigint DEFAULT NULL COMMENT '最后一次刷新时间戳',
  `next_refresh_time` datetime DEFAULT NULL COMMENT '下次刷新时间',
  `next_refresh_timestamp` bigint DEFAULT NULL COMMENT '下次刷新时间戳',
  `status` int DEFAULT '0' COMMENT '状态：0-失效，1-启用',
  `create_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  `update_id` bigint DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户权益表';



CREATE TABLE `user_benefit_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `type` int DEFAULT '1' COMMENT '产生流水的类型',
  `operation` int DEFAULT '1' COMMENT '操作：1-增加，2-消耗',
  `user_benefit_id` bigint NOT NULL DEFAULT '0' COMMENT '用户在用权益id',
  `benefit_group_code` varchar(64) DEFAULT NULL COMMENT '权益组编号',
  `benefit_id` bigint NOT NULL DEFAULT '0' COMMENT '权益id',
  `benefit_name` varchar(64) DEFAULT NULL COMMENT '权益名',
  `count` int NOT NULL COMMENT '变动数量',
  `remain` int NOT NULL COMMENT '变动后剩余数量',
  `terminal` tinyint DEFAULT '0' COMMENT '终端：0-未知，1-Android，2-IOS',
  `biz_id` varchar(64) DEFAULT NULL COMMENT '业务来源id，如订单id、兑换code',
  `create_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  `update_id` bigint DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户权益流水表';
```

### 2.1.3、涉及接口

1、创建权益

```text/x-sql
POST /admin/benefit
```

入参

返回值

```text/x-sql
{
    "status": 200,
    "message": "SUCCESS",
    "data": true
}
```

2、修改权益

```text/x-sql
PUT /admin/benefit/{id}
```

入参

返回值

```text/x-sql
{
    "status": 200,
    "message": "SUCCESS",
    "data": true
}
```

3、分页查询

```text/x-sql
GET /admin/benefit/page
```

入参

```text/x-sql
?name=xxx&groupCode=xxx&status=1
```

返回值

```text/x-sql
{
    "status": 200,
    "message": "SUCCESS",
    "data": {
      ...
      ...
      ...
    }
}
```

4、获取下拉权益组

本迭代可不处理

## 2.2、后台会员管理

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/37e1ce71-7e89-4a06-a344-71a514f3f64b.png)![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/41686504-7654-4974-adf9-354d27df59c7.png)

### 2.2.1、修改点

1、新增根据权益组获取所有权益接口（用于下拉选择）

2、新增创建会员接口

3、新增修改会员接口（仅可修改上线下状态）**prd内同时支持关联权益修改，当前版本建议不能修改**

4、新增会员分页查询接口

### 2.2.2、数据库

会员以及会员权益表

```text/x-sql
CREATE TABLE `vip` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `name` varchar(64) DEFAULT NULL COMMENT '名称',
  `level` int DEFAULT '10' COMMENT '会员等级',
  `status` int DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  `update_id` bigint DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会员表';


CREATE TABLE `vip_benefit` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `vip_id` bigint NOT NULL COMMENT '会员主键',
  `benefit_id` bigint NOT NULL COMMENT '权益主键',
  `benefit_group_id` bigint NOT NULL COMMENT '权益组主键',
  `create_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会员权益表';




```

用户会员和用户会员流水表

```text/x-sql
CREATE TABLE `user_vip` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `is_vip` int DEFAULT '0' COMMENT '状态：0-非会员，1-是会员',
  `vip_id` bigint NOT NULL COMMENT '会员id',
  `vip_level` int NOT NULL COMMENT '会员等级',
  `is_subscription` tinyint DEFAULT '0' COMMENT '是否为处于订阅中：0-否，1-是',
  `expire_date` varchar(16) DEFAULT NULL COMMENT '到期日期',
  `expire_timestamp` bigint DEFAULT NULL COMMENT '到期日期时间戳',
  `create_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  `update_id` bigint DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户会员表';



CREATE TABLE `user_vip_flow` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `title` varchar(64) DEFAULT NULL COMMENT '日志标题',
  `type` int DEFAULT '1' COMMENT '产生流水的类型：1-购买，202-会员到期',
  `operation` int DEFAULT '1' COMMENT '操作：1-开通，2-关闭',
  `operation_time` datetime DEFAULT NULL COMMENT '开通/关闭时间',
  `operation_timestamp` bigint DEFAULT NULL COMMENT '开通/关闭时间戳',
  `days` int NOT NULL DEFAULT '0' COMMENT '开通天数',
  `expire_date` date DEFAULT NULL COMMENT '本次开通后的过期日期',
  `expire_timestamp` bigint DEFAULT NULL COMMENT '本次开通后的过期时间戳',
  `terminal` tinyint DEFAULT '0' COMMENT '终端：0-未知，1-Android，2-IOS',
  `biz_id` varchar(64) DEFAULT NULL COMMENT '业务来源id，如订单id、兑换code',
  `create_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户会员流水表';
```

### 2.2.3、涉及接口

1、根据权益组code获取所有权益

```text/x-sql
GET /admin/benefit/list
```

入参

```text/x-sql
?groupCode=3D_MAP_USAGE
```

返回值

2、创建会员

```text/x-sql
POST /admin/vip
```

入参

返回值

```text/x-sql
{
    "status": 200,
    "message": "SUCCESS",
    "data": true
}
```

3、修改会员

```text/x-sql
PUT /admin/vip/{id}
```

入参

返回值

4、会员分页查询

```text/x-sql
GET /admin/vip/page
```

入参

```text/x-sql
?name=xxx
```

返回值

## 2.3、后台商品管理

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/34147a23-6e9b-4b86-9f94-9fa8b981ca9a.png)![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/8b5b522e-ae6e-41e9-9949-4b8f1cf0e427.png)

### 2.3.1、修改点

1、新增获取所有在用会员列表接口（用于下拉选择）

2、新增创建商品接口

3、新增修改商品接口（禁支持排序和上下线）

4、新增分页查询商品接口

**5、本次迭代不涉及优惠和优惠历史记录相关逻辑**

### 2.3.2、数据库

```text/x-sql
CREATE TABLE `trade_product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `name` varchar(64) DEFAULT NULL COMMENT '名称',
  `type` int DEFAULT '1' COMMENT '商品类型：1-会员',
  `is_subscription` tinyint DEFAULT '0' COMMENT '是否为订阅商品：0-否，1-是',
  `price` decimal(10,2) DEFAULT NULL COMMENT '实际价格',
  `origin_price` decimal(10,2) DEFAULT NULL COMMENT '原价价格',
  `currency` varchar(16) DEFAULT NULL COMMENT '币种',
  `sort` int DEFAULT '1' COMMENT '排序，越大越靠前',
  `terminal` int DEFAULT '1' COMMENT '终端：1-Android，2-IOS',
  `out_product_code` varchar(64) DEFAULT NULL COMMENT '第三方支付平台商品编码',
  `is_hide` tinyint DEFAULT '0' COMMENT '是否仅白名单用户可见：0-否，1-是',
  `status` int DEFAULT '0' COMMENT '状态：0-禁用，1-启用',
  `vip_id` bigint NOT NULL COMMENT '会员id',
  `vip_level` int NOT NULL COMMENT '会员等级',
  `vip_cycle_type` int NOT NULL COMMENT '会员周期类型：1-月，2-季，3-年，4-日，5-周',
  `vip_cycle_count` int NOT NULL COMMENT '会员周期数量',
  `create_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  `update_id` bigint DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';
```

订单表

```text/x-sql
CREATE TABLE `trade_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `order_no` varchar(64) DEFAULT NULL COMMENT '订单编号',
  `out_trade_no` varchar(64) DEFAULT NULL COMMENT '外部交易号',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `product_id` bigint DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(64) DEFAULT NULL COMMENT '名称',
  `product_type` int DEFAULT '1' COMMENT '商品类型：1-会员',
  `user_subscription_id` bigint DEFAULT NULL COMMENT '订阅ID',
  `currency` varchar(16) DEFAULT NULL COMMENT '币种',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '订单金额',
  `payment_provider` int DEFAULT NULL COMMENT '三方支付：3-苹果内购，10-paypal',
  `paid_time` datetime DEFAULT NULL COMMENT '支付时间',
  `paid_timestamp` bigint DEFAULT NULL COMMENT '支付时间戳',
  `status` int DEFAULT '1' COMMENT '订单状态：1-待支付，2-交易完成，3-交易失败',
  `create_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  `update_id` bigint DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';
```

订阅和订阅日志表

```text/x-sql
CREATE TABLE `user_subscription` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `product_id` bigint DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(64) DEFAULT NULL COMMENT '名称',
  `payment_provider` int DEFAULT NULL COMMENT '三方支付：3-苹果内购，10-paypal',
  `out_subscription_id` varchar(64) DEFAULT NULL COMMENT '第三方支付平台订阅id',
  `first_cycle_amount` decimal(10,2) DEFAULT NULL COMMENT '订阅第一个周期金额',
  `next_cycle_amount` decimal(10,2) DEFAULT NULL COMMENT '订阅下一个周期金额',
  `next_paid_date` varchar(16) DEFAULT NULL COMMENT '下一次支付日期',
  `next_paid_timestamp` bigint DEFAULT NULL COMMENT '下一次支付日期时间戳',
  `sign_timestamp` bigint DEFAULT NULL COMMENT '签约时间',
  `cancel_timestamp` bigint DEFAULT NULL COMMENT '解约时间',
  `status` int DEFAULT '1' COMMENT '订阅状态：1-签约，0-解约',
  `create_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  `update_id` bigint DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_timestamp` bigint DEFAULT NULL COMMENT '修改时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户订阅表';




CREATE TABLE `user_subscription_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '开发主键',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `product_id` bigint DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(64) DEFAULT NULL COMMENT '名称',
  `payment_provider` int DEFAULT NULL COMMENT '三方支付：3-苹果内购，10-paypal',
  `user_subscription_id` bigint NOT NULL DEFAULT '0' COMMENT '开发主键',
  `operation` int DEFAULT '1' COMMENT '订阅操作：1-签约，2-续约，3-解约',
  `next_paid_date` varchar(16) DEFAULT NULL COMMENT '下一次支付日期',
  `next_paid_timestamp` bigint DEFAULT NULL COMMENT '下一次支付日期时间戳',
  `create_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_timestamp` bigint DEFAULT NULL COMMENT '创建时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户订阅日志表';
```

### 2.3.3、涉及接口

1、获取所有在用会员列表

```text/x-sql
GET /admin/vip/list
```

入参

```text/x-sql
?status=1
```

返回值

2、创建商品

```text/x-sql
POST /admin/product
```

入参

返回值

3、修改商品

```text/x-sql
PUT /admin/product/{id}
```

入参

返回值

4、分页查询

```text/x-sql
GET /admin/product/page
```

入参

返回值

## 2.4、后台地图管理

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/5a9df8e2-2173-4c5b-80ec-56f6016000e9.png)![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/86e3c3ce-e7c0-48c1-a957-5a9557285dec.png)

### 2.4.1、修改点

1、老接口创建、修改增加是否支持3D、3D静态封面、3D动态封面

2、老接口分页查询增加是否3D，返回值增加新增的所有字段

### 2.4.2、数据库

```text/x-sql
ALTER TABLE `sport_race_map` ADD COLUMN `3d_supported` tinyint DEFAULT '0' COMMENT '是否支持3d地图：0-否，1-是' AFTER `status`;
ALTER TABLE `sport_race_map` ADD COLUMN `3d_static_cover` varchar(255) DEFAULT NULL COMMENT '3d静态封面' AFTER `cover`;
ALTER TABLE `sport_race_map` ADD COLUMN `3d_dynamic_cover` varchar(255) DEFAULT NULL COMMENT '3d动态封面' AFTER `3d_static_cover`;
```

### 2.4.3、涉及接口

均为老接口

## 2.5、用户注册

### 2.3.1、修改点

1、新用户注册后需要增加一个分配默认权益的流程，搜索出所有权益level=0的权益添加到用户权益池

2、老用户需要有脚本为所有用户添加默认权益到用户权益池

```text/x-sql
insert user_benefit 
(user_id,benefit_group_code,benefit_id,benefit_name,benefit_i18n_code,remain,total,last_refresh_time,last_refresh_timestamp,next_refresh_time,next_refresh_timestamp,status)

select id,'3dm001',14,'每日2次权益','3d_map_2_month',2,2,now(),UNIX_TIMESTAMP(NOW()),'2024-11-21 00:00:00',
1732118400000,1
from user_info where id not in 
(select distinct user_id from user_benefit)
```

## 2.6、App地图展示

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/eLbnj8Ng6x5vOaNY/img/e4dd520e-ff35-4701-820e-82dddf7cd9c6.png)![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/eLbnj8Ng6x5vOaNY/img/dce6445a-7870-45fb-abe4-2fcf90b293a5.png)![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/eLbnj8Ng6x5vOaNY/img/b0854640-5491-46b5-ac6d-380ad1c33a4e.png)

### 2.6.1、修改点

1、引导和训练页耽搁地图查询

获取首页单个地图信息（老接口）

后端查询用户是否有绑定设备：

*   有的话搜索所有绑定中设备的地图，且按照3d优先及序号最高的排序获取所有地图，取符合条件的第一个地图返回，
    
*   没有的话直接按照3d优先及需要最高的排序获取所有地图，取符合条件的第一个地图返回
    

2、列表页增加是否3d筛选项（老接口）

3、详情页地图信息返回内容增加新字段（老接口）

4、详情页新增当前用户地图使用次数的权益信息

5、详情页新增当前用户会员信息

### 2.6.2、涉及接口

1、当前用户对应权益组的权益使用情况

```text/x-sql
GET /user/benefit/list
```

入参

```text/x-sql
?groupCode=3D-MAP_USAGE
```

返回值

2、当前用户会员情况

```text/x-sql
GET /user/user-vip
```

入参

无

返回值

## 2.7、订阅页展示

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/eLbnj8Ng6x5vOaNY/img/0dab54cb-b792-47ac-92bd-76542fd3f42c.png)![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/eLbnj8Ng6x5vOaNY/img/c17ea141-2169-489e-911f-78dfa5b4f6b6.png)![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/eLbnj8Ng6x5vOaNY/img/91555876-a2db-407e-b5b3-e53f3d703407.png)

### 2.7.1、修改点

1、新增可购买商品列表接口

2、新增开通历史接口

3、根据当前用户信息展示商品可购买情况

### 2.7.2、涉及接口

1、商品列表

```text/x-sql
GET /user/product/list
```

入参

返回值

2、会员开通历史

```text/x-sql
GET /user/user-vip-flow/page
```

入参

返回值

## 2.8、订阅购买

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/eLbnj8Ng6x5vOaNY/img/bd8d7e3a-8165-407e-918a-ea6bc8522beb.png)

### 2.8.1、修改点

1、苹果和paypal支付、续订、升级、降级的全流程搭建

2、用户当前在用权益的周期性刷新定时脚本

### 2.8.2、购买流程

#### *******、苹果相关流程

![苹果订阅流程图.drawio.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/0173aac5-6309-4f70-b957-f4f6c119c189.png)

![苹果续订流程.drawio.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/0da4268c-e14e-4424-bd9b-434e99132d7c.png)![苹果升级订阅流程图.drawio.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/4b956232-2ae6-4fb3-a111-f94507bddb5b.png)

![苹果订阅降级流程.drawio.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/4b9566a6-b97a-4345-a911-e3efd01031ce.png)

#### 2.8.2.2、PayPal相关流程

![paypal订阅流程.drawio.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/276e058c-d307-414f-8373-aea14a2cbbca.png)

![paypal续订.drawio.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/6f6be4cc-ed74-4a32-8f80-2a8b3a1e6ceb.png)

![paypal升级订阅流程.drawio.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/91126fa5-1bec-4f9c-bdbc-0ef99604c869.png)

#### 2.8.2.3、 PingPong 相关流程

**发起订阅**

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/Rbv32PXNjTvY9mWO/90c2312c9293423fa1dcd0cd94a4e4c94013.png)

**支付续约**

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/Rbv32PXNjTvY9mWO/52f3ab61ed6d4f3896071af25dc37eed4013.png)

**支付单完结定时任务**

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/Rbv32PXNjTvY9mWO/31f1cf8bfee242eaa2e558026132d7ab4013.png)

### 2.8.3、涉及接口

1、创建订单

```text/x-sql
POST /user/order
```

入参

```text/x-sql
{
    "productId": "1"
}
```

返回值

```text/x-sql
{
    "status": 200,
    "message": "SUCCESS",
    "data": "79bc2b35-a5bc-43ef-8aad-ab9002be3407" // 订单编号
}
```

2、获取跳转链接

3、支付完成接口

4、回调处理接口

## 2.9、地图模式中

### 2.9.1、修改点

1、后端增加训练报告预创建时消耗一次地图使用次数的逻辑

2、其他前端改造待补充

## 2.10、时区问题

后期涉及到用户续费、到期等各种涉及到日期信息的逻辑处理，如何需要考虑用户所在时区，将会出现各种问题，所以这次将会根据所在服定一个标准时间，美国按照纽约时间，日本按照东京时间，相关涉及业务或需要处理的地方：

*   用户今日目标统计的“今日”、“本周”这个时间界定（目标有今日目标表和本周目标表）
    
*   用户日历里面“每一天”的时间界定
    
*   运动数据统计里面搜索运动数据的时间条件
    
*   开屏页的时间
    
*   挑战模式里面的在线体育（实时赛），里面现在前端显示服务器时间和本地时间，可以和产品商量以后只剩服务器时间还是前端依然要本地事件
    
*   地图模式和实景挑战赛的每月刷新定时任务时间点
    
*   训练报告列表页和详情页中关于运动时间的展示
    
*   其他使用msql语句失去转化的和java代码调用时区转化工具类的地方
    
*   数据库和代码实例需要按照对应服的时区进行重启，表数据需要脚本修改历史所有的时间
    

*   管理后台：推荐位列表查询、推荐位详情、添加、修改
    
*   运动结果结算（补偿结算、正常结算）
    
*   实时赛历史排名信息
    

### 2.10.1、数据库

```mysql
#美国# merit

## merit_device

UPDATE device SET update_time = DATE_SUB(update_time, INTERVAL 5 HOUR), migrate_time = DATE_SUB(migrate_time, INTERVAL 5 HOUR), create_time = DATE_SUB(create_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE device_bind_flow SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE device_product SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE device_product_model SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE device_user_rel SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), migrate_time = DATE_SUB(migrate_time, INTERVAL 5 HOUR), last_connect_time = DATE_SUB(last_connect_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE log_firmware SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE product_model_control_mapping SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE product_model_firmware SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';


## merit_misc

UPDATE advert SET end_time = DATE_SUB(end_time, INTERVAL 5 HOUR), begin_time = DATE_SUB(begin_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR), create_time = DATE_SUB(create_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE advert_position_info SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE advert_product_rel SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE advert_tag_rel SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE app_version SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE area SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE category SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE category_group SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE i18n_data SET update_time = DATE_SUB(update_time, INTERVAL 5 HOUR), issue_time = DATE_SUB(issue_time, INTERVAL 5 HOUR), create_time = DATE_SUB(create_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE log_email SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE log_sms SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE misc_msg_getui_audience SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE misc_msg_record SET send_time = DATE_SUB(send_time, INTERVAL 5 HOUR), create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE misc_msg_record_detail SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR), read_time = DATE_SUB(read_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE misc_msg_template SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE push_token SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE tag SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';
UPDATE tag_category SET create_time = DATE_SUB(create_time, INTERVAL 5 HOUR), update_time = DATE_SUB(update_time, INTERVAL 5 HOUR) WHERE create_time < '2024-XX-XX 00:00:00';

```

## 2.11、UI优化

### 2.11.1、地图模式细节修改

|  模块  |  图示  |  说明  |
| --- | --- | --- |
|  地图模式训练板块  |  ![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/eLbnj8Ng6x5vOaNY/img/4b7e7088-d4ae-4d12-a34b-5c9d6cc95597.png)![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/eLbnj8Ng6x5vOaNY/img/4f8b3a88-d83c-4ee4-9998-113e43a238e6.png)  |  *   训练页： 1 尺寸调整：343\*256px      *   列表页： 2 尺寸调整：168\*180px       |
|  后台截图功能  |   |  固定比例 3:4 $\color{#0089FF}{@曾志银}$   |

## 2.12、错误码

|  **错误码值**  |  **中文含义**  |  **英**  |  **日**  |
| --- | --- | --- | --- |
|  PRODUCT\_LEVEL\_HIGHER  |  当前 vip 等级大于等于订阅级别  |  The current VIP level is greater than or equal to the subscription level.  |  現在のVIPレベルは、サブスクリプションレベル以上です。  |
|  PRODUCT\_TERMINAL\_CROSS  |  订阅跨端，需要用户先把原来订阅手动取消  |  For cross-platform subscription, the user needs to manually cancel the original subscription first.  |  サブスクリプションを異なるプラットフォームで利用するには、ユーザーが元のサブスクリプションを手動でキャンセルする必要があります。  |
|  BENEFIT\_NOT\_ENOUGH  |  权益次数不足  |  Insufficient number of entitlements.  |  権利回数が不足しています。  |

# 3、其他信息

## 3.1、沙盒账号

待补充

## 3.2、三方支付费用

[paypal手续费说明](https://www.sandbox.paypal.com/C2/webapps/mpp/merchant-fees)

苹果15%

# 4、后端工时评估

后台管理相关：24小时

时区改造：32小时

前台展示与后台脚本改造：34小时

支付相关：85小时

总计：175小时

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/1e573df1-8799-4056-b7f2-6abf8087e0c8.png)

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/3d54409a-ba65-47f3-8410-41a32c5f98d3.png)

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mxPOGy5MzXP2nKa9/img/c6984d09-60d8-4bfb-8edf-dcf3972e3e8d.png)