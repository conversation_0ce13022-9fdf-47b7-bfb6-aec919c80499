{"props": {"pageProps": {"shortenedUrl": "https://go.ted.com/6REu", "action": null, "videoData": {"__typename": "Video", "id": "1114", "slug": "morgan_spurlock_the_greatest_ted_talk_ever_sold", "title": "The greatest TED Talk ever sold", "socialTitle": "The greatest TED Talk ever sold", "presenterDisplayName": "<PERSON>", "internalLanguageCode": "en", "commentsEnabled": false, "commentsLoggedInOnly": false, "recordedOn": "2011-03-02", "curatorApproved": true, "viewedCount": 2622964, "duration": 1151, "publishedAt": "2011-04-06T15:00:00Z", "topics": {"__typename": "TopicConnection", "nodes": [{"__typename": "Topic", "id": "18", "name": "business", "slug": "business"}, {"__typename": "Topic", "id": "83", "name": "consumerism", "slug": "consumerism"}, {"__typename": "Topic", "id": "93", "name": "film", "slug": "film"}, {"__typename": "Topic", "id": "164", "name": "comedy", "slug": "comedy"}, {"__typename": "Topic", "id": "209", "name": "humor", "slug": "humor"}]}, "talkExtras": {"__typename": "TalkExtras", "learnModules": [{"__typename": "LearnModule", "author": "<PERSON>", "blurb": null, "eyebrow": null, "headline": "*The Greatest Movie Ever Sold*", "imageUrl": "https://pe.tedcdn.com/images/ted/439c0dadf6d18a339179d81da1024c2664e28028_453x646.jpg", "linkUrl": "http://www.amazon.com/The-Greatest-Movie-Ever-Sold/dp/B004UXUV98/ref=as_li_tf_tl?ie=UTF8&camp=1789&creative=9325&creativeASIN=0520271440&linkCode=as2&tag=teco06-20", "published": true, "publisher": "Sony Pictures Classic", "status": "APPROVED", "type": "BOOK", "visibleUrl": null, "year": "2011"}], "takeAction": [{"__typename": "TakeActionModule", "blurb": "**Join** the Campaign for a Commercial-Free Childhood and help put an end to child-targeted marketing.", "endAt": null, "eyebrow": null, "linkUrl": "http://www.commercialfreechildhood.org/actions", "published": true, "startAt": null, "status": "APPROVED", "verb": "join", "visibleUrl": "commercialfreechildhood.org"}], "recommendations": [{"__typename": "Recommendation", "blurb": "The filmmaker shares ways to explore the impact of corporate sponsorship on society.", "recLists": [{"__typename": "RecommendationList", "title": "reading list", "description": "", "recItems": [{"__typename": "RecommendationItem", "blurb": "", "eyebrow": null, "headline": "Brandwashed", "isPdf": false, "label": "READ_BOOK", "linkUrl": "http://www.amazon.com/Brandwashed-Tricks-Companies-Manipulate-Persuade/dp/0385531737/ref=as_li_tf_tl?ie=UTF8&camp=1789&creative=9325&creativeASIN=0520271440&linkCode=as2&tag=teco06-20", "note": "<PERSON>\r\nCrown Business, 2011"}, {"__typename": "RecommendationItem", "blurb": "", "eyebrow": null, "headline": "<PERSON><PERSON>", "isPdf": false, "label": "READ_BOOK", "linkUrl": "http://www.amazon.com/Madboy-Beyond-Tales-World-Advertising/dp/1453258175/ref=as_li_tf_tl?ie=UTF8&camp=1789&creative=9325&creativeASIN=0520271440&linkCode=as2&tag=teco06-20", "note": "<PERSON>\r\nOpen Road Media, 2011"}, {"__typename": "RecommendationItem", "blurb": "", "eyebrow": null, "headline": "Easy Riders, Raging Bulls", "isPdf": false, "label": "WATCH", "linkUrl": "http://www.amazon.com/Riders-Raging-<PERSON>-<PERSON>-<PERSON>/dp/B0001MDQ9E/ref=as_li_tf_tl?ie=UTF8&camp=1789&creative=9325&creativeASIN=0520271440&linkCode=as2&tag=teco06-20", "note": "Shout Factory, 2003"}, {"__typename": "RecommendationItem", "blurb": "", "eyebrow": null, "headline": "Adbusters magazine", "isPdf": false, "label": "EXPLORE", "linkUrl": "https://www.adbusters.org/magazine", "note": ""}]}]}]}, "primaryImageSet": [{"__typename": "PhotoSize", "url": "https://talkstar-photos.s3.amazonaws.com/uploads/b4c7a48e-fffe-4aeb-ba5b-36aee9e58cb3/MorganSpurlock_2011-stageshot.jpg", "aspectRatioName": "4x3"}, {"__typename": "PhotoSize", "url": "https://talkstar-photos.s3.amazonaws.com/uploads/f91de0f2-85f9-4cbe-b664-9e5f37ae8de9/MorganSpurlock_2011-1350x675.jpg", "aspectRatioName": "2x1"}, {"__typename": "PhotoSize", "url": "https://talkstar-photos.s3.amazonaws.com/uploads/d2659f17-26a9-45ec-b5a7-9e2e0151d6c1/MorganSpurlock_2011-embed.jpg", "aspectRatioName": "16x9"}], "relatedVideos": [{"__typename": "Video", "slug": "jeff_skoll_my_journey_into_movies_that_matter", "id": "170"}, {"__typename": "Video", "slug": "j_j_abrams_the_mystery_box", "id": "205"}, {"__typename": "Video", "slug": "tim_leberecht_3_ways_to_usefully_lose_control_of_your_brand", "id": "1591"}, {"__typename": "Video", "slug": "franco_sacchi_a_tour_of_nollywood_nigeria_s_booming_film_industry", "id": "403"}, {"__typename": "Video", "slug": "<PERSON><PERSON>_kidron_the_shared_wonder_of_film", "id": "1476"}, {"__typename": "Video", "slug": "martin_ville<PERSON><PERSON>_how_i_made_an_impossible_film", "id": "1760"}], "customContentDetails": {"__typename": "CustomContentDetails", "partnerName": null}, "speakers": {"__typename": "AcmeSpeakerConnection", "nodes": [{"__typename": "AcmeSpeaker", "photoUrl": "https://pe.tedcdn.com/images/ted/83ee9347031ca1fb5b827f27c8f5ef65887788f5_254x191.jpg", "firstname": "<PERSON>", "middlename": "", "lastname": "<PERSON><PERSON><PERSON>", "description": "Filmmaker", "isLive": true, "title": "", "whatOthersSay": "In the U.S., we've given corporations all the powers and freedoms of an individual but with none of the responsibility. Corporations need to be giving back to their communities just as much as they're taking away.", "whoTheyAre": "<PERSON> makes documentary film and TV that is personal, political -- and, above all, deeply empathetic.", "whyListen": "<p>Though it was as high-concept as any reality-TV show, <PERSON>&#39;s 2004 film&nbsp;<a href=\"http://geni.us/supersizeme\" target=\"_blank\"><em>Super Size Me</em></a>&nbsp;was something else entirely: a critique of modern fast-feeding, wrapped in the personal story of a charming, curious host. And &quot;host&quot; can be taken literally: eating only McDonald&#39;s for 30 days straight, <PERSON><PERSON><PERSON> went into a shocking physical and emotional decline, showing via his own body the truth about junk food. After this Oscar-nominated doc came <PERSON><PERSON><PERSON>&#39;s three-seasons-long unscripted TV show, <em>30 Days</em>, based on another lifehack: Send a person to live, for 30 days, inside another worldview. Stories from <em>30 Days</em> are human, engaging, surprising: An anti-immigrant activist warms to a tight-knit family of border-crossers; an outsourced US engineer meets the Indian engineer who holds his old job; a former pro football player spends 30 days navigating the world in a wheelchair.</p><p>In 2008, <PERSON><PERSON><PERSON> released&nbsp;<em><a href=\"http://geni.us/whereintheworld\" target=\"_blank\">Where in the World Is <PERSON><PERSON><PERSON>?</a>,</em>&nbsp;about his months-long trek through Afghanistan, Saudi Arabia, Egypt, Israel, Palestine ... following leads and interviewing people along the way. (In an interview, he guessed he got within 50 miles of Osama before winding up the hunt.) He was also part of a group-filmed version of <em>Freakonomics</em>. He wrote a book about his fast-food odyssey, called&nbsp;<a href=\"http://geni.us/donteatthisbook\" target=\"_blank\"><em>Don&#39;t Eat This Book</em></a>&nbsp;-- while his wife, vegan chef Alex Jamieson, wrote a bestseller about the eight-week cleansing diet she put Spurlock on after he got supersized.</p><p>His latest film,&nbsp;<em><a href=\"http://geni.us/greatestmovieever\" target=\"_blank\">The Greatest Movie Ever Sold</a>,</em>&nbsp;dives into the  mysterious world of brand sponsorship, a major influence on how pop culture is developed and shared. Almost  totally sponsored itself, the film was the first to be sold at Sundance 2011,  and, it&#39;s said, made a profit before it even opened. The film debuts in  US theaters on April 22, 2011.</p><p>&nbsp;</p>", "slug": "morgan_spurlock"}]}, "description": "With humor and persistence, filmmaker <PERSON> dives into the hidden but influential world of brand marketing, on his quest to make a completely sponsored film about sponsorship. (And yes, onstage naming rights for this talk were sponsored too. By whom and for how much? He'll tell you.)", "socialDescription": "With humor and persistence, filmmaker <PERSON> dives into the hidden but influential world of brand marketing, on his quest to make a completely sponsored film about sponsorship. (And yes, onstage naming rights for this talk were sponsored too. By whom and for how much? He'll tell you.)", "partnerName": null, "playerData": "{\"id\":\"1114\",\"mediaIdentifier\":\"consus-pm1328-im2346\",\"mediaProjectVersionIdentifier\":\"consus-pm1328-im2346\",\"duration\":1168,\"languages\":[{\"languageName\":\"Greek\",\"endonym\":\"Ελληνικά\",\"languageCode\":\"el\",\"ianaCode\":\"el\",\"isRtl\":false},{\"languageName\":\"English\",\"endonym\":\"English\",\"languageCode\":\"en\",\"ianaCode\":\"en\",\"isRtl\":false},{\"languageName\":\"Vietnamese\",\"endonym\":\"Tiếng Việt\",\"languageCode\":\"vi\",\"ianaCode\":\"vi\",\"isRtl\":false},{\"languageName\":\"Italian\",\"endonym\":\"Italiano\",\"languageCode\":\"it\",\"ianaCode\":\"it\",\"isRtl\":false},{\"languageName\":\"Arabic\",\"endonym\":\"العربية\",\"languageCode\":\"ar\",\"ianaCode\":\"ar\",\"isRtl\":true},{\"languageName\":\"Portuguese, Brazilian\",\"endonym\":\"Português brasileiro\",\"languageCode\":\"pt-br\",\"ianaCode\":\"pt-BR\",\"isRtl\":false},{\"languageName\":\"Czech\",\"endonym\":\"Čeština\",\"languageCode\":\"cs\",\"ianaCode\":\"cs\",\"isRtl\":false},{\"languageName\":\"Indonesian\",\"endonym\":\"Bahasa Indonesia\",\"languageCode\":\"id\",\"ianaCode\":\"id\",\"isRtl\":false},{\"languageName\":\"Spanish\",\"endonym\":\"Español\",\"languageCode\":\"es\",\"ianaCode\":\"es\",\"isRtl\":false},{\"languageName\":\"Russian\",\"endonym\":\"Русский\",\"languageCode\":\"ru\",\"ianaCode\":\"ru\",\"isRtl\":false},{\"languageName\":\"Dutch\",\"endonym\":\"Nederlands\",\"languageCode\":\"nl\",\"ianaCode\":\"nl\",\"isRtl\":false},{\"languageName\":\"Portuguese\",\"endonym\":\"Português de Portugal\",\"languageCode\":\"pt\",\"ianaCode\":\"pt\",\"isRtl\":false},{\"languageName\":\"Chinese, Traditional\",\"endonym\":\"中文 (繁體)\",\"languageCode\":\"zh-tw\",\"ianaCode\":\"zh-Hant\",\"isRtl\":false},{\"languageName\":\"Turkish\",\"endonym\":\"Türkçe\",\"languageCode\":\"tr\",\"ianaCode\":\"tr\",\"isRtl\":false},{\"languageName\":\"Chinese, Simplified\",\"endonym\":\"中文 (简体)\",\"languageCode\":\"zh-cn\",\"ianaCode\":\"zh-Hans\",\"isRtl\":false},{\"languageName\":\"Lithuanian\",\"endonym\":\"Lietuvių kalba\",\"languageCode\":\"lt\",\"ianaCode\":\"lt\",\"isRtl\":false},{\"languageName\":\"Romanian\",\"endonym\":\"Română\",\"languageCode\":\"ro\",\"ianaCode\":\"ro\",\"isRtl\":false},{\"languageName\":\"Polish\",\"endonym\":\"Polski\",\"languageCode\":\"pl\",\"ianaCode\":\"pl\",\"isRtl\":false},{\"languageName\":\"French\",\"endonym\":\"Français\",\"languageCode\":\"fr\",\"ianaCode\":\"fr\",\"isRtl\":false},{\"languageName\":\"Bulgarian\",\"endonym\":\"български\",\"languageCode\":\"bg\",\"ianaCode\":\"bg\",\"isRtl\":false},{\"languageName\":\"Croatian\",\"endonym\":\"Hrvatski\",\"languageCode\":\"hr\",\"ianaCode\":\"hr\",\"isRtl\":false},{\"languageName\":\"German\",\"endonym\":\"Deutsch\",\"languageCode\":\"de\",\"ianaCode\":\"de\",\"isRtl\":false},{\"languageName\":\"Danish\",\"endonym\":\"Dansk\",\"languageCode\":\"da\",\"ianaCode\":\"da\",\"isRtl\":false},{\"languageName\":\"Persian\",\"endonym\":\"فارسى\",\"languageCode\":\"fa\",\"ianaCode\":\"fa\",\"isRtl\":true},{\"languageName\":\"Hungarian\",\"endonym\":\"Magyar\",\"languageCode\":\"hu\",\"ianaCode\":\"hu\",\"isRtl\":false},{\"languageName\":\"Japanese\",\"endonym\":\"日本語\",\"languageCode\":\"ja\",\"ianaCode\":\"ja\",\"isRtl\":false},{\"languageName\":\"Hebrew\",\"endonym\":\"עברית\",\"languageCode\":\"he\",\"ianaCode\":\"he\",\"isRtl\":true},{\"languageName\":\"Serbian\",\"endonym\":\"Српски, Srpski\",\"languageCode\":\"sr\",\"ianaCode\":\"sr\",\"isRtl\":false},{\"languageName\":\"Korean\",\"endonym\":\"한국어\",\"languageCode\":\"ko\",\"ianaCode\":\"ko\",\"isRtl\":false},{\"languageName\":\"Slovak\",\"endonym\":\"Slovenčina\",\"languageCode\":\"sk\",\"ianaCode\":\"sk\",\"isRtl\":false},{\"languageName\":\"Ukrainian\",\"endonym\":\"Українська\",\"languageCode\":\"uk\",\"ianaCode\":\"uk\",\"isRtl\":false}],\"nativeLanguage\":\"en\",\"isSubtitleRequired\":false,\"resources\":{\"h264\":[{\"bitrate\":1200,\"file\":\"https://py.tedcdn.com/consus/projects/00/13/26/005/products/2011-morgan-spurlock-005-fallback-998cd35f69ea7c5609e9092c662b3215-1200k.mp4\"}],\"hls\":{\"adUrl\":\"https://pubads.g.doubleclick.net/gampad/ads?ciu_szs=300x250%2C512x288%2C120x60%2C320x50%2C6x7%2C6x8\\u0026correlator=%5Bcorrelator%5D\\u0026cust_params=event%3DTED2011%26id%3D1114%26tag%3Dbusiness%2Cconsumerism%2Cfilm%2Chumor%2Ccomedy%26talk%3Dmorgan_spurlock_the_greatest_ted_talk_ever_sold%26year%3D2011\\u0026env=vp\\u0026gdfp_req=1\\u0026impl=s\\u0026iu=%2F5641%2Fmobile%2Fios%2Fweb\\u0026output=xml_vast2\\u0026sz=640x360\\u0026unviewed_position_start=1\\u0026url=%5Breferrer%5D\",\"maiTargeting\":{\"id\":\"1114\",\"talk\":\"morgan_spurlock_the_greatest_ted_talk_ever_sold\",\"tag\":\"business,consumerism,film,humor,comedy\",\"year\":\"2011\",\"event\":\"TED2011\"},\"stream\":\"https://hls.ted.com/project_masters/1328/manifest.m3u8?intro_master_id=2346\",\"metadata\":\"https://hls.ted.com/project_masters/1328/metadata.json?intro_master_id=2346\"}},\"targeting\":{\"id\":\"1114\",\"talk\":\"morgan_spurlock_the_greatest_ted_talk_ever_sold\",\"tag\":\"business,consumerism,film,humor,comedy\",\"year\":\"2011\",\"event\":\"TED2011\"},\"canonical\":\"https://www.ted.com/talks/morgan_spurlock_the_greatest_ted_talk_ever_sold\",\"name\":\"Morgan Spurlock: The greatest TED Talk ever sold\",\"title\":\"The greatest TED Talk ever sold\",\"speaker\":\"Morgan Spurlock\",\"thumb\":\"https://pi.tedcdn.com/r/talkstar-photos.s3.amazonaws.com/uploads/d2659f17-26a9-45ec-b5a7-9e2e0151d6c1/MorganSpurlock_2011-embed.jpg?quality=89\\u0026w=600\",\"slug\":\"morgan_spurlock_the_greatest_ted_talk_ever_sold\",\"event\":\"TED2011\",\"published\":1302102000,\"external\":{\"service\":\"YouTube\",\"code\":\"6c0VtOdibcI\",\"duration\":1169.0,\"start_time\":0.0}}", "videoContext": "TED2011", "audioInternalLanguageCode": "en", "language": "en", "hasTranslations": true, "featured": true, "type": {"__typename": "TypeOfVideo", "id": "1", "name": "TED Stage Talk"}}, "transcriptData": {"translation": {"__typename": "Translation", "id": "21777", "language": {"__typename": "Language", "id": "35", "endonym": "English", "englishName": "English", "internalLanguageCode": "en", "rtl": false}, "reviewer": null, "translator": null, "paragraphs": [{"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "I have spent the past few years", "time": 0}, {"__typename": "<PERSON><PERSON>", "text": "putting myself into situations", "time": 3000}, {"__typename": "<PERSON><PERSON>", "text": "that are usually very difficult", "time": 5000}, {"__typename": "<PERSON><PERSON>", "text": "and at the same time somewhat dangerous.", "time": 7000}, {"__typename": "<PERSON><PERSON>", "text": "I went to prison --", "time": 11000}, {"__typename": "<PERSON><PERSON>", "text": "difficult.", "time": 13000}, {"__typename": "<PERSON><PERSON>", "text": "I worked in a coal mine --", "time": 15000}, {"__typename": "<PERSON><PERSON>", "text": "dangerous.", "time": 18000}, {"__typename": "<PERSON><PERSON>", "text": "I filmed in war zones --", "time": 20000}, {"__typename": "<PERSON><PERSON>", "text": "difficult and dangerous.", "time": 22000}, {"__typename": "<PERSON><PERSON>", "text": "And I spent 30 days eating nothing but this --", "time": 24000}, {"__typename": "<PERSON><PERSON>", "text": "fun in the beginning,", "time": 28000}, {"__typename": "<PERSON><PERSON>", "text": "little difficult in the middle, very dangerous in the end.", "time": 30000}, {"__typename": "<PERSON><PERSON>", "text": "In fact, most of my career,", "time": 34000}, {"__typename": "<PERSON><PERSON>", "text": "I've been immersing myself", "time": 36000}, {"__typename": "<PERSON><PERSON>", "text": "into seemingly horrible situations", "time": 38000}, {"__typename": "<PERSON><PERSON>", "text": "for the whole goal of trying", "time": 41000}, {"__typename": "<PERSON><PERSON>", "text": "to examine societal issues", "time": 43000}, {"__typename": "<PERSON><PERSON>", "text": "in a way that make them engaging, that make them interesting,", "time": 45000}, {"__typename": "<PERSON><PERSON>", "text": "that hopefully break them down in a way", "time": 48000}, {"__typename": "<PERSON><PERSON>", "text": "that make them entertaining and accessible to an audience.", "time": 50000}, {"__typename": "<PERSON><PERSON>", "text": "So when I knew I was coming here", "time": 53000}, {"__typename": "<PERSON><PERSON>", "text": "to do a TED Talk that was going to look at the world of branding and sponsorship,", "time": 55000}, {"__typename": "<PERSON><PERSON>", "text": "I knew I would want to do something a little different.", "time": 58000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "So as some of you may or may not have heard,", "time": 60000}, {"__typename": "<PERSON><PERSON>", "text": "a couple weeks ago, I took out an ad on eBay.", "time": 63000}, {"__typename": "<PERSON><PERSON>", "text": "I sent out some Facebook messages,", "time": 66000}, {"__typename": "<PERSON><PERSON>", "text": "some Twitter messages,", "time": 69000}, {"__typename": "<PERSON><PERSON>", "text": "and I gave people the opportunity to buy the naming rights", "time": 71000}, {"__typename": "<PERSON><PERSON>", "text": "to my 2011 TED Talk.", "time": 73000}, {"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 75000}, {"__typename": "<PERSON><PERSON>", "text": "That's right, some lucky individual, corporation,", "time": 77000}, {"__typename": "<PERSON><PERSON>", "text": "for-profit or non-profit,", "time": 80000}, {"__typename": "<PERSON><PERSON>", "text": "was going to get the once-in-a-lifetime opportunity --", "time": 82000}, {"__typename": "<PERSON><PERSON>", "text": "because I'm sure <PERSON> will never let it happen again --", "time": 84000}, {"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 86000}, {"__typename": "<PERSON><PERSON>", "text": "to buy the naming rights", "time": 88000}, {"__typename": "<PERSON><PERSON>", "text": "to the talk you're watching right now,", "time": 90000}, {"__typename": "<PERSON><PERSON>", "text": "that at the time didn't have a title, didn't really have a lot of content", "time": 92000}, {"__typename": "<PERSON><PERSON>", "text": "and didn't really give much hint", "time": 95000}, {"__typename": "<PERSON><PERSON>", "text": "as to what the subject matter would actually be.", "time": 97000}, {"__typename": "<PERSON><PERSON>", "text": "So what you were getting was this:", "time": 100000}, {"__typename": "<PERSON><PERSON>", "text": "Your name here presents:", "time": 102000}, {"__typename": "<PERSON><PERSON>", "text": "My TED Talk that you have no idea what the subject is", "time": 104000}, {"__typename": "<PERSON><PERSON>", "text": "and, depending on the content, could ultimately blow up in your face,", "time": 107000}, {"__typename": "<PERSON><PERSON>", "text": "especially if I make you or your company look stupid for doing it.", "time": 110000}, {"__typename": "<PERSON><PERSON>", "text": "But that being said,", "time": 113000}, {"__typename": "<PERSON><PERSON>", "text": "it's a very good media opportunity.", "time": 115000}, {"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 117000}, {"__typename": "<PERSON><PERSON>", "text": "You know how many people watch these TED Talks?", "time": 123000}, {"__typename": "<PERSON><PERSON>", "text": "It's a lot.", "time": 126000}, {"__typename": "<PERSON><PERSON>", "text": "That's just a working title, by the way.", "time": 129000}, {"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 131000}, {"__typename": "<PERSON><PERSON>", "text": "So even with that caveat,", "time": 133000}, {"__typename": "<PERSON><PERSON>", "text": "I knew that someone would buy the naming rights.", "time": 136000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Now if you'd have asked me that a year ago,", "time": 138000}, {"__typename": "<PERSON><PERSON>", "text": "I wouldn't have been able to tell you that with any certainty.", "time": 140000}, {"__typename": "<PERSON><PERSON>", "text": "But in the new project that I'm working on, my new film,", "time": 142000}, {"__typename": "<PERSON><PERSON>", "text": "we examine the world of marketing, advertising.", "time": 144000}, {"__typename": "<PERSON><PERSON>", "text": "And as I said earlier,", "time": 147000}, {"__typename": "<PERSON><PERSON>", "text": "I put myself in some pretty horrible situations over the years,", "time": 149000}, {"__typename": "<PERSON><PERSON>", "text": "but nothing could prepare me, nothing could ready me,", "time": 152000}, {"__typename": "<PERSON><PERSON>", "text": "for anything as difficult", "time": 155000}, {"__typename": "<PERSON><PERSON>", "text": "or as dangerous", "time": 158000}, {"__typename": "<PERSON><PERSON>", "text": "as going into the rooms with these guys.", "time": 160000}, {"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 164000}, {"__typename": "<PERSON><PERSON>", "text": "You see, I had this idea for a movie.", "time": 167000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Video) <PERSON>: What I want to do is make a film", "time": 170000}, {"__typename": "<PERSON><PERSON>", "text": "all about product placement, marketing and advertising,", "time": 172000}, {"__typename": "<PERSON><PERSON>", "text": "where the entire film is funded", "time": 175000}, {"__typename": "<PERSON><PERSON>", "text": "by product placement, marketing and advertising.", "time": 177000}, {"__typename": "<PERSON><PERSON>", "text": "So the movie will be called \"The Greatest Movie Ever Sold.\"", "time": 179000}, {"__typename": "<PERSON><PERSON>", "text": "So what happens in \"The Greatest Movie Ever Sold,\"", "time": 182000}, {"__typename": "<PERSON><PERSON>", "text": "is that everything from top to bottom, from start to finish,", "time": 184000}, {"__typename": "<PERSON><PERSON>", "text": "is branded from beginning to end --", "time": 187000}, {"__typename": "<PERSON><PERSON>", "text": "from the above-the-title sponsor that you'll see in the movie,", "time": 189000}, {"__typename": "<PERSON><PERSON>", "text": "which is brand X.", "time": 191000}, {"__typename": "<PERSON><PERSON>", "text": "Now this brand, the Qualcomm Stadium,", "time": 193000}, {"__typename": "<PERSON><PERSON>", "text": "the Staples Center ...", "time": 195000}, {"__typename": "<PERSON><PERSON>", "text": "these people will be married to the film in perpetuity -- forever.", "time": 197000}, {"__typename": "<PERSON><PERSON>", "text": "And so the film explores this whole idea -- (<PERSON>: It's redundant.)", "time": 200000}, {"__typename": "<PERSON><PERSON>", "text": "It's what? (<PERSON><PERSON>: It's redundant.) In perpetuity, forever?", "time": 202000}, {"__typename": "<PERSON><PERSON>", "text": "I'm a redundant person. (<PERSON><PERSON>: I'm just saying.)", "time": 205000}, {"__typename": "<PERSON><PERSON>", "text": "That was more for emphasis.", "time": 207000}, {"__typename": "<PERSON><PERSON>", "text": "It was, \"In perpetuity. Forever.\"", "time": 209000}, {"__typename": "<PERSON><PERSON>", "text": "But not only are we going to have the brand X title sponsor,", "time": 211000}, {"__typename": "<PERSON><PERSON>", "text": "but we're going to make sure we sell out every category we can in the film.", "time": 213000}, {"__typename": "<PERSON><PERSON>", "text": "So maybe we sell a shoe and it becomes the greatest shoe you ever wore ...", "time": 215000}, {"__typename": "<PERSON><PERSON>", "text": "the greatest car you ever drove from \"The Greatest Movie Ever Sold,\"", "time": 218000}, {"__typename": "<PERSON><PERSON>", "text": "the greatest drink you've ever had, courtesy of \"The Greatest Movie Ever Sold.\"", "time": 221000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "<PERSON>: So the idea is,", "time": 224000}, {"__typename": "<PERSON><PERSON>", "text": "beyond just showing that brands are a part of your life,", "time": 226000}, {"__typename": "<PERSON><PERSON>", "text": "but actually get them to finance the film? (MS: Get them to finance the film.)", "time": 228000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: And actually we show the whole process of how does it work.", "time": 231000}, {"__typename": "<PERSON><PERSON>", "text": "The goal of this whole film is transparency.", "time": 233000}, {"__typename": "<PERSON><PERSON>", "text": "You're going to see the whole thing take place in this movie.", "time": 235000}, {"__typename": "<PERSON><PERSON>", "text": "So that's the whole concept,", "time": 237000}, {"__typename": "<PERSON><PERSON>", "text": "the whole film, start to finish.", "time": 239000}, {"__typename": "<PERSON><PERSON>", "text": "And I would love for CEG to help make it happen.", "time": 241000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "<PERSON>: You know it's funny,", "time": 243000}, {"__typename": "<PERSON><PERSON>", "text": "because when I first hear it,", "time": 245000}, {"__typename": "<PERSON><PERSON>", "text": "it is the ultimate respect", "time": 247000}, {"__typename": "<PERSON><PERSON>", "text": "for an audience.", "time": 249000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Guy: I don't know how receptive", "time": 251000}, {"__typename": "<PERSON><PERSON>", "text": "people are going to be to it, though.", "time": 253000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "XK: Do you have a perspective --", "time": 255000}, {"__typename": "<PERSON><PERSON>", "text": "I don't want to use \"angle\" because that has a negative connotation --", "time": 257000}, {"__typename": "<PERSON><PERSON>", "text": "but do you know how this is going to play out? (MS: No idea.)", "time": 259000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "<PERSON>: How much money does it take to do this?", "time": 262000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: 1.5 million. (DC: Okay.)", "time": 265000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "<PERSON>: I think that you're going to have a hard time meeting with them,", "time": 268000}, {"__typename": "<PERSON><PERSON>", "text": "but I think it's certainly worth pursuing", "time": 270000}, {"__typename": "<PERSON><PERSON>", "text": "a couple big, really obvious brands.", "time": 272000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "XK: Who knows, maybe by the time your film comes out,", "time": 275000}, {"__typename": "<PERSON><PERSON>", "text": "we look like a bunch of blithering idiots.", "time": 277000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: What do you think the response is going to be?", "time": 279000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "<PERSON>: The responses mostly will be \"no.\"", "time": 282000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: But is it a tough sell because of the film", "time": 284000}, {"__typename": "<PERSON><PERSON>", "text": "or a tough sell because of me?", "time": 286000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "JK: Both.", "time": 288000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: ... Meaning not so optimistic.", "time": 290000}, {"__typename": "<PERSON><PERSON>", "text": "So, sir, can you help me? I need help.", "time": 293000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "M<PERSON>: I can help you.", "time": 295000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: Okay. (MK: Good.)", "time": 297000}, {"__typename": "<PERSON><PERSON>", "text": "Awesome.", "time": 299000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MK: We've gotta figure out which brands.", "time": 301000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: Yeah. (MK: That's the challenge.)", "time": 303000}, {"__typename": "<PERSON><PERSON>", "text": "When you look at the people you deal with ..", "time": 306000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MK: We've got some places we can go. (MS: Okay.)", "time": 308000}, {"__typename": "<PERSON><PERSON>", "text": "Turn the camera off.", "time": 310000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: I thought \"Turn the camera off\"", "time": 312000}, {"__typename": "<PERSON><PERSON>", "text": "meant, \"Let's have an off-the-record conversation.\"", "time": 314000}, {"__typename": "<PERSON><PERSON>", "text": "Turns out it really means,", "time": 316000}, {"__typename": "<PERSON><PERSON>", "text": "\"We want nothing to do with your movie.\"", "time": 318000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: And just like that, one by one,", "time": 321000}, {"__typename": "<PERSON><PERSON>", "text": "all of these companies suddenly disappeared.", "time": 324000}, {"__typename": "<PERSON><PERSON>", "text": "None of them wanted anything to do with this movie.", "time": 327000}, {"__typename": "<PERSON><PERSON>", "text": "I was amazed.", "time": 329000}, {"__typename": "<PERSON><PERSON>", "text": "They wanted absolutely nothing to do with this project.", "time": 331000}, {"__typename": "<PERSON><PERSON>", "text": "And I was blown away, because I thought the whole concept, the idea of advertising,", "time": 333000}, {"__typename": "<PERSON><PERSON>", "text": "was to get your product out in front of as many people as possible,", "time": 335000}, {"__typename": "<PERSON><PERSON>", "text": "to get as many people to see it as possible.", "time": 338000}, {"__typename": "<PERSON><PERSON>", "text": "Especially in today's world,", "time": 340000}, {"__typename": "<PERSON><PERSON>", "text": "this intersection of new media and old media", "time": 342000}, {"__typename": "<PERSON><PERSON>", "text": "and the fractured media landscape,", "time": 344000}, {"__typename": "<PERSON><PERSON>", "text": "isn't the idea to get", "time": 346000}, {"__typename": "<PERSON><PERSON>", "text": "that new buzz-worthy delivery vehicle", "time": 348000}, {"__typename": "<PERSON><PERSON>", "text": "that's going to get that message to the masses?", "time": 351000}, {"__typename": "<PERSON><PERSON>", "text": "No, that's what I thought.", "time": 353000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "But the problem was, you see,", "time": 356000}, {"__typename": "<PERSON><PERSON>", "text": "my idea had one fatal flaw,", "time": 358000}, {"__typename": "<PERSON><PERSON>", "text": "and that flaw was this.", "time": 361000}, {"__typename": "<PERSON><PERSON>", "text": "Actually no, that was not the flaw whatsoever.", "time": 365000}, {"__typename": "<PERSON><PERSON>", "text": "That wouldn't have been a problem at all.", "time": 367000}, {"__typename": "<PERSON><PERSON>", "text": "This would have been fine.", "time": 369000}, {"__typename": "<PERSON><PERSON>", "text": "But what this image represents was the problem.", "time": 371000}, {"__typename": "<PERSON><PERSON>", "text": "See, when you do a Google image search for transparency,", "time": 373000}, {"__typename": "<PERSON><PERSON>", "text": "this is ---", "time": 375000}, {"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 377000}, {"__typename": "<PERSON><PERSON>", "text": "(Applause)", "time": 379000}, {"__typename": "<PERSON><PERSON>", "text": "This is one of the first images that comes up.", "time": 382000}, {"__typename": "<PERSON><PERSON>", "text": "So I like the way you roll, <PERSON>. No.", "time": 385000}, {"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 388000}, {"__typename": "<PERSON><PERSON>", "text": "This is was the problem: transparency --", "time": 392000}, {"__typename": "<PERSON><PERSON>", "text": "free from pretense or deceit;", "time": 395000}, {"__typename": "<PERSON><PERSON>", "text": "easily detected or seen through;", "time": 397000}, {"__typename": "<PERSON><PERSON>", "text": "readily understood;", "time": 399000}, {"__typename": "<PERSON><PERSON>", "text": "characterized by visibility or accessibility of information,", "time": 401000}, {"__typename": "<PERSON><PERSON>", "text": "especially concerning business practices --", "time": 404000}, {"__typename": "<PERSON><PERSON>", "text": "that last line being probably the biggest problem.", "time": 406000}, {"__typename": "<PERSON><PERSON>", "text": "You see, we hear a lot about transparency these days.", "time": 409000}, {"__typename": "<PERSON><PERSON>", "text": "Our politicians say it, our president says it,", "time": 412000}, {"__typename": "<PERSON><PERSON>", "text": "even our CEO's say it.", "time": 414000}, {"__typename": "<PERSON><PERSON>", "text": "But suddenly when it comes down to becoming a reality,", "time": 416000}, {"__typename": "<PERSON><PERSON>", "text": "something suddenly changes.", "time": 418000}, {"__typename": "<PERSON><PERSON>", "text": "But why? Well, transparency is scary --", "time": 420000}, {"__typename": "<PERSON><PERSON>", "text": "(Roar)", "time": 423000}, {"__typename": "<PERSON><PERSON>", "text": "like that odd, still-screaming bear.", "time": 425000}, {"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 428000}, {"__typename": "<PERSON><PERSON>", "text": "It's unpredictable --", "time": 430000}, {"__typename": "<PERSON><PERSON>", "text": "(Music)", "time": 432000}, {"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 434000}, {"__typename": "<PERSON><PERSON>", "text": "like this odd country road.", "time": 436000}, {"__typename": "<PERSON><PERSON>", "text": "And it's also very risky.", "time": 439000}, {"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 443000}, {"__typename": "<PERSON><PERSON>", "text": "What else is risky?", "time": 446000}, {"__typename": "<PERSON><PERSON>", "text": "Eating an entire bowl of Cool Whip.", "time": 448000}, {"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 451000}, {"__typename": "<PERSON><PERSON>", "text": "That's very risky.", "time": 456000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Now when I started talking to companies", "time": 460000}, {"__typename": "<PERSON><PERSON>", "text": "and telling them that we wanted to tell this story,", "time": 462000}, {"__typename": "<PERSON><PERSON>", "text": "and they said, \"No, we want you to tell a story.", "time": 464000}, {"__typename": "<PERSON><PERSON>", "text": "We want you to tell a story,", "time": 466000}, {"__typename": "<PERSON><PERSON>", "text": "but we just want to tell our story.\"", "time": 468000}, {"__typename": "<PERSON><PERSON>", "text": "See, when I was a kid", "time": 471000}, {"__typename": "<PERSON><PERSON>", "text": "and my father would catch me in some sort of a lie --", "time": 473000}, {"__typename": "<PERSON><PERSON>", "text": "and there he is giving me the look he often gave me --", "time": 475000}, {"__typename": "<PERSON><PERSON>", "text": "he would say, \"<PERSON>, there's three sides to every story.", "time": 478000}, {"__typename": "<PERSON><PERSON>", "text": "There's your story,", "time": 482000}, {"__typename": "<PERSON><PERSON>", "text": "there's my story", "time": 485000}, {"__typename": "<PERSON><PERSON>", "text": "and there's the real story.\"", "time": 487000}, {"__typename": "<PERSON><PERSON>", "text": "Now you see, with this film, we wanted to tell the real story.", "time": 489000}, {"__typename": "<PERSON><PERSON>", "text": "But with only one company, one agency willing to help me --", "time": 492000}, {"__typename": "<PERSON><PERSON>", "text": "and that's only because I knew <PERSON> and <PERSON> for years --", "time": 494000}, {"__typename": "<PERSON><PERSON>", "text": "I realized that I would have to go on my own,", "time": 498000}, {"__typename": "<PERSON><PERSON>", "text": "I'd have to cut out the middleman", "time": 500000}, {"__typename": "<PERSON><PERSON>", "text": "and go to the companies myself with all of my team.", "time": 502000}, {"__typename": "<PERSON><PERSON>", "text": "So what you suddenly started to realize --", "time": 505000}, {"__typename": "<PERSON><PERSON>", "text": "or what I started to realize --", "time": 507000}, {"__typename": "<PERSON><PERSON>", "text": "is that when you started having conversations with these companies,", "time": 509000}, {"__typename": "<PERSON><PERSON>", "text": "the idea of understanding your brand is a universal problem.", "time": 511000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Video) MS: I have friends who make great big, giant Hollywood films,", "time": 514000}, {"__typename": "<PERSON><PERSON>", "text": "and I have friends who make little independent films like I make.", "time": 516000}, {"__typename": "<PERSON><PERSON>", "text": "And the friends of mine who make big, giant Hollywood movies", "time": 519000}, {"__typename": "<PERSON><PERSON>", "text": "say the reason their films are so successful", "time": 521000}, {"__typename": "<PERSON><PERSON>", "text": "is because of the brand partners that they have.", "time": 523000}, {"__typename": "<PERSON><PERSON>", "text": "And then my friends who make small independent films", "time": 525000}, {"__typename": "<PERSON><PERSON>", "text": "say, \"Well, how are we supposed to compete", "time": 527000}, {"__typename": "<PERSON><PERSON>", "text": "with these big, giant Hollywood movies?\"", "time": 529000}, {"__typename": "<PERSON><PERSON>", "text": "And the movie is called", "time": 531000}, {"__typename": "<PERSON><PERSON>", "text": "\"The Greatest Movie Ever Sold.\"", "time": 533000}, {"__typename": "<PERSON><PERSON>", "text": "So how specifically will we see <PERSON> in the film?", "time": 535000}, {"__typename": "<PERSON><PERSON>", "text": "Any time I'm ready to go, any time I open up my medicine cabinet,", "time": 538000}, {"__typename": "<PERSON><PERSON>", "text": "you will see <PERSON> deodorant.", "time": 541000}, {"__typename": "<PERSON><PERSON>", "text": "While anytime I do an interview with someone,", "time": 543000}, {"__typename": "<PERSON><PERSON>", "text": "I can say, \"Are you fresh enough for this interview?", "time": 546000}, {"__typename": "<PERSON><PERSON>", "text": "Are you ready? You look a little nervous.", "time": 549000}, {"__typename": "<PERSON><PERSON>", "text": "I want to help you calm down.", "time": 551000}, {"__typename": "<PERSON><PERSON>", "text": "So maybe you should put some one before the interview.\"", "time": 553000}, {"__typename": "<PERSON><PERSON>", "text": "So we'll offer one of these fabulous scents.", "time": 555000}, {"__typename": "<PERSON><PERSON>", "text": "Whether it's a \"Floral Fusion\" or a \"Paradise Winds,\"", "time": 557000}, {"__typename": "<PERSON><PERSON>", "text": "they'll have their chance.", "time": 559000}, {"__typename": "<PERSON><PERSON>", "text": "We will have them geared for both male or female --", "time": 561000}, {"__typename": "<PERSON><PERSON>", "text": "solid, roll-on or stick, whatever it may be.", "time": 564000}, {"__typename": "<PERSON><PERSON>", "text": "That's the two-cent tour.", "time": 567000}, {"__typename": "<PERSON><PERSON>", "text": "So now I can answer any of your questions", "time": 569000}, {"__typename": "<PERSON><PERSON>", "text": "and give you the five-cent tour.", "time": 571000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "<PERSON>: We are a smaller brand.", "time": 573000}, {"__typename": "<PERSON><PERSON>", "text": "Much like you talked about being a smaller movie,", "time": 575000}, {"__typename": "<PERSON><PERSON>", "text": "we're very much a challenger brand.", "time": 577000}, {"__typename": "<PERSON><PERSON>", "text": "So we don't have the budgets that other brands have.", "time": 579000}, {"__typename": "<PERSON><PERSON>", "text": "So doing things like this -- you know,", "time": 581000}, {"__typename": "<PERSON><PERSON>", "text": "remind people about Ban --", "time": 583000}, {"__typename": "<PERSON><PERSON>", "text": "is kind of why were interested in it.", "time": 585000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: What are the words that you would use to describe <PERSON>?", "time": 587000}, {"__typename": "<PERSON><PERSON>", "text": "Ban is blank.", "time": 589000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "KF: That's a great question.", "time": 592000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 595000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Woman: Superior technology.", "time": 600000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: Technology's not the way you want to describe something", "time": 602000}, {"__typename": "<PERSON><PERSON>", "text": "somebody's putting in their armpit.", "time": 604000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Man: We talk about bold, fresh.", "time": 606000}, {"__typename": "<PERSON><PERSON>", "text": "I think \"fresh\" is a great word that really spins this category into the positive,", "time": 608000}, {"__typename": "<PERSON><PERSON>", "text": "versus \"fights odor and wetness.\"", "time": 611000}, {"__typename": "<PERSON><PERSON>", "text": "It keeps you fresh.", "time": 613000}, {"__typename": "<PERSON><PERSON>", "text": "How do we keep you fresher longer -- better freshness,", "time": 615000}, {"__typename": "<PERSON><PERSON>", "text": "more freshness, three times fresher.", "time": 617000}, {"__typename": "<PERSON><PERSON>", "text": "Things like that that are more of that positive benefit.", "time": 619000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: And that's a multi-million dollar corporation.", "time": 623000}, {"__typename": "<PERSON><PERSON>", "text": "What about me? What about a regular guy?", "time": 626000}, {"__typename": "<PERSON><PERSON>", "text": "I need to go talk to the man on the street,", "time": 628000}, {"__typename": "<PERSON><PERSON>", "text": "the people who are like me, the regular <PERSON><PERSON>.", "time": 630000}, {"__typename": "<PERSON><PERSON>", "text": "They need to tell me about my brand.", "time": 632000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Video) MS: How would you guys describe your brand?", "time": 634000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Man: Um, my brand?", "time": 638000}, {"__typename": "<PERSON><PERSON>", "text": "I don't know.", "time": 641000}, {"__typename": "<PERSON><PERSON>", "text": "I like really nice clothes.", "time": 643000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Woman: 80's revival", "time": 645000}, {"__typename": "<PERSON><PERSON>", "text": "meets skater-punk,", "time": 647000}, {"__typename": "<PERSON><PERSON>", "text": "unless it's laundry day.", "time": 649000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: All right, what is brand Gerry?", "time": 651000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Gerry: Unique. (MS: Unique.)", "time": 653000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Man: I guess what kind of genre, style I am", "time": 655000}, {"__typename": "<PERSON><PERSON>", "text": "would be like dark glamor.", "time": 657000}, {"__typename": "<PERSON><PERSON>", "text": "I like a lot of black colors,", "time": 660000}, {"__typename": "<PERSON><PERSON>", "text": "a lot of grays and stuff like that.", "time": 662000}, {"__typename": "<PERSON><PERSON>", "text": "But usually I have an accessory,", "time": 664000}, {"__typename": "<PERSON><PERSON>", "text": "like sunglasses,", "time": 666000}, {"__typename": "<PERSON><PERSON>", "text": "or I like crystal and things like that too.", "time": 668000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Woman: If Dan were a brand,", "time": 670000}, {"__typename": "<PERSON><PERSON>", "text": "he might be a classic convertible", "time": 672000}, {"__typename": "<PERSON><PERSON>", "text": "Mercedes Benz.", "time": 676000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Man 2: The brand that I am", "time": 678000}, {"__typename": "<PERSON><PERSON>", "text": "is, I would call it casual fly.", "time": 680000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Woman 2: Part hippie, part yogi,", "time": 682000}, {"__typename": "<PERSON><PERSON>", "text": "part Brooklyn girl -- I don't know.", "time": 684000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Man 3: I'm the pet guy.", "time": 686000}, {"__typename": "<PERSON><PERSON>", "text": "I sell pet toys all over the country, all over the world.", "time": 688000}, {"__typename": "<PERSON><PERSON>", "text": "So I guess that's my brand.", "time": 690000}, {"__typename": "<PERSON><PERSON>", "text": "In my warped little industry, that's my brand.", "time": 692000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Man 4: My brand is FedEx because I deliver the goods.", "time": 695000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Man 5: Failed writer-alcoholic brand.", "time": 698000}, {"__typename": "<PERSON><PERSON>", "text": "Is that something?", "time": 700000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Lawyer: I'm a lawyer brand.", "time": 702000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Tom: I'm <PERSON>.", "time": 708000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: Well we can't all be brand Tom, but I do often find myself", "time": 711000}, {"__typename": "<PERSON><PERSON>", "text": "at the intersection of dark glamor and casual fly.", "time": 714000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 717000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "And what I realized is I needed an expert.", "time": 719000}, {"__typename": "<PERSON><PERSON>", "text": "I needed somebody who could get inside my head,", "time": 721000}, {"__typename": "<PERSON><PERSON>", "text": "somebody who could really help me understand", "time": 723000}, {"__typename": "<PERSON><PERSON>", "text": "what they call your \"brand personality.\"", "time": 725000}, {"__typename": "<PERSON><PERSON>", "text": "And so I found a company called Olson Zaltman in Pittsburg.", "time": 727000}, {"__typename": "<PERSON><PERSON>", "text": "They've helped companies like Nestle, Febreze, Hallmark", "time": 729000}, {"__typename": "<PERSON><PERSON>", "text": "discover that brand personality.", "time": 732000}, {"__typename": "<PERSON><PERSON>", "text": "If they could do it for them, surely they could do it for me.", "time": 734000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Video) <PERSON>: You brought your pictures, right?", "time": 737000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: I did. The very first picture", "time": 739000}, {"__typename": "<PERSON><PERSON>", "text": "is a picture of my family.", "time": 741000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "A: So tell me a little bit how it relates to your thoughts and feelings about who you are.", "time": 743000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: These are the people who shape the way I look at the world.", "time": 746000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "A: Tell me about this world.", "time": 748000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: This world? I think your world is the world that you live in --", "time": 750000}, {"__typename": "<PERSON><PERSON>", "text": "like people who are around you, your friends, your family,", "time": 753000}, {"__typename": "<PERSON><PERSON>", "text": "the way you live your life, the job you do.", "time": 756000}, {"__typename": "<PERSON><PERSON>", "text": "All those things stemmed and started from one place,", "time": 758000}, {"__typename": "<PERSON><PERSON>", "text": "and for me they stemmed and started with my family in West Virginia.", "time": 760000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "A: What's the next one you want to talk about?", "time": 763000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: The next one: This was the best day ever.", "time": 765000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "A: How does this relate to your thoughts and feelings about who you are?", "time": 767000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: It's like, who do I want to be?", "time": 769000}, {"__typename": "<PERSON><PERSON>", "text": "I like things that are different.", "time": 771000}, {"__typename": "<PERSON><PERSON>", "text": "I like things that are weird. I like weird things.", "time": 773000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "A: Tell me about the \"why\" phase -- what does that do for us?", "time": 775000}, {"__typename": "<PERSON><PERSON>", "text": "What is the machete? What pupa stage are you in now?", "time": 777000}, {"__typename": "<PERSON><PERSON>", "text": "Why is it important to reboot? What does the red represent?", "time": 779000}, {"__typename": "<PERSON><PERSON>", "text": "Tell me a little bit about that part.", "time": 782000}, {"__typename": "<PERSON><PERSON>", "text": "... A little more about you that is not who you are.", "time": 784000}, {"__typename": "<PERSON><PERSON>", "text": "What are some other metamorphoses that you've had?", "time": 787000}, {"__typename": "<PERSON><PERSON>", "text": "... Doesn't have to be fear. What kind of roller coaster are you on?", "time": 789000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: EEEEEE! (A: Thank you.) No, thank you.", "time": 791000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "A: Thanks for you patience. (MS: Great job.)", "time": 793000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "A: Yeah. (MS: Thanks a lot.) All right.", "time": 795000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: Yeah, I don't know what's going to come of this.", "time": 797000}, {"__typename": "<PERSON><PERSON>", "text": "There was a whole lot of crazy going on in there.", "time": 799000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "<PERSON>: The first thing we saw was this idea", "time": 802000}, {"__typename": "<PERSON><PERSON>", "text": "that you had two distinct, but complementary", "time": 804000}, {"__typename": "<PERSON><PERSON>", "text": "sides to your brand personality --", "time": 806000}, {"__typename": "<PERSON><PERSON>", "text": "the Morgan Spurlock brand is a mindful/play brand.", "time": 809000}, {"__typename": "<PERSON><PERSON>", "text": "Those are juxtaposed very nicely together.", "time": 812000}, {"__typename": "<PERSON><PERSON>", "text": "And I think there's almost a paradox with those.", "time": 814000}, {"__typename": "<PERSON><PERSON>", "text": "And I think some companies", "time": 816000}, {"__typename": "<PERSON><PERSON>", "text": "will just focus on one of their strengths or the other", "time": 818000}, {"__typename": "<PERSON><PERSON>", "text": "instead of focusing on both.", "time": 821000}, {"__typename": "<PERSON><PERSON>", "text": "Most companies tend to -- and it's human nature --", "time": 823000}, {"__typename": "<PERSON><PERSON>", "text": "to avoid things that they're not sure of,", "time": 826000}, {"__typename": "<PERSON><PERSON>", "text": "avoid fear, those elements,", "time": 828000}, {"__typename": "<PERSON><PERSON>", "text": "and you really embrace those,", "time": 830000}, {"__typename": "<PERSON><PERSON>", "text": "and you actually turn them into positives for you, and it's a neat thing to see.", "time": 832000}, {"__typename": "<PERSON><PERSON>", "text": "What other brands are like that?", "time": 835000}, {"__typename": "<PERSON><PERSON>", "text": "The first on here is the classic, Apple.", "time": 837000}, {"__typename": "<PERSON><PERSON>", "text": "And you can see here too, Target, Wii,", "time": 839000}, {"__typename": "<PERSON><PERSON>", "text": "Mini from the Mini Coopers, and JetBlue.", "time": 842000}, {"__typename": "<PERSON><PERSON>", "text": "Now there's playful brands and mindful brands,", "time": 845000}, {"__typename": "<PERSON><PERSON>", "text": "those things that have come and gone,", "time": 847000}, {"__typename": "<PERSON><PERSON>", "text": "but a playful, mindful brand is a pretty powerful thing.", "time": 849000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "MS: A playful, mindful brand. What is your brand?", "time": 852000}, {"__typename": "<PERSON><PERSON>", "text": "If somebody asked you to describe your brand identity, your brand personality,", "time": 854000}, {"__typename": "<PERSON><PERSON>", "text": "what would you be?", "time": 857000}, {"__typename": "<PERSON><PERSON>", "text": "Are you an up attribute? Are you something that gets the blood flowing?", "time": 859000}, {"__typename": "<PERSON><PERSON>", "text": "Or are you more of a down attribute?", "time": 862000}, {"__typename": "<PERSON><PERSON>", "text": "Are you something that's a little more calm, reserved, conservative?", "time": 864000}, {"__typename": "<PERSON><PERSON>", "text": "Up attributes are things like being playful,", "time": 867000}, {"__typename": "<PERSON><PERSON>", "text": "being fresh like the Fresh Prince,", "time": 870000}, {"__typename": "<PERSON><PERSON>", "text": "contemporary, adventurous,", "time": 873000}, {"__typename": "<PERSON><PERSON>", "text": "edgy or daring like <PERSON><PERSON><PERSON>,", "time": 875000}, {"__typename": "<PERSON><PERSON>", "text": "nimble or agile, profane, domineering,", "time": 877000}, {"__typename": "<PERSON><PERSON>", "text": "magical or mystical like <PERSON><PERSON><PERSON>.", "time": 880000}, {"__typename": "<PERSON><PERSON>", "text": "Or are you more of a down attribute?", "time": 882000}, {"__typename": "<PERSON><PERSON>", "text": "Are you mindful, sophisticated like 007?", "time": 884000}, {"__typename": "<PERSON><PERSON>", "text": "Are you established, traditional, nurturing, protective,", "time": 886000}, {"__typename": "<PERSON><PERSON>", "text": "empathetic like the Oprah?", "time": 889000}, {"__typename": "<PERSON><PERSON>", "text": "Are you reliable, stable, familiar,", "time": 891000}, {"__typename": "<PERSON><PERSON>", "text": "safe, secure, sacred,", "time": 893000}, {"__typename": "<PERSON><PERSON>", "text": "contemplative or wise", "time": 895000}, {"__typename": "<PERSON><PERSON>", "text": "like the Dalai Lama or <PERSON><PERSON>?", "time": 897000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Over the course of this film,", "time": 899000}, {"__typename": "<PERSON><PERSON>", "text": "we had 500-plus companies", "time": 902000}, {"__typename": "<PERSON><PERSON>", "text": "who were up and down companies", "time": 904000}, {"__typename": "<PERSON><PERSON>", "text": "saying, \"no,\" they didn't want any part of this project.", "time": 906000}, {"__typename": "<PERSON><PERSON>", "text": "They wanted nothing to do with this film, mainly because they would have no control,", "time": 908000}, {"__typename": "<PERSON><PERSON>", "text": "they would have no control over the final product.", "time": 911000}, {"__typename": "<PERSON><PERSON>", "text": "But we did get 17 brand partners", "time": 913000}, {"__typename": "<PERSON><PERSON>", "text": "who were willing to relinquish that control,", "time": 915000}, {"__typename": "<PERSON><PERSON>", "text": "who wanted to be in business", "time": 917000}, {"__typename": "<PERSON><PERSON>", "text": "with someone as mindful and as playful as myself", "time": 919000}, {"__typename": "<PERSON><PERSON>", "text": "and who ultimately empowered us to tell stories", "time": 922000}, {"__typename": "<PERSON><PERSON>", "text": "that normally we wouldn't be able to tell --", "time": 924000}, {"__typename": "<PERSON><PERSON>", "text": "stories that an advertiser would normally never get behind.", "time": 927000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "They enabled us to tell the story about neuromarketing,", "time": 930000}, {"__typename": "<PERSON><PERSON>", "text": "as we got into telling the story in this film", "time": 933000}, {"__typename": "<PERSON><PERSON>", "text": "about how now they're using MRI's", "time": 935000}, {"__typename": "<PERSON><PERSON>", "text": "to target the desire centers of your brain", "time": 937000}, {"__typename": "<PERSON><PERSON>", "text": "for both commercials as well as movie marketing.", "time": 939000}, {"__typename": "<PERSON><PERSON>", "text": "We went to San Paulo where they have banned outdoor advertising.", "time": 942000}, {"__typename": "<PERSON><PERSON>", "text": "In the entire city for the past five years,", "time": 945000}, {"__typename": "<PERSON><PERSON>", "text": "there's no billboards, there's no posters, there's no flyers, nothing.", "time": 947000}, {"__typename": "<PERSON><PERSON>", "text": "(Applause)", "time": 950000}, {"__typename": "<PERSON><PERSON>", "text": "And we went to school districts", "time": 952000}, {"__typename": "<PERSON><PERSON>", "text": "where now companies are making their way", "time": 954000}, {"__typename": "<PERSON><PERSON>", "text": "into cash-strapped schools all across America.", "time": 956000}, {"__typename": "<PERSON><PERSON>", "text": "What's incredible for me is the projects that I've gotten the most feedback out of,", "time": 959000}, {"__typename": "<PERSON><PERSON>", "text": "or I've had the most success in,", "time": 962000}, {"__typename": "<PERSON><PERSON>", "text": "are ones where I've interacted with things directly.", "time": 964000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "And that's what these brands did.", "time": 966000}, {"__typename": "<PERSON><PERSON>", "text": "They cut out the middleman, they cut out their agencies", "time": 968000}, {"__typename": "<PERSON><PERSON>", "text": "and said, \"Maybe these agencies", "time": 970000}, {"__typename": "<PERSON><PERSON>", "text": "don't have my best interest in mind.", "time": 972000}, {"__typename": "<PERSON><PERSON>", "text": "I'm going to deal directly with the artist.", "time": 974000}, {"__typename": "<PERSON><PERSON>", "text": "I'm going to work with him to create something different,", "time": 976000}, {"__typename": "<PERSON><PERSON>", "text": "something that's going to get people thinking,", "time": 978000}, {"__typename": "<PERSON><PERSON>", "text": "that's going to challenge the way we look at the world.\"", "time": 980000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "And how has that been for them? Has it been successful?", "time": 982000}, {"__typename": "<PERSON><PERSON>", "text": "Well, since the film premiered at the Sundance Film Festival, let's take a look.", "time": 984000}, {"__typename": "<PERSON><PERSON>", "text": "According to <PERSON><PERSON>, the movie premiered in January,", "time": 987000}, {"__typename": "<PERSON><PERSON>", "text": "and since then -- and this isn't even the whole thing --", "time": 990000}, {"__typename": "<PERSON><PERSON>", "text": "we've had 900 million media impressions for this film.", "time": 992000}, {"__typename": "<PERSON><PERSON>", "text": "That's literally covering just like a two and a half-week period.", "time": 995000}, {"__typename": "<PERSON><PERSON>", "text": "That's only online -- no print, no TV.", "time": 997000}, {"__typename": "<PERSON><PERSON>", "text": "The film hasn't even been distributed yet.", "time": 999000}, {"__typename": "<PERSON><PERSON>", "text": "It's not even online. It's not even streaming.", "time": 1001000}, {"__typename": "<PERSON><PERSON>", "text": "It's not even been out into other foreign countries yet.", "time": 1003000}, {"__typename": "<PERSON><PERSON>", "text": "So ultimately,", "time": 1006000}, {"__typename": "<PERSON><PERSON>", "text": "this film has already started to gain a lot of momentum.", "time": 1008000}, {"__typename": "<PERSON><PERSON>", "text": "And not bad for a project that almost every ad agency we talked to", "time": 1011000}, {"__typename": "<PERSON><PERSON>", "text": "advised their clients not to take part.", "time": 1014000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "What I always believe", "time": 1016000}, {"__typename": "<PERSON><PERSON>", "text": "is that if you take chances, if you take risks,", "time": 1018000}, {"__typename": "<PERSON><PERSON>", "text": "that in those risks will come opportunity.", "time": 1020000}, {"__typename": "<PERSON><PERSON>", "text": "I believe that when you push people away from that,", "time": 1023000}, {"__typename": "<PERSON><PERSON>", "text": "you're pushing them more towards failure.", "time": 1025000}, {"__typename": "<PERSON><PERSON>", "text": "I believe that when you train your employees to be risk averse,", "time": 1027000}, {"__typename": "<PERSON><PERSON>", "text": "then you're preparing your whole company", "time": 1030000}, {"__typename": "<PERSON><PERSON>", "text": "to be reward challenged.", "time": 1032000}, {"__typename": "<PERSON><PERSON>", "text": "I feel like that what has to happen moving forward", "time": 1034000}, {"__typename": "<PERSON><PERSON>", "text": "is we need to encourage people to take risks.", "time": 1036000}, {"__typename": "<PERSON><PERSON>", "text": "We need to encourage people to not be afraid", "time": 1039000}, {"__typename": "<PERSON><PERSON>", "text": "of opportunities that may scare them.", "time": 1041000}, {"__typename": "<PERSON><PERSON>", "text": "Ultimately, moving forward,", "time": 1043000}, {"__typename": "<PERSON><PERSON>", "text": "I think we have to embrace fear.", "time": 1045000}, {"__typename": "<PERSON><PERSON>", "text": "We've got to put that bear in a cage.", "time": 1047000}, {"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 1049000}, {"__typename": "<PERSON><PERSON>", "text": "Embrace fear. Embrace risk.", "time": 1056000}, {"__typename": "<PERSON><PERSON>", "text": "One big spoonful at a time, we have to embrace risk.", "time": 1059000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "And ultimately, we have to embrace transparency.", "time": 1062000}, {"__typename": "<PERSON><PERSON>", "text": "Today, more than ever,", "time": 1066000}, {"__typename": "<PERSON><PERSON>", "text": "a little honesty is going to go a long way.", "time": 1068000}, {"__typename": "<PERSON><PERSON>", "text": "And that being said, through honesty and transparency,", "time": 1070000}, {"__typename": "<PERSON><PERSON>", "text": "my entire talk, \"Em<PERSON>ce Transparency,\"", "time": 1073000}, {"__typename": "<PERSON><PERSON>", "text": "has been brought to you", "time": 1076000}, {"__typename": "<PERSON><PERSON>", "text": "by my good friends at EMC,", "time": 1078000}, {"__typename": "<PERSON><PERSON>", "text": "who for $7,100", "time": 1081000}, {"__typename": "<PERSON><PERSON>", "text": "bought the naming rights on eBay.", "time": 1083000}, {"__typename": "<PERSON><PERSON>", "text": "(Applause)", "time": 1085000}, {"__typename": "<PERSON><PERSON>", "text": "EMC: Turning big data", "time": 1094000}, {"__typename": "<PERSON><PERSON>", "text": "into big opportunity", "time": 1097000}, {"__typename": "<PERSON><PERSON>", "text": "for organizations all over the world.", "time": 1099000}, {"__typename": "<PERSON><PERSON>", "text": "EMC presents: \"Embrace Transparency.\"", "time": 1101000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Thank you very much, guys.", "time": 1104000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Applause)", "time": 1106000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "June Cohen: <PERSON>, <PERSON>,", "time": 1119000}, {"__typename": "<PERSON><PERSON>", "text": "in the name of transparency,", "time": 1122000}, {"__typename": "<PERSON><PERSON>", "text": "what exactly happened to that $7,100?", "time": 1124000}, {"__typename": "<PERSON><PERSON>", "text": "MS: That is a fantastic question.", "time": 1126000}, {"__typename": "<PERSON><PERSON>", "text": "I have in my pocket a check", "time": 1129000}, {"__typename": "<PERSON><PERSON>", "text": "made out to the parent organization to the TED organization,", "time": 1132000}, {"__typename": "<PERSON><PERSON>", "text": "the Sapling Foundation --", "time": 1134000}, {"__typename": "<PERSON><PERSON>", "text": "a check for $7,100", "time": 1136000}, {"__typename": "<PERSON><PERSON>", "text": "to be applied toward my attendance for next year's TED.", "time": 1138000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 1141000}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Applause)", "time": 1143000}]}]}, "video": {"__typename": "Video", "id": "1114", "talkExtras": {"__typename": "TalkExtras", "footnotes": []}}}, "commentsEnabled": false, "commentsLoggedInOnly": false}, "language": "en", "messages": {}, "responseCode": 200, "__N_SSP": true}, "page": "/talks/[...slug]", "query": {"slug": ["morgan_spurlock_the_greatest_ted_talk_ever_sold"]}, "buildId": "MFpVn2yC-lUjL4RP9B6q-", "runtimeConfig": {"STRIPE_PUBLISHABLE_KEY": "pk_live_2E0Im9ZSYd61pq9NRBXWa445", "RECAPTCHA_KEY": "6Leu7SEbAAAAAJ62yHnNX_CyMiNGTrdhvp9roeEC", "SUPERCAST_TOKEN": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "isFallback": false, "dynamicIds": [35003], "gssp": true, "customServer": true, "appGip": true, "scriptLoader": []}