{"appGip": true, "buildId": "MFpVn2yC-lUjL4RP9B6q-", "customServer": true, "dynamicIds": [35003], "gssp": true, "isFallback": false, "page": "/talks/[...slug]", "props": {"__N_SSP": true, "language": "en", "messages": {}, "pageProps": {"action": null, "commentsEnabled": true, "commentsLoggedInOnly": true, "shortenedUrl": "https://go.ted.com/rQkR6", "transcriptData": {"translation": {"__typename": "Translation", "id": "247699", "language": {"__typename": "Language", "endonym": "English", "englishName": "English", "id": "35", "internalLanguageCode": "en", "rtl": false}, "paragraphs": [{"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Do you remember the first time\nyou heard a really good story?", "time": 531}, {"__typename": "<PERSON><PERSON>", "text": "One of my first times was when I read\n\"Ramona the Pest\" by <PERSON>.", "time": 5235}, {"__typename": "<PERSON><PERSON>", "text": "As a kid, I loved to curl up\nin my favorite chair in our living room", "time": 10941}, {"__typename": "<PERSON><PERSON>", "text": "and transport myself\ninto the misadventures", "time": 14878}, {"__typename": "<PERSON><PERSON>", "text": "of <PERSON><PERSON>.", "time": 17981}, {"__typename": "<PERSON><PERSON>", "text": "Even now, I remember\nhow my heart would race,", "time": 20317}, {"__typename": "<PERSON><PERSON>", "text": "reading about her getting into trouble\nover and over for misbehaving.", "time": 22753}, {"__typename": "<PERSON><PERSON>", "text": "At the same time, as a shy suburban kid", "time": 28058}, {"__typename": "<PERSON><PERSON>", "text": "who often took myself\na little bit too seriously,", "time": 30861}, {"__typename": "<PERSON><PERSON>", "text": "I really admired her fun-loving attitude\nand her carefree spirit.", "time": 33897}, {"__typename": "<PERSON><PERSON>", "text": "I've always loved stories", "time": 39069}, {"__typename": "<PERSON><PERSON>", "text": "because they allow me\nto experience other worlds", "time": 40404}, {"__typename": "<PERSON><PERSON>", "text": "I didn't know anything about,", "time": 43140}, {"__typename": "<PERSON><PERSON>", "text": "yet helped me make sense\nof my own world at the same time.", "time": 45609}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "But stories aren't just for books\nor movies or entertainment.", "time": 50881}, {"__typename": "<PERSON><PERSON>", "text": "They’re one of the most powerful forces\non the planet to connect, persuade", "time": 56320}, {"__typename": "<PERSON><PERSON>", "text": "and influence our mindset,\nbeliefs and behavior.", "time": 61258}, {"__typename": "<PERSON><PERSON>", "text": "And that's why storytelling\nis one of the most powerful", "time": 65395}, {"__typename": "<PERSON><PERSON>", "text": "marketing and leadership tools there is.", "time": 68231}, {"__typename": "<PERSON><PERSON>", "text": "In a world cluttered with forgettable,\nlackluster messages,", "time": 72903}, {"__typename": "<PERSON><PERSON>", "text": "stories make us memorable.", "time": 76807}, {"__typename": "<PERSON><PERSON>", "text": "Not only that, stories create powerful\nconnections between the storyteller", "time": 80043}, {"__typename": "<PERSON><PERSON>", "text": "and the story listener.", "time": 84781}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "<PERSON>, writes,", "time": 87551}, {"__typename": "<PERSON><PERSON>", "text": "<PERSON><PERSON>, professor of psychology\nand neuroscience at Princeton University,", "time": 88952}, {"__typename": "<PERSON><PERSON>", "text": "discovered that as you hear\na story unfold,", "time": 94625}, {"__typename": "<PERSON><PERSON>", "text": "your brainwaves actually\nstart to synchronize", "time": 98161}, {"__typename": "<PERSON><PERSON>", "text": "with those of the storyteller.", "time": 101164}, {"__typename": "<PERSON><PERSON>", "text": "The greater the listener's comprehension,", "time": 104635}, {"__typename": "<PERSON><PERSON>", "text": "the more closely the brainwave\npatterns mirrored each other.", "time": 106870}, {"__typename": "<PERSON><PERSON>", "text": "Kind of brings a whole new\nmeaning to the phrase", "time": 112676}, {"__typename": "<PERSON><PERSON>", "text": "\"get on the same wavelength,\" right?", "time": 114978}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "But here's the best part of all:", "time": 117981}, {"__typename": "<PERSON><PERSON>", "text": "stories are proven to affect behavior.", "time": 120283}, {"__typename": "<PERSON><PERSON>", "text": "Let's take the health care\ncontext, for example.", "time": 124388}, {"__typename": "<PERSON><PERSON>", "text": "<PERSON>, a communication professor\nat the University of Buffalo,", "time": 127124}, {"__typename": "<PERSON><PERSON>", "text": "says that people are more likely\nto make changes to their lifestyle", "time": 130794}, {"__typename": "<PERSON><PERSON>", "text": "and health habits", "time": 134364}, {"__typename": "<PERSON><PERSON>", "text": "if they see a character they relate to\nmaking the same change.", "time": 136099}, {"__typename": "<PERSON><PERSON>", "text": "And so if you've ever wanted\nto get anyone to do anything,", "time": 141104}, {"__typename": "<PERSON><PERSON>", "text": "you would do well to learn\nhow to tell better stories.", "time": 146343}, {"__typename": "<PERSON><PERSON>", "text": "Yet too often we're telling\nthe wrong stories", "time": 151114}, {"__typename": "<PERSON><PERSON>", "text": "or we're not telling them at all.", "time": 154384}, {"__typename": "<PERSON><PERSON>", "text": "And in effect, we're wasting our time\nand our precious dollars on programs,", "time": 155986}, {"__typename": "<PERSON><PERSON>", "text": "campaigns and initiatives\nthat sorely miss the mark.", "time": 160057}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Well, I've been studying stories\nsince I was a kid,", "time": 164027}, {"__typename": "<PERSON><PERSON>", "text": "and I've been bringing them to life\nfor more than a decade", "time": 166430}, {"__typename": "<PERSON><PERSON>", "text": "as a professional corporate marketer.", "time": 169199}, {"__typename": "<PERSON><PERSON>", "text": "And today, I want to show you the easy way", "time": 171101}, {"__typename": "<PERSON><PERSON>", "text": "to create your own stories\nthat connect, persuade, influence", "time": 173236}, {"__typename": "<PERSON><PERSON>", "text": "and break through all the clutter.", "time": 177274}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "So let's start at the beginning.", "time": 180444}, {"__typename": "<PERSON><PERSON>", "text": "Number one, the problem and the pursuit.", "time": 182713}, {"__typename": "<PERSON><PERSON>", "text": "The problem and the pursuit.", "time": 186583}, {"__typename": "<PERSON><PERSON>", "text": "You know, I believe the worst story of all\nis the one that is told too soon.", "time": 189519}, {"__typename": "<PERSON><PERSON>", "text": "And truly, this is a very common mistake\nthat aspiring storytellers make.", "time": 195726}, {"__typename": "<PERSON><PERSON>", "text": "We launch into a story", "time": 200697}, {"__typename": "<PERSON><PERSON>", "text": "and don't know the first thing\nabout who we're talking to.", "time": 202032}, {"__typename": "<PERSON><PERSON>", "text": "Before you're qualified to tell anything,", "time": 206069}, {"__typename": "<PERSON><PERSON>", "text": "you must deeply understand\nyour audience's problem and pursuit.", "time": 208071}, {"__typename": "<PERSON><PERSON>", "text": "Their problem is where they are right now.", "time": 213643}, {"__typename": "<PERSON><PERSON>", "text": "It's the issues that they're facing\nin their current state", "time": 216379}, {"__typename": "<PERSON><PERSON>", "text": "and how they feel about it.", "time": 219116}, {"__typename": "<PERSON><PERSON>", "text": "Their pursuit is where they want to be.", "time": 222018}, {"__typename": "<PERSON><PERSON>", "text": "It's who they want to be,", "time": 224387}, {"__typename": "<PERSON><PERSON>", "text": "and it's how they will feel\nin this future perfect world.", "time": 225756}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Business development expert", "time": 230560}, {"__typename": "<PERSON><PERSON>", "text": "and LinkedIn top sales\ninfluencer, <PERSON>,", "time": 231995}, {"__typename": "<PERSON><PERSON>", "text": "knows a little something\nabout understanding", "time": 235365}, {"__typename": "<PERSON><PERSON>", "text": "her audience's problem and pursuit.", "time": 237501}, {"__typename": "<PERSON><PERSON>", "text": "To create messages\nfor her email marketing,", "time": 241238}, {"__typename": "<PERSON><PERSON>", "text": "her website and her social media content,", "time": 244141}, {"__typename": "<PERSON><PERSON>", "text": "she listens to her clients", "time": 246743}, {"__typename": "<PERSON><PERSON>", "text": "and then begins to extract specific\nelements from what they've said", "time": 249379}, {"__typename": "<PERSON><PERSON>", "text": "and infuses it into her language\nand her visuals.", "time": 252816}, {"__typename": "<PERSON><PERSON>", "text": "The effect", "time": 256686}, {"__typename": "<PERSON><PERSON>", "text": "is a resounding “Girl!", "time": 258622}, {"__typename": "<PERSON><PERSON>", "text": "How did you do that?", "time": 261558}, {"__typename": "<PERSON><PERSON>", "text": "Because every time I see\nsomething from you,", "time": 264127}, {"__typename": "<PERSON><PERSON>", "text": "it feels like you're talking\ndirectly to me.\"", "time": 266263}, {"__typename": "<PERSON><PERSON>", "text": "Now, how did she do it?", "time": 269633}, {"__typename": "<PERSON><PERSON>", "text": "Simply because she's taking\ntime to understand", "time": 271868}, {"__typename": "<PERSON><PERSON>", "text": "her audience's problem and pursuit.", "time": 274037}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Now how did she get that understanding?", "time": 276540}, {"__typename": "<PERSON><PERSON>", "text": "Well, it's because she's living out\na very powerful storytelling principle,", "time": 279976}, {"__typename": "<PERSON><PERSON>", "text": "which is this:", "time": 284714}, {"__typename": "<PERSON><PERSON>", "text": "good story listening", "time": 286883}, {"__typename": "<PERSON><PERSON>", "text": "always comes before a good story telling.", "time": 289619}, {"__typename": "<PERSON><PERSON>", "text": "And so, number one,\nif you want to tell a good story,", "time": 295158}, {"__typename": "<PERSON><PERSON>", "text": "you have to understand\nyour audience's problem and pursuit.", "time": 297661}, {"__typename": "<PERSON><PERSON>", "text": "Number two, you have to be able\nto paint them a picture.", "time": 300463}, {"__typename": "<PERSON><PERSON>", "text": "You have to be able to co-create a reality", "time": 304134}, {"__typename": "<PERSON><PERSON>", "text": "that shows you understand the problem,", "time": 306236}, {"__typename": "<PERSON><PERSON>", "text": "yet hints at a better future.", "time": 308271}, {"__typename": "<PERSON><PERSON>", "text": "To do this, you'll need a specific person,", "time": 311541}, {"__typename": "<PERSON><PERSON>", "text": "a specific challenge,", "time": 315245}, {"__typename": "<PERSON><PERSON>", "text": "specific imagery and specific feelings.", "time": 318215}, {"__typename": "<PERSON><PERSON>", "text": "Did I mention you should be specific?", "time": 323653}, {"__typename": "<PERSON><PERSON>", "text": "Now specific doesn’t mean\nlong and drawn out,", "time": 326957}, {"__typename": "<PERSON><PERSON>", "text": "it just means you want to include\nsome distinguishable characteristics", "time": 329926}, {"__typename": "<PERSON><PERSON>", "text": "that your audience can relate to.", "time": 333997}, {"__typename": "<PERSON><PERSON>", "text": "It's the reason why Nike's ads\nwith LeBron James", "time": 337000}, {"__typename": "<PERSON><PERSON>", "text": "don't include a bunch of close up shots\nof shoes they're selling.", "time": 339636}, {"__typename": "<PERSON><PERSON>", "text": "They don't need to.", "time": 343406}, {"__typename": "<PERSON><PERSON>", "text": "They found the perfect person\nin <PERSON><PERSON><PERSON> James to represent a specific,", "time": 345041}, {"__typename": "<PERSON><PERSON>", "text": "relatable challenge,", "time": 350280}, {"__typename": "<PERSON><PERSON>", "text": "namely overcoming obstacles\nto beat an opponent.", "time": 352115}, {"__typename": "<PERSON><PERSON>", "text": "Then they utilize specific imagery", "time": 355919}, {"__typename": "<PERSON><PERSON>", "text": "to represent a specific\nprogression of feelings,", "time": 358221}, {"__typename": "<PERSON><PERSON>", "text": "like defeat and discouragement,", "time": 361324}, {"__typename": "<PERSON><PERSON>", "text": "to hope and victory and resilience.", "time": 364794}, {"__typename": "<PERSON><PERSON>", "text": "And once you've been gripped\nby a story like that,", "time": 370567}, {"__typename": "<PERSON><PERSON>", "text": "doesn't it almost go without saying", "time": 373069}, {"__typename": "<PERSON><PERSON>", "text": "that you want to wear the same\nsports gear <PERSON><PERSON><PERSON> James does?", "time": 374771}, {"__typename": "<PERSON><PERSON>", "text": "To inspire action and shift beliefs,", "time": 379109}, {"__typename": "<PERSON><PERSON>", "text": "paint a vivid picture for your audience\nthrough words and imagery", "time": 382379}, {"__typename": "<PERSON><PERSON>", "text": "that they can instantly recall\nwhen they think of you.", "time": 386016}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "And so if you're going\nto tell a good story,", "time": 389619}, {"__typename": "<PERSON><PERSON>", "text": "you have to, number one,\nknow your audience's problem and pursuit.", "time": 391688}, {"__typename": "<PERSON><PERSON>", "text": "You must, number two, paint them a picture", "time": 395158}, {"__typename": "<PERSON><PERSON>", "text": "that they can remember and repeat.", "time": 398061}, {"__typename": "<PERSON><PERSON>", "text": "And finally, number three,\nyou must propose.", "time": 400764}, {"__typename": "<PERSON><PERSON>", "text": "You must propose.", "time": 406403}, {"__typename": "<PERSON><PERSON>", "text": "The year was 2004.", "time": 409439}, {"__typename": "<PERSON><PERSON>", "text": "I was a senior at Kent State\nUniversity at the time,", "time": 412042}, {"__typename": "<PERSON><PERSON>", "text": "and my boyfriend was holding\na get-together at his home,", "time": 414511}, {"__typename": "<PERSON><PERSON>", "text": "which I, of course, attended.", "time": 417347}, {"__typename": "<PERSON><PERSON>", "text": "And suddenly,", "time": 419783}, {"__typename": "<PERSON><PERSON>", "text": "when I least expected it,\nthere were roses,", "time": 421117}, {"__typename": "<PERSON><PERSON>", "text": "there was music,", "time": 424321}, {"__typename": "<PERSON><PERSON>", "text": "and right there, in front of all\nmy closest family and friends,", "time": 425522}, {"__typename": "<PERSON><PERSON>", "text": "he got down on one knee,", "time": 428491}, {"__typename": "<PERSON><PERSON>", "text": "he pulled out a ring,", "time": 430560}, {"__typename": "<PERSON><PERSON>", "text": "and in an instant all my little girl\nmarriage proposal fantasies came true.", "time": 431828}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "On the other hand, we have <PERSON>.", "time": 439369}, {"__typename": "<PERSON><PERSON>", "text": "<PERSON>, a couple of years back,", "time": 444541}, {"__typename": "<PERSON><PERSON>", "text": "was at a local outdoor music event.", "time": 446943}, {"__typename": "<PERSON><PERSON>", "text": "And at the end of the event,", "time": 450046}, {"__typename": "<PERSON><PERSON>", "text": "he invited his girlfriend <PERSON>\nto come up on stage with him.", "time": 451414}, {"__typename": "<PERSON><PERSON>", "text": "And so <PERSON> begins\nto wade through the crowd", "time": 456386}, {"__typename": "<PERSON><PERSON>", "text": "and find her way to the stage,", "time": 458588}, {"__typename": "<PERSON><PERSON>", "text": "and she climbs the steps to the stage,", "time": 460323}, {"__typename": "<PERSON><PERSON>", "text": "and she gets up there and she's standing\nup there looking at <PERSON>.", "time": 462192}, {"__typename": "<PERSON><PERSON>", "text": "The only problem was,", "time": 466162}, {"__typename": "<PERSON><PERSON>", "text": "she's looking at him like this.", "time": 468098}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 471701}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Undaunted, <PERSON> begins\nto pour out his heart,", "time": 475372}, {"__typename": "<PERSON><PERSON>", "text": "and he says, \"<PERSON>, baby,", "time": 480110}, {"__typename": "<PERSON><PERSON>", "text": "I love you, girl.\"", "time": 484114}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 485682}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "He says, \"<PERSON>, baby,", "time": 488284}, {"__typename": "<PERSON><PERSON>", "text": "You've been like an angel in my life.\"", "time": 491454}, {"__typename": "<PERSON><PERSON>", "text": "He said, \"<PERSON>, baby,", "time": 495425}, {"__typename": "<PERSON><PERSON>", "text": "the writing is all over the wall.", "time": 497827}, {"__typename": "<PERSON><PERSON>", "text": "I want to spend the rest\nof my life with you.\"", "time": 500330}, {"__typename": "<PERSON><PERSON>", "text": "And he, too, gets down on one knee", "time": 504434}, {"__typename": "<PERSON><PERSON>", "text": "and he, too, pulls out a ring and he says,", "time": 507003}, {"__typename": "<PERSON><PERSON>", "text": "\"<PERSON>, will you marry me?\"", "time": 509272}, {"__typename": "<PERSON><PERSON>", "text": "And right there,", "time": 514644}, {"__typename": "<PERSON><PERSON>", "text": "in front of all those people,", "time": 517047}, {"__typename": "<PERSON><PERSON>", "text": "<PERSON> looked him dead in the eye,\nand she said, “I’m sorry, but no,”", "time": 519582}, {"__typename": "<PERSON><PERSON>", "text": "and walked off the stage.", "time": 525188}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 527023}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "This is the tension we stand in\nas marketers and leaders.", "time": 531327}, {"__typename": "<PERSON><PERSON>", "text": "Because we're constantly making proposals.", "time": 537333}, {"__typename": "<PERSON><PERSON>", "text": "Buy my product, try my service,", "time": 540170}, {"__typename": "<PERSON><PERSON>", "text": "come to my event, join my cause.", "time": 542705}, {"__typename": "<PERSON><PERSON>", "text": "And as much as we want\nto hear that enthusiastic yes,", "time": 547177}, {"__typename": "<PERSON><PERSON>", "text": "we're also pretty afraid\nof the rejection of no.", "time": 552115}, {"__typename": "<PERSON><PERSON>", "text": "I mean, let's be real.", "time": 555218}, {"__typename": "<PERSON><PERSON>", "text": "Especially a public\nrejection like <PERSON>'s,", "time": 556419}, {"__typename": "<PERSON><PERSON>", "text": "where, despite your best efforts,", "time": 560023}, {"__typename": "<PERSON><PERSON>", "text": "your preparation and your good intentions,", "time": 561691}, {"__typename": "<PERSON><PERSON>", "text": "it just fell flat.", "time": 564160}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "But this is the power of story.", "time": 568832}, {"__typename": "<PERSON><PERSON>", "text": "Because stories make proposals\nextremely appealing", "time": 573770}, {"__typename": "<PERSON><PERSON>", "text": "and stack the odds in your favor.", "time": 577006}, {"__typename": "<PERSON><PERSON>", "text": "First, stories help you\nget the timing right.", "time": 580877}, {"__typename": "<PERSON><PERSON>", "text": "How many of you know,", "time": 585415}, {"__typename": "<PERSON><PERSON>", "text": "nobody wants you to propose\nmarriage on the first date?", "time": 586649}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Laughter)", "time": 589552}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "But too often,", "time": 592755}, {"__typename": "<PERSON><PERSON>", "text": "we expect our audiences\nto commit too soon.", "time": 593957}, {"__typename": "<PERSON><PERSON>", "text": "Well-placed stories slow down\nthe process just enough", "time": 598661}, {"__typename": "<PERSON><PERSON>", "text": "for you to build credibility and trust.", "time": 602065}, {"__typename": "<PERSON><PERSON>", "text": "It’s the principle that New York Times\nbest-selling author <PERSON>", "time": 605902}, {"__typename": "<PERSON><PERSON>", "text": "talks about in his book\n\"Jab, Jab, Jab, <PERSON> Hook:", "time": 609405}, {"__typename": "<PERSON><PERSON>", "text": "How to Tell Your Story\nin a Noisy Social World.\"", "time": 612275}, {"__typename": "<PERSON><PERSON>", "text": "The idea is to give before you ask", "time": 616112}, {"__typename": "<PERSON><PERSON>", "text": "and to give much more than you ask.", "time": 619282}, {"__typename": "<PERSON><PERSON>", "text": "Good stories position us to be givers\nbefore we expect to receive.", "time": 623453}, {"__typename": "<PERSON><PERSON>", "text": "Not only that, stories\nmake proposals irresistible", "time": 630460}, {"__typename": "<PERSON><PERSON>", "text": "because they allow us to build connection.", "time": 634063}, {"__typename": "<PERSON><PERSON>", "text": "Stories masterfully infuse a human\nelement into our businesses,", "time": 637934}, {"__typename": "<PERSON><PERSON>", "text": "our brands and our programs\nthat draws people in.", "time": 641371}, {"__typename": "<PERSON><PERSON>", "text": "So much so that by the time\nyou do go in for the ask,", "time": 645975}, {"__typename": "<PERSON><PERSON>", "text": "like any good proposal,", "time": 648912}, {"__typename": "<PERSON><PERSON>", "text": "it simply feels\nlike the next logical step.", "time": 650413}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "You see, stories are more than stories.", "time": 655251}, {"__typename": "<PERSON><PERSON>", "text": "They are the connective tissue", "time": 657854}, {"__typename": "<PERSON><PERSON>", "text": "that links you to the solution\nin people's minds,", "time": 659322}, {"__typename": "<PERSON><PERSON>", "text": "so that by the time you do present\nyour solution or propose your offer,", "time": 662125}, {"__typename": "<PERSON><PERSON>", "text": "they say, \"It's about time.", "time": 665528}, {"__typename": "<PERSON><PERSON>", "text": "I've been waiting for this.\"", "time": 668398}, {"__typename": "<PERSON><PERSON>", "text": "And once you've told your story", "time": 672068}, {"__typename": "<PERSON><PERSON>", "text": "with all the confidence\nin the world, ask, invite,", "time": 674103}, {"__typename": "<PERSON><PERSON>", "text": "propose, shout it from the rooftops.", "time": 677874}, {"__typename": "<PERSON><PERSON>", "text": "Because by that time,\nif you found the right people,", "time": 681544}, {"__typename": "<PERSON><PERSON>", "text": "the next step you're suggesting", "time": 685081}, {"__typename": "<PERSON><PERSON>", "text": "is something they'll want\njust as much as you do.", "time": 687050}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "And so,", "time": 692322}, {"__typename": "<PERSON><PERSON>", "text": "tell more stories.", "time": 694190}, {"__typename": "<PERSON><PERSON>", "text": "In fact, tell your stories.", "time": 697327}, {"__typename": "<PERSON><PERSON>", "text": "It might be the details\nsurrounding your greatest losses,", "time": 701164}, {"__typename": "<PERSON><PERSON>", "text": "your greatest wins", "time": 704500}, {"__typename": "<PERSON><PERSON>", "text": "or the context in which you’ve learned\nyour greatest life lessons", "time": 705702}, {"__typename": "<PERSON><PERSON>", "text": "that will paint the pictures,", "time": 710073}, {"__typename": "<PERSON><PERSON>", "text": "that will let people know,\nyou understand my problem.", "time": 712909}, {"__typename": "<PERSON><PERSON>", "text": "And you understand my pursuit.", "time": 717647}, {"__typename": "<PERSON><PERSON>", "text": "Absolutely, I would love\nto accept your proposal.", "time": 719249}, {"__typename": "<PERSON><PERSON>", "text": "And who knows, maybe your stories\nwill serve a bigger purpose.", "time": 723620}, {"__typename": "<PERSON><PERSON>", "text": "Maybe they'll help someone\nfind connection, community", "time": 727924}, {"__typename": "<PERSON><PERSON>", "text": "and a little bit of fun.", "time": 731227}, {"__typename": "<PERSON><PERSON>", "text": "Just like so many years ago,", "time": 732495}, {"__typename": "<PERSON><PERSON>", "text": "<PERSON>'s stories did for me.", "time": 734664}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "Thank you.", "time": 737700}]}, {"__typename": "Paragraph", "cues": [{"__typename": "<PERSON><PERSON>", "text": "(Applause)", "time": 738701}]}], "reviewer": null, "translator": null}, "video": {"__typename": "Video", "id": "128040", "talkExtras": {"__typename": "TalkExtras", "footnotes": [{"__typename": "Footnote", "annotation": "> \"<PERSON> writes, <PERSON><PERSON>, professor of psychology and neuroscience at Princeton University discovered that as you hear a story unfold, your brainwaves actually start to synchronize with those of the storyteller. The greater the listener's comprehension, the more closely the brainwave patterns mirrored each other.\"\r\n\r\nFor more information about Professor <PERSON><PERSON>’s study, see [here](https://www.npr.org/sections/health-shots/2020/04/11/815573198/how-stories-connect-and-persuade-us-unleashing-the-brain-power-of-narrative) and [here](https://www.pnas.org/doi/10.1073/pnas.**********).", "author": null, "category": "note", "date": null, "linkUrl": null, "source": null, "text": null, "timecode": "01:48", "title": null}, {"__typename": "Footnote", "annotation": "> “<PERSON>, a communications professor at the University of Buffalo, says that people are more likely to make changes to their lifestyle and health habits if they see a character they relate to making the same change.”\r\n\r\nFor more information about Professor <PERSON>'s study, see [here](https://www.npr.org/sections/health-shots/2020/04/11/815573198/how-stories-connect-and-persuade-us-unleashing-the-brain-power-of-narrative) and [here](https://www.ncbi.nlm.nih.gov/pmc/articles/PMC8933767/).", "author": null, "category": "note", "date": null, "linkUrl": null, "source": null, "text": null, "timecode": "02:26", "title": null}, {"__typename": "Footnote", "annotation": "> “On the other hand, we have <PERSON><PERSON>, a couple of years back was at a local outdoor music event. And at the end of the event, he invited his girlfriend <PERSON> to come up on stage with him.”\r\n\r\nNote: Pseudonyms have been used to protect the identities of the individuals featured in this story.", "author": null, "category": "note", "date": null, "linkUrl": null, "source": null, "text": null, "timecode": "07:26", "title": null}]}}}, "videoData": {"__typename": "Video", "audioInternalLanguageCode": "en", "commentsEnabled": true, "commentsLoggedInOnly": true, "curatorApproved": true, "customContentDetails": {"__typename": "CustomContentDetails", "partnerName": null}, "description": "\"Storytelling is one of the most powerful marketing and leadership tools there is,\" says communications expert <PERSON>. She explains how stories make proposals of all kinds more memorable — and shows how you can craft a compelling narrative to connect, persuade and drive meaningful action.", "duration": 743, "featured": true, "hasTranslations": true, "id": "128040", "internalLanguageCode": "en", "language": "en", "partnerName": null, "playerData": "{\"id\":\"128040\",\"mediaIdentifier\":\"consus-pm9111-im2346\",\"mediaProjectVersionIdentifier\":\"consus-pm9111-im2346\",\"duration\":739,\"languages\":[{\"languageName\":\"English\",\"endonym\":\"English\",\"languageCode\":\"en\",\"ianaCode\":\"en\",\"isRtl\":false},{\"languageName\":\"Spanish\",\"endonym\":\"Español\",\"languageCode\":\"es\",\"ianaCode\":\"es\",\"isRtl\":false},{\"languageName\":\"Arabic\",\"endonym\":\"العربية\",\"languageCode\":\"ar\",\"ianaCode\":\"ar\",\"isRtl\":true}],\"nativeLanguage\":\"en\",\"isSubtitleRequired\":false,\"resources\":{\"h264\":[{\"bitrate\":1200,\"file\":\"https://py.tedcdn.com/consus/projects/00/69/92/001/products/2022x-kelly-parker-001-fallback-2fc6841b-71f6-429b-9eb6-e98ad0759caf-1200k.mp4\"}],\"hls\":{\"adUrl\":\"https://pubads.g.doubleclick.net/gampad/ads?ciu_szs=300x250%2C512x288%2C120x60%2C320x50%2C6x7%2C6x8\\u0026correlator=%5Bcorrelator%5D\\u0026cust_params=event%3DTEDxBalchStreet%26id%3D128040%26tag%3Dbusiness%2Cstorytelling%2Ccommunication%2Cmarketing%2CTEDx%26talk%3Dkelly_d_parker_the_art_of_persuasive_storytelling%26year%3D2022\\u0026env=vp\\u0026gdfp_req=1\\u0026impl=s\\u0026iu=%2F5641%2Fmobile%2Fios%2Fweb\\u0026output=xml_vast2\\u0026sz=640x360\\u0026unviewed_position_start=1\\u0026url=%5Breferrer%5D\",\"maiTargeting\":{\"id\":\"128040\",\"talk\":\"kelly_d_parker_the_art_of_persuasive_storytelling\",\"tag\":\"business,storytelling,communication,marketing,TEDx\",\"year\":\"2022\",\"event\":\"TEDxBalchStreet\"},\"stream\":\"https://hls.ted.com/project_masters/9111/manifest.m3u8?intro_master_id=2346\",\"metadata\":\"https://hls.ted.com/project_masters/9111/metadata.json?intro_master_id=2346\"}},\"targeting\":{\"id\":\"128040\",\"talk\":\"kelly_d_parker_the_art_of_persuasive_storytelling\",\"tag\":\"business,storytelling,communication,marketing,TEDx\",\"year\":\"2022\",\"event\":\"TEDxBalchStreet\"},\"canonical\":\"https://www.ted.com/talks/kelly_d_parker_the_art_of_persuasive_storytelling\",\"name\":\"Kelly D. Parker: The art of persuasive storytelling\",\"title\":\"The art of persuasive storytelling\",\"speaker\":\"Kelly D. Parker\",\"thumb\":\"https://pi.tedcdn.com/r/talkstar-photos.s3.amazonaws.com/uploads/ca75cf3b-aa42-4f65-90f0-c06564f04c7f/KellyParker_2022X-embed.jpg?quality=89\\u0026w=600\",\"slug\":\"kelly_d_parker_the_art_of_persuasive_storytelling\",\"event\":\"TEDxBalchStreet\",\"published\":1713883651,\"external\":{\"service\":\"YouTube\",\"code\":\"Oo2upU6ny-I\",\"duration\":0.0,\"start_time\":0.0}}", "presenterDisplayName": "<PERSON>", "primaryImageSet": [{"__typename": "PhotoSize", "aspectRatioName": "16x9", "url": "https://talkstar-photos.s3.amazonaws.com/uploads/ca75cf3b-aa42-4f65-90f0-c06564f04c7f/KellyParker_2022X-embed.jpg"}, {"__typename": "PhotoSize", "aspectRatioName": "4x3", "url": "https://talkstar-photos.s3.amazonaws.com/uploads/f0f030cb-ab7b-4cb5-98e8-2a0bc2f913e1/KellyParker_2022X-stageshot.jpg"}, {"__typename": "PhotoSize", "aspectRatioName": "2x1", "url": "https://talkstar-photos.s3.amazonaws.com/uploads/2839a6c9-78e6-46c2-845f-b310c23209e8/KellyParker_2022X-1350x675.jpg"}], "publishedAt": "2024-04-23T14:47:31Z", "recordedOn": "2022-03-05", "relatedVideos": [{"__typename": "Video", "id": "70380", "slug": "karen_eber_how_your_brain_responds_to_stories_and_why_they_re_crucial_for_leaders"}, {"__typename": "Video", "id": "41395", "slug": "america_ferrera_my_identity_is_a_superpower_not_an_obstacle"}, {"__typename": "Video", "id": "126841", "slug": "anthony_tan_and_amane_dann<PERSON><PERSON>_want_to_succeed_in_business_find_a_problem_to_solve"}, {"__typename": "Video", "id": "1282", "slug": "joe_sabia_the_technology_of_storytelling"}, {"__typename": "Video", "id": "1379", "slug": "andrew_stanton_the_clues_to_a_great_story"}, {"__typename": "Video", "id": "1841", "slug": "and<PERSON>_fitz<PERSON><PERSON>_adventures_in_twitter_fiction"}], "slug": "kelly_d_parker_the_art_of_persuasive_storytelling", "socialDescription": "\"Storytelling is one of the most powerful marketing and leadership tools there is,\" says communications expert <PERSON>. She explains how stories make proposals of all kinds more memorable — and shows how you can craft a compelling narrative to connect, persuade and drive meaningful action.", "socialTitle": "The art of persuasive storytelling", "speakers": {"__typename": "AcmeSpeakerConnection", "nodes": [{"__typename": "AcmeSpeaker", "description": "Storytelling strategist", "firstname": "<PERSON>", "isLive": true, "lastname": "<PERSON>", "middlename": "", "photoUrl": "https://ted-conferences-speaker-photos-production.s3.amazonaws.com/n18cecs2c8lr28q1eujlf3am2z2c", "slug": "kelly_d_parker", "title": "", "whatOthersSay": "", "whoTheyAre": "<PERSON> is a seasoned storyteller and marketing professional who believes in the transformative power of narratives.", "whyListen": "<div>With nearly 20 years of experience, <PERSON> has trained professionals worldwide and guided organizations like Abbvie, Microsoft and Excelon in mastering the art of storytelling. Drawing from her tenure at global enterprises such as Sherwin-Williams and American Greetings, <PERSON> understands how stories can fuel momentum, foster community and drive impactful change.<br><br><PERSON> is the host of the <em>Business Storytelling Made Easy Podcast</em>, where she interviews industry experts and shares practical tips to help listeners craft and communicate unforgettable stories. A <em>Fast Company</em> contributor and sought-after keynote speaker, <PERSON> emphasizes the ability of a well-crafted story to drive profits, reshape cultures and rejuvenate brand identities. With a passion for leveraging storytelling as a universal language, <PERSON> continues to inspire and empower others to harness the narrative potential within every aspect of life and business.</div>"}]}, "talkExtras": {"__typename": "TalkExtras", "learnModules": [], "recommendations": [], "takeAction": [{"__typename": "TakeActionModule", "blurb": "**Visit** <PERSON>'s website to learn more. ", "endAt": null, "eyebrow": null, "linkUrl": "https://www.kellydparker.com/", "published": true, "startAt": null, "status": "APPROVED", "verb": "join", "visibleUrl": "kellydparker.com"}]}, "title": "The art of persuasive storytelling", "topics": {"__typename": "TopicConnection", "nodes": [{"__typename": "Topic", "id": "18", "name": "business", "slug": "business"}, {"__typename": "Topic", "id": "84", "name": "marketing", "slug": "marketing"}, {"__typename": "Topic", "id": "85", "name": "storytelling", "slug": "storytelling"}, {"__typename": "Topic", "id": "86", "name": "communication", "slug": "communication"}, {"__typename": "Topic", "id": "273", "name": "TEDx", "slug": "tedx"}]}, "type": {"__typename": "TypeOfVideo", "id": "2", "name": "TEDx Talk"}, "videoContext": "TEDxBalchStreet", "viewedCount": 477749}}, "responseCode": 200}, "query": {"slug": ["kelly_d_parker_the_art_of_persuasive_storytelling"]}, "runtimeConfig": {"RECAPTCHA_KEY": "6Leu7SEbAAAAAJ62yHnNX_CyMiNGTrdhvp9roeEC", "STRIPE_PUBLISHABLE_KEY": "pk_live_2E0Im9ZSYd61pq9NRBXWa445", "SUPERCAST_TOKEN": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "scriptLoader": []}