用 performance_schema： 在某些 MySQL 版本中，可以通过 performance_schema 来获取锁和事务的信息。确保 performance_schema 已启用，然后可以运行以下查询：

SELECT * FROM performance_schema.data_locks;

这将列出当前的锁信息，包括持有锁的线程和相关事务


在 performance_schema.data_locks 表中，你可以获取有关当前锁的信息。要理解锁的关系，通常需要查看表中的几个关键字段。以下是一些重要字段及其解释：

重要字段
OBJECT_TYPE：锁的对象类型（如 TABLE 或 ROW）。
OBJECT_SCHEMA：锁定对象所在的数据库。
OBJECT_NAME：锁定对象的名称（如表名）。
LOCK_TYPE：锁的类型，例如 X（排他锁）、S（共享锁）。
LOCK_MODE：当前锁的模式。
LOCK_OWNER_THREAD_ID：持有锁的线程 ID。
LOCK_OWNER_EVENT_ID：持有锁的事件 ID。
LOCK_REQUEST_THREAD_ID：请求锁的线程 ID。
LOCK_REQUEST_EVENT_ID：请求锁的事件 ID。

