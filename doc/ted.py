import requests  # getting content of the TED Talk page

from bs4 import BeautifulSoup  # web scraping

import re  # Regular Expression pattern matching

import json  # json processing

# url = 'https://www.ted.com/talks/amy_cuddy_your_body_language_may_shape_who_you_are'
url = 'https://www.ted.com/talks/sir_ken_robinson_do_schools_kill_creativity?'
# url = 'https://www.ted.com/talks/morgan_spurlock_the_greatest_ted_talk_ever_sold'
r = requests.get(url)
# with open('ted_talk_content.html', 'wb') as file:
#     file.write(r.content)
soup = BeautifulSoup(r.content, "html.parser")
next_data_script = soup.find(id="__NEXT_DATA__")
data_json = next_data_script.string
# with open('1.json', 'w') as file:
#     file.write(data_json)

video_data = json.loads(data_json)[
    'props']['pageProps']['videoData']

player_data = json.loads(video_data['playerData'])
mp4_url = player_data['resources']['h264'][0]['file']
m3u8_url = player_data['resources']['hls']['stream']
metadata = player_data['resources']['hls']['metadata']
subtitles = json.loads(requests.get(metadata).content)['subtitles']
ted = {
    "id": video_data['id'],
    "shortenedUrl": json.loads(data_json)['props']['pageProps']['shortenedUrl'],
    "playerUrl": mp4_url,
    "presenterDisplayName": video_data['presenterDisplayName'],
    "title": video_data['title'],
    "socialTitle": video_data['socialTitle'],
    "publishedAt": video_data['publishedAt'],
    "duration": video_data['duration'],
    "viewedCount": video_data['viewedCount'],
    "canonical": player_data['canonical'],
    "thumb": player_data['thumb'],
    "slug": player_data['slug'],
    "m3u8": m3u8_url,
    "metadata": metadata,
    "subtitles": subtitles,
    "languages": player_data['languages'],
}
json_string = json.dumps(ted)
print(json_string)
