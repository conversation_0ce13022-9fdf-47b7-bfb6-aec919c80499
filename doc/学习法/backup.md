情境优先原则：完全摒弃传统语法教学，构建「场景-语音-肌肉记忆」三位一体学习模型

百遍法则系统：将100遍训练拆解为「认知→解析→掌控→自动化」四阶段进化路径

反枯燥机制：通过「内容变异+成就解锁+社交激励」构建可持续学习生态


学习工具与辅助功能
​多模态训练：
​视频模式：看画面+字幕跟读。
​纯音频模式：仅播放音频，适用于通勤/家务场景。
​进度追踪：
​遍数计数器：记录每个片段完成次数（目标100遍）。
​成就系统：解锁“10遍小成”“50遍达人”等徽章，分享至社区。
​自适应推荐：
根据用户水平（CEFR A1-C2）推荐影视资源（如初学者推荐《老友记》，高级用户推荐《纸牌屋》）


用户留存与激励
​学习社群：
用户可上传跟读录音，获得社区点赞/评论。
组队挑战：3人小队共同完成一部电影100遍打卡。
​AI学习教练：
定期生成学习报告（如“本周你的元音发音进步15%”）。
推送激励语录（如“已坚持7天，离脱口而出只差93遍！”）。