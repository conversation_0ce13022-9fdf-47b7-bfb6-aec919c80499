根据你的需求，我会将后端开发分为核心模块和辅助模块，并给出优先级排序、技术实现建议以及开发路线图。以下是详细的开发计划：

---

### **一、开发优先级排序（从高到低）**
#### **1. 基础框架搭建 (Week 1-2)**
- **核心目标**：搭建可扩展的后端架构
  - 初始化 Golang 项目（模块化分层：router/controller/service/repository）
  - 配置数据库连接（MySQL + GORM 框架）
  - 配置日志系统（Zap/Slog）
  - 实现全局错误处理与响应格式标准化
  - 集成配置文件管理（Viper）
  - **安全防护**：HTTPS、SQL 注入防护、请求频率限制

#### **2. 用户系统 (Week 3-4)**
- **核心功能**：
  - 手机号登录/注册（短信验证码集成）
  - 账号绑定（邮箱、第三方登录）
  - 用户信息管理（头像、昵称）
  - 账号注销（软删除）
- **数据库表设计**：
  ```sql
  CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(20) UNIQUE,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(100),
    status ENUM('active', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP
  );
  ```
- **技术细节**：
  - 使用 Redis 缓存短信验证码（临时存储，5分钟过期）
  - JWT Token 实现无状态认证

#### **3. 视频与字幕管理 (Week 5-6)**
- **核心功能**：
  - 视频元数据管理（标题、描述、多语言字幕关联）
  - 字幕增删改查（支持用户自定义字幕）
  - 视频文件上传/下载（对接云存储）
- **数据库表设计**：
  ```sql
  CREATE TABLE videos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255),
    storage_path VARCHAR(255), -- 云存储路径
    duration INT
  );

  CREATE TABLE subtitles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    video_id INT,
    language VARCHAR(10),
    content TEXT,
    is_user_modified BOOLEAN DEFAULT false,
    FOREIGN KEY (video_id) REFERENCES videos(id)
  );
  ```
- **技术细节**：
  - 使用阿里云 OSS 或 AWS S3 存储视频文件
  - 字幕版本控制（保留用户修改历史）

#### **4. AI 交互模块 (Week 7)**
- **核心功能**：
  - 封装 DeepSeek API 的代理接口
  - 句子级 AI 问答上下文管理
  - 录音文件转文字（可选集成 ASR 服务）
- **接口设计示例**：
  ```go
  // POST /api/v1/ai/ask
  type AskRequest struct {
    Sentence string `json:"sentence"`
    UserID   int    `json:"user_id"`
  }
  ```
- **技术细节**：
  - 限流控制（避免 API 滥用）
  - 异步任务队列（Celery/RabbitMQ，处理长时间 AI 请求）

#### **5. 学习数据统计 (Week 8)**
- **核心功能**：
  - 学习记录埋点（视频观看时长、打卡）
  - 每日学习报告生成
  - 可视化数据接口（折线图、柱状图）
- **数据库表设计**：
  ```sql
  CREATE TABLE study_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    video_id INT,
    duration INT,
    created_at DATE
  );
  ```
- **技术细节**：
  - 使用 Redis HyperLogLog 统计 DAU/MAU
  - 定时任务（统计每日聚合数据）

#### **6. 会员与订阅 (Week 9)**
- **核心功能**：
  - 苹果订阅状态回调处理
  - 会员权益控制（视频权限、AI调用次数）
- **数据库表设计**：
  ```sql
  CREATE TABLE subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    apple_receipt TEXT,
    expires_at TIMESTAMP
  );
  ```
- **技术细节**：
  - 使用 Apple Server-to-Server Notification 验证订阅
  - 权益中间件（拦截非会员请求）

#### **7. 辅助功能 (Week 10)**
- 本地视频导入（文件分片上传）
- 习惯设置（每日学习目标提醒）
- 系统设置（多语言、通知开关）

---

### **二、技术栈扩展建议**
1. **Redis 使用场景**：
   - 短信验证码缓存
   - 每日用户活跃数据统计（HyperLogLog）
   - JWT Token 黑名单（退出登录时禁用 Token）
2. **消息队列（RabbitMQ/Kafka）**：
   - 异步处理 AI 请求
   - 批量发送通知（如学习提醒）
3. **Elasticsearch（可选）**：
   - 实现视频内容的全文搜索

---

### **三、关键依赖关系**
1. **用户系统** 是几乎所有模块的前置条件
2. **视频管理** 是学习功能的基础
3. **AI 模块** 依赖用户身份验证和视频句子上下文
4. **订阅系统** 依赖用户系统和支付回调处理

---

### **四、推荐开发顺序**
```mermaid
graph TD
  A[基础框架] --> B[用户系统]
  B --> C[视频与字幕]
  C --> D[AI 模块]
  C --> E[学习数据]
  B --> F[订阅系统]
  E --> G[可视化报表]
  F --> G
```

---

### **五、注意事项**
1. **API 文档**：使用 Swagger 或 Postman 同步编写，便于后续联调
2. **压力测试**：重点测试视频流传输和 AI 接口的并发性能
3. **合规性**：用户数据加密存储（如手机号）、遵守 GDPR 和苹果审核规则

建议优先完成用户系统和视频管理的核心接口（登录、视频列表、字幕编辑），即使 UI 未完成，也可通过 Postman 测试逻辑。其他模块可按需并行开发。