package middleware

import (
	"loop/pkg/enum"
	"loop/pkg/i18n"
	"loop/pkg/jwtx"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

func AdminCheck(jwtSecret string) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.Request.Header.Get("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenIsEmpty),
			})
			c.Abort()
			return
		}

		claims, err := jwtx.ParseAdminToken(token, jwtSecret)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenParse),
			})
			c.Abort()
			return
		} else if time.Now().Unix() > claims.ExpiresAt {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenExpire),
			})
			c.Abort()
			return
		}
		if claims.RoleLevel != int(enum.AdminLevel) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrUnauthorized),
			})
			c.Abort()
			return
		}
		c.Next()
	}
}
