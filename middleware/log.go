package middleware

import (
	"bytes"
	"fmt"
	"io"
	"math"
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

var timeFormat = "02/Jan/2006:15:04:05 -0700"

// 日志记录到文件
func LoggerToFile(notLogged ...string) gin.HandlerFunc {
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknow"
	}

	var skip map[string]struct{}

	if length := len(notLogged); length > 0 {
		skip = make(map[string]struct{}, length)

		for _, p := range notLogged {
			skip[p] = struct{}{}
		}
	}
	return func(c *gin.Context) {
		// other handler can change c.Path so:
		path := c.Request.URL.Path
		start := time.Now()

		// 获取请求参数
		queryParams := c.Request.URL.Query()
		formParams := c.Request.PostForm
		// 读取请求的 JSON body
		var requestBody []byte
		if c.Request.Method == http.MethodPost || c.Request.Method == http.MethodPut {
			contentType := c.Request.Header.Get("Content-Type")
			if contentType == "application/json" {
				bodyBytes, err := io.ReadAll(c.Request.Body)
				if err == nil {
					requestBody = bodyBytes
					c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
				}
			}
		}

		clientIP := c.ClientIP()
		clientUserAgent := c.Request.UserAgent()
		referer := c.Request.Referer()

		if _, ok := skip[path]; ok {
			c.Next()
			return
		}

		// 在请求开始时打印访问日志
		requestMsg := fmt.Sprintf("Gin Request Start %s - %s [%s] \"%s %s\" \"%s\" \"%s\" query=%v form=%v requestBody=%v",
			clientIP, hostname, time.Now().Format(timeFormat), c.Request.Method, path,
			referer, clientUserAgent, queryParams, formParams, string(requestBody))
		logrus.Info(requestMsg)

		// 执行后续中间件和处理器
		c.Next()

		// 在请求结束时打印响应日志
		stop := time.Since(start)
		latency := int(math.Ceil(float64(stop.Nanoseconds()) / 1000000.0))
		statusCode := c.Writer.Status()
		dataLength := c.Writer.Size()
		if dataLength < 0 {
			dataLength = 0
		}

		entry := logrus.WithFields(logrus.Fields{
			"hostname":    hostname,
			"statusCode":  statusCode,
			"latency":     latency, // time to process
			"clientIP":    clientIP,
			"method":      c.Request.Method,
			"path":        path,
			"referer":     referer,
			"dataLength":  dataLength,
			"userAgent":   clientUserAgent,
			"query":       queryParams,
			"form":        formParams,
			"requestBody": string(requestBody),
		})

		if len(c.Errors) > 0 {
			entry.Error(c.Errors.ByType(gin.ErrorTypePrivate).String())
		} else {
			responseMsg := fmt.Sprintf("Gin Response End %s - %s [%s] \"%s %s\" %d %d \"%s\" \"%s\" (%dms)",
				clientIP, hostname, time.Now().Format(timeFormat), c.Request.Method, path, statusCode,
				dataLength, referer, clientUserAgent, latency)
			if statusCode >= http.StatusInternalServerError {
				logrus.Error(responseMsg)
			} else if statusCode >= http.StatusBadRequest {
				logrus.Warn(responseMsg)
			} else {
				logrus.Info(responseMsg)
			}
		}
	}
}
