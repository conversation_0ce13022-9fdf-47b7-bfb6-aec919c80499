package middleware

import (
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// Cors 跨域配置
func Cors() gin.HandlerFunc {
	config := cors.DefaultConfig()
	config.AllowMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"}
	config.AllowHeaders = []string{
		"Origin", "Content-Length", "Content-Type", "Cookie", "X-CSRF-TOKEN", "Accept",
		"Authorization", "X-XSRF-TOKEN", "Access-Control-Allow-Origin"}
	config.ExposeHeaders = append(config.ExposeHeaders, "Authorization")
	config.ExposeHeaders = append(config.ExposeHeaders, "authenticated")
	// if gin.Mode() == gin.ReleaseMode {
	// 	// 生产环境需要配置跨域域名，否则403
	// 	config.AllowOrigins = []string{"http://www.example.com"}
	// } else {
	// 	// 测试环境下模糊匹配本地开头的请求
	// 	config.AllowOriginFunc = func(origin string) bool {
	// 		if regexp.MustCompile(`^http://127\.0\.0\.1:\d+$`).MatchString(origin) {
	// 			return true
	// 		}
	// 		if regexp.MustCompile(`^http://localhost:\d+$`).MatchString(origin) {
	// 			return true
	// 		}
	// 		return true
	// 	}
	// }
	config.AllowAllOrigins = true
	config.AllowCredentials = true
	return cors.New(config)
}
