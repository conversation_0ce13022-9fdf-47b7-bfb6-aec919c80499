package middleware

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sony/gobreaker"
)

// 创建熔断器
var cb = gobreaker.NewCircuitBreaker(gobreaker.Settings{
	Name:        "HTTPRequestCircuitBreaker",
	MaxRequests: 3,                // 半开状态期最大允许放行请求：即进入Half-Open状态时，一个时间周期内允许最大同时请求数（如果还达不到切回closed状态条件，则不能再放行请求）。
	Interval:    15 * time.Second, // closed状态时，重置计数的时间周期；如果配为0，切入Open后永不切回Closed--有点暴力。
	Timeout:     30 * time.Second, // 进入Open状态后，多长时间会自动切成 Half-open，默认60s，不能配为0
	// ReadyToTrip回调函数：进入Open状态的条件，比如默认是连接5次出错，即进入Open状态，即可对熔断条件进行配置。在fail计数发生后，回调一次。
	ReadyToTrip: func(counts gobreaker.Counts) bool {
		// 总请求数不足时不触发熔断（避免低流量误判）
		if counts.Requests < 10 {
			return false
		}

		// 计算总错误率（失败数 / 总请求数）
		failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
		return failureRatio > 0.6 // 错误率超过60%触发熔断
	},
})

// 熔断器中间件
func CircuitBreakerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 在熔断器内执行核心业务逻辑
		_, err := cb.Execute(func() (interface{}, error) {
			// 创建一个channel处理异步错误
			errChan := make(chan error, 1)

			// 使用Gin的Copy避免上下文污染
			cp := c.Copy()
			go func() {
				defer close(errChan)
				// 执行后续中间件和业务逻辑
				cp.Next()

				// 业务逻辑完成后检测错误
				if cp.Writer.Status() >= http.StatusInternalServerError {
					errChan <- fmt.Errorf("backend error: %d", cp.Writer.Status())
				} else {
					errChan <- nil
				}
			}()

			// 等待业务处理完成或超时
			select {
			case <-cp.Request.Context().Done():
				return nil, cp.Request.Context().Err()
			case err := <-errChan:
				return nil, err
			}
		})

		if err != nil {
			c.AbortWithStatusJSON(http.StatusServiceUnavailable, gin.H{
				"code": http.StatusServiceUnavailable,
				"msg":  "服务暂时不可用，请稍后重试",
			})
		}
	}
}
