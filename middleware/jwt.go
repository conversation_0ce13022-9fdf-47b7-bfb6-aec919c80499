package middleware

import (
	"context"
	"loop/internal/client"
	"loop/internal/constants"
	"loop/pkg/i18n"
	"loop/pkg/jwtx"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

func JWT(jwtSecret string, client *client.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.Request.Header.Get("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenIsEmpty),
			})
			c.Abort()
			return
		}

		// 先验证 JWT token
		claims, err := jwtx.ParseToken(token, jwtSecret)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenParse),
			})
			c.Abort()
			return
		} else if time.Now().Unix() > claims.ExpiresAt {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenExpire),
			})
			c.Abort()
			return
		}

		// 再验证 Redis 中存储的 token
		storedToken, err := client.RedisClient.Get(context.Background(), constants.UserTokenPrefix+claims.Uid).Result()
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenNotFound),
			})
			c.Abort()
			return
		}

		// 验证当前 token 是否与 Redis 中存储的一致
		if storedToken != token {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenMismatch),
			})
			c.Abort()
			return
		}

		if claims.Status == 1 {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrUnauthorized),
			})
			c.Abort()
			return
		}
		if claims.Uid == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrUnauthorized),
			})
			c.Abort()
			return
		}
		c.Set("uid", claims.Uid)
		c.Next()
	}
}
